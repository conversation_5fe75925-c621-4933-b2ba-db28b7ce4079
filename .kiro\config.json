{"version": "1.0.0", "project": "Pennfly Private Academy", "hooks": {"enabled": true, "autoLoad": true, "hooks": [{"id": "docs-sync-hook", "file": "hooks/docs-sync-hook.kiro.hook", "enabled": true}, {"id": "content-validator", "file": "hooks/content-validator.kiro.hook", "enabled": true}, {"id": "component-optimizer", "file": "hooks/component-optimizer.kiro.hook", "enabled": true}, {"id": "seo-optimizer", "file": "hooks/seo-optimizer.kiro.hook", "enabled": true}, {"id": "build-analyzer", "file": "hooks/build-analyzer.kiro.hook", "enabled": true}, {"id": "accessibility-checker", "file": "hooks/accessibility-checker.kiro.hook", "enabled": true}, {"id": "content-creator", "file": "hooks/content-creator.kiro.hook", "enabled": true}]}, "steering": {"enabled": true, "autoLoad": true, "files": [{"id": "project-context", "file": "steering/project-context.md", "inclusion": "always"}, {"id": "development-standards", "file": "steering/development-standards.md", "inclusion": "fileMatch", "pattern": "mad-magnitude/src/**/*"}, {"id": "content-creation", "file": "steering/content-creation.md", "inclusion": "manual"}, {"id": "current-priorities", "file": "steering/current-priorities.md", "inclusion": "always"}]}}