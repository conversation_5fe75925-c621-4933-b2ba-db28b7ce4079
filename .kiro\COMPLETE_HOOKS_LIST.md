# 完整的 Agent Hooks 列表

## 🎯 总览

现在你的项目配置了 **7 个专业的 Agent Hooks**，涵盖开发、测试、部署的各个环节。

## 🔄 自动触发的 Hooks（保存文件时自动运行）

### 1. 文档同步器 (`docs-sync-hook`)

- **触发**: 保存项目文件时
- **功能**: 自动更新项目文档和 README
- **适用**: 代码或配置变更时

### 2. 内容验证器 (`content-validator`)

- **触发**: 保存 `mad-magnitude/src/content/**/*.md` 文件
- **功能**: 检查内容格式、frontmatter、标签、语法
- **适用**: 创建博客文章、研究报告时

### 3. 组件优化器 (`component-optimizer`)

- **触发**: 保存 `mad-magnitude/src/components/**/*.astro` 文件
- **功能**: 检查 TypeScript 类型、性能、可访问性
- **适用**: 开发 Astro 组件时

### 4. SEO 优化器 (`seo-optimizer`)

- **触发**: 保存 `mad-magnitude/src/pages/**/*.astro` 文件
- **功能**: 检查页面 SEO 设置、元标签、结构
- **适用**: 创建或修改页面时

## 🔧 手动触发的 Hooks（点击按钮运行）

### 5. 构建分析器 (`build-analyzer`)

- **按钮**: "分析构建结果"
- **功能**: 分析构建性能和优化建议
- **适用**: 发布前性能检查

### 6. 可访问性检查器 (`accessibility-checker`)

- **按钮**: "检查可访问性"
- **功能**: 全面检查 WCAG 合规性
- **适用**: 确保网站无障碍访问

### 7. 内容创建助手 (`content-creator`)

- **按钮**: "创建新内容"
- **功能**: 帮助创建符合规范的新内容
- **适用**: 需要创建新文章或内容时

## 🚀 推荐使用流程

### 日常开发流程

1. **编写代码** → 保存文件 → **自动 hooks 检查** → 修复问题
2. **文档自动更新** → 文档同步器自动运行
3. **定期运行** 手动 hooks 检查整体状况

### 功能开发流程

1. **开始开发** → 使用 "内容创建助手" 生成模板
2. **编写组件** → 自动触发 "组件优化器"
3. **创建页面** → 自动触发 "SEO 优化器"
4. **功能完成** → 运行 "构建分析器" 验证性能

### 发布前检查流程

1. **性能检查** → 运行 "构建分析器"
2. **质量检查** → 运行 "可访问性检查器"
3. **文档检查** → 确认文档同步器已更新文档

## 🎯 Hook 分类

### 📝 内容相关

- 内容验证器
- 内容创建助手
- SEO 优化器

### 🔧 代码质量

- 组件优化器
- 构建分析器

### 🛡️ 质量保证

- 可访问性检查器

### 📚 文档维护

- 文档同步器

## 💡 使用技巧

1. **自动触发的 hooks** 会在你保存文件时弹出，建议都运行一下
2. **手动触发的 hooks** 建议定期运行，特别是发布前
3. **文档同步器** 会自动保持文档最新，无需手动维护
4. **性能优化** 在开发中期和发布前运行构建分析器

## 🔄 Hook 状态管理

所有 hooks 默认都是启用状态。如果需要禁用某个 hook：

1. 编辑对应的 `.kiro.hook` 文件
2. 设置 `"enabled": false`
3. 重启 Kiro IDE

## 📊 当前项目状态

基于项目检查，建议优先运行：

1. **构建分析器** - 检查构建性能
2. **可访问性检查器** - 确保合规性
3. **内容创建助手** - 创建新内容时使用

现在你有了一套完整的开发助手工具！🎉
