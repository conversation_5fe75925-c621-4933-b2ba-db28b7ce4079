# 内容架构设计文档（简化版）

## 概述

基于简洁化原则，重新设计内容架构。专注于**静态内容的清晰组织和展示**，避免复杂的内容管理功能。

## 设计原则

### 1. 内容优先

- 专注于内容的创作和展示
- 简化内容的组织结构
- 避免复杂的元数据管理

### 2. 结构清晰

- 使用直观的目录结构
- 保持内容分类的简单性
- 避免过度的内容关联

### 3. 维护简单

- 减少配置的复杂性
- 使用标准的 Markdown 格式
- 避免复杂的内容处理流程

## 简化的内容架构

### 内容目录结构

```
src/content/
├── news/           # 动态资讯
├── logs/           # 研究日志
├── economics/      # 经济研究所
├── philosophy/     # 哲学研究所
├── internet/       # 互联网研究所
├── ai/             # AI研究所
├── future/         # 未来研究所
└── products/       # 产品发布
```

### 简化的内容模型

**基础内容字段**：

```yaml
---
title:
  zh: "中文标题"
  en: "English Title" # 可选
description:
  zh: "中文描述"
  en: "English Description" # 可选
publishDate: 2025-01-15
updateDate: 2025-01-15 # 可选
draft: false
featured: false # 可选
tags: ["标签1", "标签2"]
author: "Pennfly"
---
```

**删除的复杂字段**：

- ❌ 复杂的分类层级
- ❌ 内容关联字段
- ❌ 推荐权重
- ❌ 阅读统计
- ❌ 版本信息
- ❌ 协作者信息

### 内容展示页面

**研究所页面**：

- 简单的文章列表
- 基本的分页功能
- 简单的标签筛选

**文章详情页**：

- 清晰的文章内容
- 基本的元数据显示
- 简单的面包屑导航

**首页**：

- 各研究所的最新文章
- 特色内容展示
- 简单的导航入口

## 技术实现

### Content Collections 配置（简化）

```typescript
// src/content/config.ts
const baseSchema = z.object({
  title: z.object({
    zh: z.string(),
    en: z.string().optional(),
  }),
  description: z.object({
    zh: z.string(),
    en: z.string().optional(),
  }),
  publishDate: z.date(),
  updateDate: z.date().optional(),
  draft: z.boolean().default(false),
  featured: z.boolean().default(false),
  tags: z.array(z.string()).default([]),
  author: z.string().default("Pennfly"),
});

export const collections = {
  news: defineCollection({ schema: baseSchema }),
  logs: defineCollection({ schema: baseSchema }),
  economics: defineCollection({ schema: baseSchema }),
  philosophy: defineCollection({ schema: baseSchema }),
  internet: defineCollection({ schema: baseSchema }),
  ai: defineCollection({ schema: baseSchema }),
  future: defineCollection({ schema: baseSchema }),
  products: defineCollection({ schema: baseSchema }),
};
```

### 删除的复杂功能

- ❌ 复杂的内容验证
- ❌ 多语言路由生成
- ❌ 内容关联处理
- ❌ 动态内容生成
- ❌ 内容统计分析

## 搜索和筛选（简化）

### 基础搜索功能

- 使用现有的 Fuse.js
- 简单的关键词匹配
- 基本的结果展示

### 简单的筛选功能

- 按研究所筛选
- 按标签筛选
- 按时间排序

### 删除的复杂功能

- ❌ 高级搜索语法
- ❌ 搜索结果排序算法
- ❌ 搜索历史记录
- ❌ 智能搜索建议

## 性能优化

### 已有的优化

- 静态站点生成
- 图片优化
- CSS 和 JS 压缩

### 简单的额外优化

- 基本的懒加载
- 简单的缓存策略
- 图片格式优化

### 不需要的复杂优化

- ❌ 复杂的缓存策略
- ❌ CDN 配置
- ❌ 性能监控
- ❌ 实时分析

## 维护策略

### 内容管理

- 直接编辑 Markdown 文件
- 使用 Git 进行版本控制
- 简单的文件组织

### 部署流程

- 自动构建和部署
- 基本的错误检查
- 简单的备份策略

### 不需要的复杂功能

- ❌ 内容管理后台
- ❌ 在线编辑器
- ❌ 协作工具
- ❌ 审核流程
