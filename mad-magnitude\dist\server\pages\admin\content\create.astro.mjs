import { a as createComponent, h as renderComponent, d as renderTemplate, m as maybeRenderHead } from "../../../assets/vendor-astro.kctgsZae.js";
import { i } from "../../../assets/vendor-astro.kctgsZae.js";
import "kleur/colors";
import { $ as $$ContentEditor } from "../../../assets/admin.2RoO5hzn.js";
import { $ as $$Layout } from "../../../assets/Layout.BKd1ZXhO.js";
/* empty css                                       */
const $$Create = createComponent(($$result, $$props, $$slots) => {
  const title = "创建内容 - Pennfly Private Academy";
  const description = "创建新的学院内容";
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": title, "description": description, "data-astro-cid-in5issv3": true }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<main class="create-content-page" data-astro-cid-in5issv3> ${renderComponent($$result2, "ContentEditor", $$ContentEditor, { "mode": "create", "data-astro-cid-in5issv3": true })} </main> ` })} `;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/admin/content/create.astro", void 0);
const $$file = "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/admin/content/create.astro";
const $$url = "/admin/content/create";
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({ __proto__: null, default: $$Create, file: $$file, url: $$url }, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  i as renderers
};
