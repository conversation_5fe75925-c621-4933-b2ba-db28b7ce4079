#!/usr/bin/env node

/**
 * 动态资讯创建脚本
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const CONTENT_DIR = path.join(__dirname, '../src/content');
const TEMPLATE_PATH = path.join(CONTENT_DIR, 'templates/news-template.md');

// 动态类型配置
const NEWS_TYPES = {
  research: { label: '研究动态', icon: '🔬' },
  announcement: { label: '重要公告', icon: '📢' },
  reflection: { label: '个人思考', icon: '💭' },
  milestone: { label: '里程碑', icon: '🎯' },
};

// 研究所配置
const INSTITUTES = {
  economics: { label: '经济研究所', icon: '💰' },
  philosophy: { label: '哲学研究所', icon: '🤔' },
  internet: { label: '互联网研究所', icon: '🌐' },
  ai: { label: 'AI研究所', icon: '🤖' },
  future: { label: '未来研究所', icon: '🔮' },
};

/**
 * 生成文件名（基于标题和日期）
 */
function generateFileName(title, date = new Date()) {
  const dateStr = date.toISOString().split('T')[0];
  const slug = title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // 移除特殊字符
    .replace(/\s+/g, '-') // 空格替换为连字符
    .replace(/-+/g, '-') // 多个连字符合并为一个
    .trim();

  return `${dateStr}-${slug}.md`;
}

/**
 * 创建动态资讯
 */
function createNews(options) {
  const {
    title,
    description,
    type = 'research',
    institutes = [],
    tags = [],
    featured = false,
    summary = '',
  } = options;

  // 验证输入
  if (!title) {
    throw new Error('标题是必需的');
  }

  if (!description) {
    throw new Error('描述是必需的');
  }

  if (!NEWS_TYPES[type]) {
    throw new Error(`无效的动态类型: ${type}`);
  }

  // 验证研究所
  const validInstitutes = institutes.filter(inst => INSTITUTES[inst]);
  if (institutes.length > 0 && validInstitutes.length === 0) {
    console.warn('警告: 没有有效的研究所被指定');
  }

  // 读取模板
  if (!fs.existsSync(TEMPLATE_PATH)) {
    throw new Error(`模板文件不存在: ${TEMPLATE_PATH}`);
  }

  const template = fs.readFileSync(TEMPLATE_PATH, 'utf8');

  // 生成内容
  const now = new Date();
  const dateStr = now.toISOString().split('T')[0];

  const content = template
    .replace(/{{title\.zh}}/g, title)
    .replace(/{{publishDate}}/g, dateStr)
    .replace(/{{author}}/g, 'Pennfly')
    .replace(/title:\s*zh:\s*'[^']*'/g, `title:\n  zh: '${title}'`)
    .replace(/description:\s*zh:\s*'[^']*'/g, `description:\n  zh: '${description}'`)
    .replace(/publishDate:\s*[\d-]+/g, `publishDate: ${dateStr}`)
    .replace(/updateDate:\s*[\d-]+/g, `updateDate: ${dateStr}`)
    .replace(/type:\s*'[^']*'/g, `type: '${type}'`)
    .replace(/featured:\s*\w+/g, `featured: ${featured}`)
    .replace(
      /relatedInstitute:\s*\[[^\]]*\]/g,
      `relatedInstitute: [${validInstitutes.map(i => `'${i}'`).join(', ')}]`
    )
    .replace(/tags:\s*\[[^\]]*\]/g, `tags: [${tags.map(t => `'${t}'`).join(', ')}]`)
    .replace(/summary:\s*'[^']*'/g, `summary: '${summary || description}'`);

  // 生成文件名和路径
  const fileName = generateFileName(title, now);
  const filePath = path.join(CONTENT_DIR, 'news', fileName);

  // 检查文件是否已存在
  if (fs.existsSync(filePath)) {
    throw new Error(`文件已存在: ${fileName}`);
  }

  // 创建文件
  fs.writeFileSync(filePath, content, 'utf8');

  return {
    fileName,
    filePath,
    type: NEWS_TYPES[type].label,
    institutes: validInstitutes.map(i => INSTITUTES[i].label),
  };
}

/**
 * 交互式创建动态
 */
async function interactiveCreate() {
  const readline = await import('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  const question = prompt => new Promise(resolve => rl.question(prompt, resolve));

  try {
    console.log('🚀 创建新的动态资讯\n');

    const title = await question('📝 请输入动态标题: ');
    if (!title.trim()) {
      console.log('❌ 标题不能为空');
      process.exit(1);
    }

    const description = await question('📄 请输入动态描述: ');
    if (!description.trim()) {
      console.log('❌ 描述不能为空');
      process.exit(1);
    }

    console.log('\n📋 选择动态类型:');
    Object.entries(NEWS_TYPES).forEach(([key, config], index) => {
      console.log(`  ${index + 1}. ${config.icon} ${config.label} (${key})`);
    });

    const typeChoice = await question('请选择类型 (1-4): ');
    const typeKeys = Object.keys(NEWS_TYPES);
    const selectedType = typeKeys[parseInt(typeChoice) - 1] || 'research';

    console.log('\n🏛️ 选择相关研究所 (可多选，用逗号分隔):');
    Object.entries(INSTITUTES).forEach(([key, config], index) => {
      console.log(`  ${index + 1}. ${config.icon} ${config.label} (${key})`);
    });

    const instituteChoice = await question('请选择研究所 (例: 1,3): ');
    const instituteKeys = Object.keys(INSTITUTES);
    const selectedInstitutes = instituteChoice
      .split(',')
      .map(i => instituteKeys[parseInt(i.trim()) - 1])
      .filter(Boolean);

    const tags = await question('🏷️ 请输入标签 (用逗号分隔): ');
    const tagList = tags
      .split(',')
      .map(t => t.trim())
      .filter(Boolean);

    const featuredChoice = await question('📌 是否置顶? (y/N): ');
    const featured = featuredChoice.toLowerCase() === 'y';

    const summary = await question('📝 请输入摘要 (可选): ');

    // 创建动态
    const result = createNews({
      title: title.trim(),
      description: description.trim(),
      type: selectedType,
      institutes: selectedInstitutes,
      tags: tagList,
      featured,
      summary: summary.trim(),
    });

    console.log('\n✅ 动态创建成功!');
    console.log(`📁 文件名: ${result.fileName}`);
    console.log(`📂 路径: ${result.filePath}`);
    console.log(`📋 类型: ${result.type}`);
    if (result.institutes.length > 0) {
      console.log(`🏛️ 研究所: ${result.institutes.join(', ')}`);
    }
  } catch (error) {
    console.error('❌ 创建失败:', error.message);
    process.exit(1);
  } finally {
    rl.close();
  }
}

/**
 * 命令行创建动态
 */
function cliCreate() {
  const args = process.argv.slice(2);

  if (args.length === 0) {
    console.log(`
📰 动态资讯创建工具

用法:
  node create-news.js                    # 交互式创建
  node create-news.js [选项]             # 命令行创建

选项:
  --title "标题"                        # 动态标题 (必需)
  --description "描述"                  # 动态描述 (必需)
  --type research|announcement|reflection|milestone  # 动态类型
  --institutes economics,ai,philosophy  # 相关研究所
  --tags "标签1,标签2"                  # 标签
  --featured                           # 是否置顶
  --summary "摘要"                     # 摘要

示例:
  node create-news.js --title "AI研究新进展" --description "最新的AI技术突破" --type research --institutes ai --tags "AI,研究" --featured

可用的动态类型: ${Object.keys(NEWS_TYPES).join(', ')}
可用的研究所: ${Object.keys(INSTITUTES).join(', ')}
`);
    return;
  }

  // 解析命令行参数
  const options = {};
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    if (arg.startsWith('--')) {
      const key = arg.slice(2);
      if (key === 'featured') {
        options.featured = true;
      } else {
        const value = args[i + 1];
        if (value && !value.startsWith('--')) {
          if (key === 'institutes' || key === 'tags') {
            options[key] = value.split(',').map(s => s.trim());
          } else {
            options[key] = value;
          }
          i++; // 跳过值参数
        }
      }
    }
  }

  try {
    const result = createNews(options);
    console.log('✅ 动态创建成功!');
    console.log(`📁 文件: ${result.fileName}`);
  } catch (error) {
    console.error('❌ 创建失败:', error.message);
    process.exit(1);
  }
}

// 主函数
function main() {
  const args = process.argv.slice(2);

  if (args.length === 0 || args.includes('--interactive')) {
    interactiveCreate();
  } else {
    cliCreate();
  }
}

// 运行脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { createNews, generateFileName };
