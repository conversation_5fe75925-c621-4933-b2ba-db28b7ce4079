/**
 * Astro middleware entry point
 * Combines security middleware with other middleware functions
 */

import type { MiddlewareHandler } from 'astro';
import { sequence } from 'astro:middleware';
import { applySecurityHeaders, validateRequestSecurity } from './middleware/security.js';

/**
 * Security middleware handler
 */
const securityMiddleware: MiddlewareHandler = async (context, next) => {
  const { request } = context;

  // Validate request security for API routes
  if (request.url.includes('/api/')) {
    const validation = validateRequestSecurity(request);

    if (!validation.isValid) {
      return new Response(
        JSON.stringify({
          error: 'Security validation failed',
          details: validation.errors,
        }),
        {
          status: 403,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  }

  // Continue to next middleware/handler
  const response = await next();

  // Apply security headers to all responses
  return applySecurityHeaders(response);
};

/**
 * Logging middleware for development
 */
const loggingMiddleware: MiddlewareHandler = async (context, next) => {
  const { request } = context;

  // Only log in development
  if (import.meta.env.DEV) {
    const start = Date.now();
    const response = await next();
    const duration = Date.now() - start;

    console.log(`${request.method} ${request.url} - ${response.status} (${duration}ms)`);

    return response;
  }

  return next();
};

// Export the middleware sequence
export const onRequest = sequence(
  securityMiddleware,
  loggingMiddleware
  // Add other middleware here as needed
);
