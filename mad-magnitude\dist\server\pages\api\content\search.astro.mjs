import { d as contentManager } from "../../../assets/utils.bIDOeBqD.js";
import { i } from "../../../assets/vendor-astro.Dc6apy9i.js";
const prerender = false;
const GET = async ({ url }) => {
  try {
    const searchParams = new URL(url).searchParams;
    const query = searchParams.get("q");
    if (!query || query.trim().length === 0) {
      return new Response(
        JSON.stringify({
          error: "Search query is required",
          code: "MISSING_QUERY"
        }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
    const results = await contentManager.searchContent(query.trim());
    const page2 = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const offset = (page2 - 1) * limit;
    const paginatedResults = results.slice(offset, offset + limit);
    const response = {
      query: query.trim(),
      results: paginatedResults.map((item) => ({
        id: item.id,
        collection: item.collection,
        slug: item.slug,
        title: item.title,
        description: item.description,
        publishDate: item.publishDate.toISOString(),
        updateDate: item.updateDate?.toISOString(),
        draft: item.draft,
        featured: item.featured,
        tags: item.tags,
        author: item.author,
        // 返回内容摘要，突出显示搜索词
        contentPreview: highlightSearchTerm(
          item.content.substring(0, 300) + (item.content.length > 300 ? "..." : ""),
          query.trim()
        )
      })),
      pagination: {
        page: page2,
        limit,
        total: results.length,
        totalPages: Math.ceil(results.length / limit),
        hasNext: offset + limit < results.length,
        hasPrev: page2 > 1
      }
    };
    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        "Cache-Control": "public, max-age=300"
      }
    });
  } catch (error) {
    console.error("内容搜索 API 错误:", error);
    return new Response(
      JSON.stringify({
        error: "Internal server error",
        code: "INTERNAL_ERROR"
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" }
      }
    );
  }
};
function highlightSearchTerm(text, searchTerm) {
  const regex = new RegExp(`(${escapeRegExp(searchTerm)})`, "gi");
  return text.replace(regex, "<mark>$1</mark>");
}
function escapeRegExp(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
}
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({ __proto__: null, GET, prerender }, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  i as renderers
};
