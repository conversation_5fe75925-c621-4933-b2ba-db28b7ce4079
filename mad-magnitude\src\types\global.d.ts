// 全局类型声明文件

// 扩展 HTMLElement 接口以支持 dataset 属性
declare global {
  interface HTMLElement {
    dataset: DOMStringMap;
  }

  // 扩展 Event 接口
  interface Event {
    target: EventTarget | null;
    currentTarget: EventTarget | null;
  }

  // 为常用的 DOM 查询方法提供更好的类型支持
  interface Document {
    getElementById<T extends HTMLElement = HTMLElement>(elementId: string): T | null;
    querySelector<T extends Element = Element>(selectors: string): T | null;
    querySelectorAll<T extends Element = Element>(selectors: string): NodeListOf<T>;
  }

  interface Element {
    querySelector<T extends Element = Element>(selectors: string): T | null;
    querySelectorAll<T extends Element = Element>(selectors: string): NodeListOf<T>;
  }

  // Astro 特定的全局类型
  namespace Astro {
    interface Props {
      [key: string]: any;
    }
  }
}

// 导出空对象以使此文件成为模块
export {};
