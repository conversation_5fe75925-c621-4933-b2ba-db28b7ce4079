{"workspace": {"name": "Pennfly Private Academy", "type": "astro-project", "version": "1.0.0"}, "hooks": {"enabled": true, "autoDiscovery": true, "directory": "hooks", "indexFile": null, "defaultAutoApprove": false, "categories": {"showInUI": true, "groupByCategory": true}}, "steering": {"enabled": true, "autoDiscovery": true, "directory": "steering", "defaultInclusion": "always"}, "ui": {"showHookCategories": true, "showHookDescriptions": true, "compactMode": false, "theme": "auto"}, "notifications": {"showHookTriggers": true, "showSteeringUpdates": false, "autoHideAfter": 5000}}