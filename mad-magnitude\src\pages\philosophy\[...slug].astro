---
export const prerender = false;

import { getEntry } from 'astro:content';
import Layout from '../../layouts/Layout.astro';

// 在服务器端渲染模式下，直接从 URL 参数获取内容
const { slug } = Astro.params;

if (!slug) {
  return Astro.redirect('/philosophy');
}

// 获取文章内容
const article = await getEntry('philosophy', slug);

if (!article) {
  return Astro.redirect('/404');
}

const { Content } = await article.render();
---

<Layout title={`${article.data.title.zh} - 哲学研究所`} description={article.data.description.zh}>
  <main class="min-h-screen bg-gray-50">
    <div class="container mx-auto px-6 py-12">
      <!-- 面包屑导航 -->
      <nav class="mb-8 text-sm text-gray-600">
        <a href="/" class="hover:text-amber-600">研究院首页</a>
        <span class="mx-2">›</span>
        <a href="/philosophy" class="hover:text-amber-600">哲学研究所</a>
        <span class="mx-2">›</span>
        <span class="text-gray-800">{article.data.title.zh}</span>
      </nav>

      <!-- 文章头部 -->
      <header class="mb-8 rounded-lg border border-gray-200 bg-white p-8 shadow-sm">
        <h1 class="mb-4 text-3xl font-bold text-gray-800">{article.data.title.zh}</h1>
        <p class="mb-6 text-lg text-gray-600">{article.data.description.zh}</p>

        <div class="flex flex-wrap items-center gap-4 text-sm text-gray-500">
          <span>{article.data.publishDate.toLocaleDateString('zh-CN')}</span>
          {article.data.readingTime && <span>阅读时间 {article.data.readingTime} 分钟</span>}
          {
            article.data.philosophyBranch && (
              <span class="rounded bg-amber-100 px-2 py-1 text-xs text-amber-800">
                {article.data.philosophyBranch === 'ethics'
                  ? '伦理学'
                  : article.data.philosophyBranch === 'metaphysics'
                    ? '形而上学'
                    : article.data.philosophyBranch === 'epistemology'
                      ? '认识论'
                      : article.data.philosophyBranch === 'logic'
                        ? '逻辑学'
                        : '美学'}
              </span>
            )
          }
          {
            article.data.featured && (
              <span class="rounded bg-yellow-100 px-2 py-1 text-xs font-medium text-yellow-800">
                精选
              </span>
            )
          }
        </div>

        {/* 相关思想家 */}
        {
          article.data.thinkers && article.data.thinkers.length > 0 && (
            <div class="mt-4">
              <div class="mb-2 text-sm text-gray-500">相关思想家:</div>
              <div class="flex flex-wrap gap-2">
                {article.data.thinkers.map((thinker: string) => (
                  <span class="rounded bg-orange-100 px-2 py-1 text-sm text-orange-700">
                    {thinker}
                  </span>
                ))}
              </div>
            </div>
          )
        }

        {/* 标签 */}
        {
          article.data.tags.length > 0 && (
            <div class="mt-4">
              <div class="mb-2 text-sm text-gray-500">标签:</div>
              <div class="flex flex-wrap gap-2">
                {article.data.tags.map((tag: string) => (
                  <span class="rounded bg-gray-100 px-2 py-1 text-sm text-gray-700">{tag}</span>
                ))}
              </div>
            </div>
          )
        }
      </header>

      <!-- 文章内容 -->
      <article
        class="prose prose-lg mx-auto max-w-none rounded-lg border border-gray-200 bg-white p-8 shadow-sm"
      >
        <Content />
      </article>

      <!-- 返回按钮 -->
      <div class="mt-8 text-center">
        <a
          href="/philosophy"
          class="inline-block rounded-lg bg-amber-600 px-6 py-2 text-white transition-colors hover:bg-amber-700"
        >
          ← 返回哲学研究所
        </a>
      </div>
    </div>
  </main>
</Layout>
