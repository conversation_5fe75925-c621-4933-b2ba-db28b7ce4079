import { a as createComponent, h as renderComponent, d as renderTemplate, m as maybeRenderHead } from "../../assets/vendor-astro.Dc6apy9i.js";
import { i } from "../../assets/vendor-astro.Dc6apy9i.js";
import "kleur/colors";
import { a as $$ContentList } from "../../assets/admin.B_vy8HF_.js";
import { $ as $$Layout } from "../../assets/Layout.DbxDGMbZ.js";
import { d as contentManager } from "../../assets/utils.bIDOeBqD.js";
/* empty css                                   */
const $$Index = createComponent(async ($$result, $$props, $$slots) => {
  const stats = contentManager ? await contentManager.getContentStats() : {
    total: 0,
    published: 0,
    drafts: 0,
    featured: 0,
    byCollection: {},
    recentActivity: []
  };
  const title = "内容管理 - Pennfly Private Academy";
  const description = "管理和编辑学院的所有内容";
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": title, "description": description, "data-astro-cid-4t7oznc4": true }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<main class="content-management-page" data-astro-cid-4t7oznc4> <div class="container" data-astro-cid-4t7oznc4> <!-- 页面头部 --> <header class="page-header" data-astro-cid-4t7oznc4> <div class="header-content" data-astro-cid-4t7oznc4> <h1 class="page-title" data-astro-cid-4t7oznc4> <span class="title-icon" data-astro-cid-4t7oznc4>📝</span>
内容管理
</h1> <p class="page-description" data-astro-cid-4t7oznc4>管理和编辑学院的所有内容，包括文章、研究报告、日志等</p> </div> <div class="header-actions" data-astro-cid-4t7oznc4> <a href="/admin/content/create" class="btn btn--primary" data-astro-cid-4t7oznc4> <span class="btn-icon" data-astro-cid-4t7oznc4>➕</span>
新建内容
</a> <a href="/admin" class="btn btn--secondary" data-astro-cid-4t7oznc4> <span class="btn-icon" data-astro-cid-4t7oznc4>🏠</span>
返回后台
</a> </div> </header> <!-- 统计概览 --> <section class="stats-overview" data-astro-cid-4t7oznc4> <div class="stats-grid" data-astro-cid-4t7oznc4> <div class="stat-card" data-astro-cid-4t7oznc4> <div class="stat-icon" data-astro-cid-4t7oznc4>📊</div> <div class="stat-content" data-astro-cid-4t7oznc4> <div class="stat-number" data-astro-cid-4t7oznc4>${stats.total}</div> <div class="stat-label" data-astro-cid-4t7oznc4>总内容数</div> </div> </div> <div class="stat-card" data-astro-cid-4t7oznc4> <div class="stat-icon" data-astro-cid-4t7oznc4>✅</div> <div class="stat-content" data-astro-cid-4t7oznc4> <div class="stat-number" data-astro-cid-4t7oznc4>${stats.published}</div> <div class="stat-label" data-astro-cid-4t7oznc4>已发布</div> </div> </div> <div class="stat-card" data-astro-cid-4t7oznc4> <div class="stat-icon" data-astro-cid-4t7oznc4>📝</div> <div class="stat-content" data-astro-cid-4t7oznc4> <div class="stat-number" data-astro-cid-4t7oznc4>${stats.drafts}</div> <div class="stat-label" data-astro-cid-4t7oznc4>草稿</div> </div> </div> <div class="stat-card" data-astro-cid-4t7oznc4> <div class="stat-icon" data-astro-cid-4t7oznc4>⭐</div> <div class="stat-content" data-astro-cid-4t7oznc4> <div class="stat-number" data-astro-cid-4t7oznc4>${stats.featured}</div> <div class="stat-label" data-astro-cid-4t7oznc4>特色内容</div> </div> </div> </div> </section> <!-- 按集合统计 --> <section class="collection-stats" data-astro-cid-4t7oznc4> <h2 class="section-title" data-astro-cid-4t7oznc4> <span class="title-icon" data-astro-cid-4t7oznc4>📂</span>
集合统计
</h2> <div class="collection-grid" data-astro-cid-4t7oznc4> ${Object.entries(stats.byCollection).map(([collection, count]) => renderTemplate`<div class="collection-card" data-astro-cid-4t7oznc4> <div class="collection-header" data-astro-cid-4t7oznc4> <span class="collection-icon" data-astro-cid-4t7oznc4> ${collection === "news" && "📰"} ${collection === "logs" && "📔"} ${collection === "research" && "📊"} ${collection === "reflections" && "💭"} ${collection === "economics" && "💰"} ${collection === "philosophy" && "🤔"} ${collection === "internet" && "🌐"} ${collection === "ai" && "🤖"} ${collection === "future" && "🔮"} ${collection === "products" && "🛠️"} </span> <span class="collection-name" data-astro-cid-4t7oznc4> ${collection === "news" && "动态资讯"} ${collection === "logs" && "研究日志"} ${collection === "research" && "研究报告"} ${collection === "reflections" && "反思记录"} ${collection === "economics" && "经济研究"} ${collection === "philosophy" && "哲学研究"} ${collection === "internet" && "互联网研究"} ${collection === "ai" && "AI研究"} ${collection === "future" && "未来研究"} ${collection === "products" && "产品发布"} </span> </div> <div class="collection-count" data-astro-cid-4t7oznc4>${count}</div> </div>`)} </div> </section> <!-- 最近活动 --> <section class="recent-activity" data-astro-cid-4t7oznc4> <h2 class="section-title" data-astro-cid-4t7oznc4> <span class="title-icon" data-astro-cid-4t7oznc4>🕒</span>
最近活动
</h2> <div class="activity-list" data-astro-cid-4t7oznc4> ${stats.recentActivity.slice(0, 10).map((activity) => renderTemplate`<div class="activity-item" data-astro-cid-4t7oznc4> <div class="activity-icon" data-astro-cid-4t7oznc4>${activity.action === "创建" ? "➕" : "✏️"}</div> <div class="activity-content" data-astro-cid-4t7oznc4> <div class="activity-text" data-astro-cid-4t7oznc4> <span class="activity-action" data-astro-cid-4t7oznc4>${activity.action}</span>
了内容：
<span class="activity-title" data-astro-cid-4t7oznc4>${activity.content}</span> </div> <div class="activity-date" data-astro-cid-4t7oznc4> ${new Date(activity.date).toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit"
  })} </div> </div> </div>`)} </div> </section> <!-- 内容列表 --> <section class="content-list-section" data-astro-cid-4t7oznc4> <h2 class="section-title" data-astro-cid-4t7oznc4> <span class="title-icon" data-astro-cid-4t7oznc4>📋</span>
内容列表
</h2> ${renderComponent($$result2, "ContentList", $$ContentList, { "showFilters": true, "showActions": true, "data-astro-cid-4t7oznc4": true })} </section> </div> </main> ` })} `;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/admin/content/index.astro", void 0);
const $$file = "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/admin/content/index.astro";
const $$url = "/admin/content";
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({ __proto__: null, default: $$Index, file: $$file, url: $$url }, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  i as renderers
};
