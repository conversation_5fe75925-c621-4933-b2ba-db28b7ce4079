---
/**
 * Enhanced ThemeToggle component for Pennfly Private Academy
 * Provides polished UI controls for switching between light, dark, and auto themes
 * with improved accessibility, animations, and user experience
 */

import type { ThemeToggleProps } from '../../types/theme';

interface Props extends ThemeToggleProps {
  class?: string;
}

const {
  size = 'md',
  variant = 'dropdown',
  showLabel = false,
  class: className = '',
  ...rest
} = Astro.props;

// Enhanced size classes with better proportions
const sizeClasses = {
  sm: {
    button: 'w-8 h-8 text-sm',
    icon: 'w-4 h-4',
    dropdown: 'w-40',
    text: 'text-xs',
  },
  md: {
    button: 'w-10 h-10 text-base',
    icon: 'w-5 h-5',
    dropdown: 'w-48',
    text: 'text-sm',
  },
  lg: {
    button: 'w-12 h-12 text-lg',
    icon: 'w-6 h-6',
    dropdown: 'w-56',
    text: 'text-base',
  },
};

// Enhanced variant classes with theme-aware styling
const variantClasses = {
  icon: 'rounded-full',
  button: 'rounded-lg px-4 py-2',
  dropdown: 'rounded-lg',
};

// Theme-aware base classes using CSS custom properties
const baseClasses = `
  inline-flex items-center justify-center
  transition-all duration-300 ease-in-out
  focus:outline-none focus:ring-2 focus:ring-offset-2
  focus:ring-theme-interactive-focus focus:ring-offset-theme-bg-primary
  bg-theme-bg-elevated border border-theme-border-default
  text-theme-fg-primary
  hover:bg-theme-bg-secondary hover:border-theme-border-strong
  active:bg-theme-bg-tertiary active:scale-95
  disabled:opacity-50 disabled:cursor-not-allowed
  shadow-theme-sm hover:shadow-theme-md
`;

const currentSize = sizeClasses[size];
const combinedClasses = `
  ${baseClasses}
  ${currentSize.button}
  ${variantClasses[variant]}
  ${className}
`.trim();
---

{variant === 'dropdown' ? (
  <!-- Enhanced Dropdown variant -->
  <div class="relative inline-block text-left theme-toggle-dropdown">
    <button
      type="button"
      class={`${combinedClasses} theme-toggle-button group`}
      aria-label="Choose theme"
      aria-haspopup="true"
      aria-expanded="false"
      {...rest}
    >
      <!-- Theme icons with smooth transitions -->
      <div class="relative">
        <!-- Light theme icon -->
        <svg 
          class={`theme-icon theme-icon-light ${currentSize.icon} transition-all duration-300 ease-in-out`}
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path 
            stroke-linecap="round" 
            stroke-linejoin="round" 
            stroke-width="2" 
            d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
          />
        </svg>
        
        <!-- Dark theme icon -->
        <svg 
          class={`theme-icon theme-icon-dark ${currentSize.icon} absolute inset-0 transition-all duration-300 ease-in-out opacity-0 scale-75`}
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path 
            stroke-linecap="round" 
            stroke-linejoin="round" 
            stroke-width="2" 
            d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
          />
        </svg>
        
        <!-- Auto theme icon -->
        <svg 
          class={`theme-icon theme-icon-auto ${currentSize.icon} absolute inset-0 transition-all duration-300 ease-in-out opacity-0 scale-75`}
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path 
            stroke-linecap="round" 
            stroke-linejoin="round" 
            stroke-width="2" 
            d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
          />
        </svg>
      </div>
      
      {showLabel && (
        <span class={`ml-2 theme-label ${currentSize.text} font-medium`}>
          <span class="theme-label-light transition-opacity duration-300">Light</span>
          <span class="theme-label-dark absolute opacity-0 transition-opacity duration-300">Dark</span>
          <span class="theme-label-auto absolute opacity-0 transition-opacity duration-300">Auto</span>
        </span>
      )}
      
      <!-- Enhanced dropdown arrow with rotation animation -->
      <svg 
        class={`ml-2 ${currentSize.icon} transition-transform duration-300 ease-in-out group-aria-expanded:rotate-180`}
        fill="none" 
        stroke="currentColor" 
        viewBox="0 0 24 24"
        aria-hidden="true"
      >
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
      </svg>
    </button>
    
    <!-- Enhanced dropdown menu with animations -->
    <div class={`theme-dropdown-menu absolute right-0 mt-2 ${currentSize.dropdown} rounded-lg shadow-theme-lg bg-theme-bg-elevated border border-theme-border-default backdrop-blur-sm opacity-0 scale-95 pointer-events-none transition-all duration-200 ease-out z-50`}>
      <div class="py-2" role="menu" aria-orientation="vertical" aria-labelledby="theme-menu">
        <!-- Theme options with enhanced styling -->
        <button
          type="button"
          class={`theme-option group flex items-center w-full px-4 py-3 ${currentSize.text} text-theme-fg-primary hover:bg-theme-bg-secondary hover:text-theme-brand-primary transition-all duration-200 ease-in-out`}
          role="menuitem"
          data-theme="light"
        >
          <div class="flex items-center justify-center w-5 h-5 mr-3 rounded-full bg-theme-bg-tertiary group-hover:bg-theme-brand-primary/10 transition-colors duration-200">
            <svg class="w-3 h-3 text-theme-brand-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
            </svg>
          </div>
          <span class="flex-1 text-left font-medium">Light</span>
          <svg class="theme-check ml-auto w-4 h-4 text-theme-semantic-success opacity-0 scale-75 transition-all duration-200" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
        </button>
        
        <button
          type="button"
          class={`theme-option group flex items-center w-full px-4 py-3 ${currentSize.text} text-theme-fg-primary hover:bg-theme-bg-secondary hover:text-theme-brand-primary transition-all duration-200 ease-in-out`}
          role="menuitem"
          data-theme="dark"
        >
          <div class="flex items-center justify-center w-5 h-5 mr-3 rounded-full bg-theme-bg-tertiary group-hover:bg-theme-brand-primary/10 transition-colors duration-200">
            <svg class="w-3 h-3 text-theme-brand-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
            </svg>
          </div>
          <span class="flex-1 text-left font-medium">Dark</span>
          <svg class="theme-check ml-auto w-4 h-4 text-theme-semantic-success opacity-0 scale-75 transition-all duration-200" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
        </button>
        
        <button
          type="button"
          class={`theme-option group flex items-center w-full px-4 py-3 ${currentSize.text} text-theme-fg-primary hover:bg-theme-bg-secondary hover:text-theme-brand-primary transition-all duration-200 ease-in-out`}
          role="menuitem"
          data-theme="auto"
        >
          <div class="flex items-center justify-center w-5 h-5 mr-3 rounded-full bg-theme-bg-tertiary group-hover:bg-theme-brand-primary/10 transition-colors duration-200">
            <svg class="w-3 h-3 text-theme-brand-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          <span class="flex-1 text-left font-medium">Auto</span>
          <svg class="theme-check ml-auto w-4 h-4 text-theme-semantic-success opacity-0 scale-75 transition-all duration-200" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
        </button>
        
        <!-- Separator -->
        <div class="my-2 border-t border-theme-border-subtle"></div>
        
        <!-- High contrast option -->
        <button
          type="button"
          class={`theme-option group flex items-center w-full px-4 py-3 ${currentSize.text} text-theme-fg-primary hover:bg-theme-bg-secondary hover:text-theme-brand-primary transition-all duration-200 ease-in-out`}
          role="menuitem"
          data-variant="high-contrast"
        >
          <div class="flex items-center justify-center w-5 h-5 mr-3 rounded-full bg-theme-bg-tertiary group-hover:bg-theme-brand-primary/10 transition-colors duration-200">
            <svg class="w-3 h-3 text-theme-brand-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
          </div>
          <span class="flex-1 text-left font-medium">High Contrast</span>
          <svg class="variant-check ml-auto w-4 h-4 text-theme-semantic-success opacity-0 scale-75 transition-all duration-200" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>
    </div>
  </div>
) : (
  <!-- Enhanced simple toggle button (icon or button variant) -->
  <button
    type="button"
    class={`${combinedClasses} theme-toggle-button group`}
    aria-label="Toggle theme"
    {...rest}
  >
    <!-- Theme icons with enhanced animations -->
    <div class="relative">
      <!-- Light theme icon -->
      <svg 
        class={`theme-icon theme-icon-light ${currentSize.icon} transition-all duration-300 ease-in-out`}
        fill="none" 
        stroke="currentColor" 
        viewBox="0 0 24 24"
        aria-hidden="true"
      >
        <path 
          stroke-linecap="round" 
          stroke-linejoin="round" 
          stroke-width="2" 
          d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
        />
      </svg>
      
      <!-- Dark theme icon -->
      <svg 
        class={`theme-icon theme-icon-dark ${currentSize.icon} absolute inset-0 transition-all duration-300 ease-in-out opacity-0 scale-75 rotate-180`}
        fill="none" 
        stroke="currentColor" 
        viewBox="0 0 24 24"
        aria-hidden="true"
      >
        <path 
          stroke-linecap="round" 
          stroke-linejoin="round" 
          stroke-width="2" 
          d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
        />
      </svg>
      
      <!-- Auto theme icon -->
      <svg 
        class={`theme-icon theme-icon-auto ${currentSize.icon} absolute inset-0 transition-all duration-300 ease-in-out opacity-0 scale-75`}
        fill="none" 
        stroke="currentColor" 
        viewBox="0 0 24 24"
        aria-hidden="true"
      >
        <path 
          stroke-linecap="round" 
          stroke-linejoin="round" 
          stroke-width="2" 
          d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
        />
      </svg>
    </div>
    
    {showLabel && (
      <span class={`ml-3 theme-label ${currentSize.text} font-medium relative overflow-hidden`}>
        <span class="theme-label-light transition-all duration-300 ease-in-out">Light</span>
        <span class="theme-label-dark absolute inset-0 transition-all duration-300 ease-in-out opacity-0 translate-y-full">Dark</span>
        <span class="theme-label-auto absolute inset-0 transition-all duration-300 ease-in-out opacity-0 translate-y-full">Auto</span>
      </span>
    )}
  </button>
)}

<script>
  import type { ThemeMode, ThemeVariant } from '../../types/theme';
  
  class EnhancedThemeToggle {
    private toggleButtons: NodeListOf<Element>;
    private dropdownMenus: NodeListOf<Element>;
    private themeManager: any;
    private animationDuration = 300;
    
    constructor() {
      this.toggleButtons = document.querySelectorAll('.theme-toggle-button');
      this.dropdownMenus = document.querySelectorAll('.theme-dropdown-menu');
      this.themeManager = (window as any).themeManager;
      
      this.init();
    }
    
    private init(): void {
      // Wait for theme manager to be available
      if (!this.themeManager) {
        setTimeout(() => this.init(), 100);
        return;
      }
      
      this.setupEventListeners();
      this.updateUI();
      
      // Listen for theme changes
      window.addEventListener('themechange', () => {
        this.updateUI();
      });
      
      // Respect user's motion preferences
      if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
        this.animationDuration = 0;
      }
    }
    
    private setupEventListeners(): void {
      // Enhanced toggle buttons with better interaction
      this.toggleButtons.forEach(button => {
        const dropdown = button.closest('.theme-toggle-dropdown');
        
        if (dropdown) {
          // Dropdown toggle with enhanced animations
          button.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleDropdown(dropdown);
          });
          
          // Enhanced keyboard support
          button.addEventListener('keydown', ((e: KeyboardEvent) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              this.toggleDropdown(dropdown);
            } else if (e.key === 'ArrowDown') {
              e.preventDefault();
              this.openDropdown(dropdown);
              this.focusFirstOption(dropdown);
            }
          }) as EventListener);
        } else {
          // Simple toggle with animation feedback
          button.addEventListener('click', () => {
            this.animateToggle(button);
            this.themeManager.toggleTheme();
          });
        }
      });
      
      // Enhanced dropdown options
      document.querySelectorAll('.theme-option').forEach(option => {
        option.addEventListener('click', (e) => {
          const target = e.currentTarget as HTMLElement;
          const theme = target.getAttribute('data-theme') as ThemeMode;
          const variant = target.getAttribute('data-variant') as ThemeVariant;
          
          // Animate selection
          this.animateSelection(target);
          
          if (theme) {
            this.themeManager.setTheme(theme);
          }
          
          if (variant) {
            this.themeManager.setVariant(variant);
          }
          
          // Close dropdown with delay for animation
          setTimeout(() => {
            this.closeAllDropdowns();
          }, 150);
        });
        
        // Enhanced keyboard navigation
        option.addEventListener('keydown', ((e: KeyboardEvent) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            (option as HTMLElement).click();
          } else if (e.key === 'ArrowDown') {
            e.preventDefault();
            this.focusNextOption(option);
          } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            this.focusPreviousOption(option);
          } else if (e.key === 'Escape') {
            e.preventDefault();
            this.closeAllDropdowns();
            this.focusToggleButton(option);
          }
        }) as EventListener);
      });
      
      // Close dropdowns when clicking outside
      document.addEventListener('click', (e) => {
        const target = e.target as Element;
        if (!target.closest('.theme-toggle-dropdown')) {
          this.closeAllDropdowns();
        }
      });
      
      // Enhanced keyboard navigation
      document.addEventListener('keydown', ((e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          this.closeAllDropdowns();
        }
      }) as EventListener);
    }
    
    private animateToggle(button: Element): void {
      if (this.animationDuration === 0) return;
      
      button.classList.add('animate-pulse');
      setTimeout(() => {
        button.classList.remove('animate-pulse');
      }, this.animationDuration);
    }
    
    private animateSelection(option: Element): void {
      if (this.animationDuration === 0) return;
      
      option.classList.add('bg-theme-brand-primary/20');
      setTimeout(() => {
        option.classList.remove('bg-theme-brand-primary/20');
      }, 200);
    }
    
    private toggleDropdown(dropdown: Element): void {
      const menu = dropdown.querySelector('.theme-dropdown-menu');
      const button = dropdown.querySelector('.theme-toggle-button');
      
      if (menu && button) {
        const isOpen = !menu.classList.contains('opacity-0');
        
        // Close all dropdowns first
        this.closeAllDropdowns();
        
        if (!isOpen) {
          this.openDropdown(dropdown);
        }
      }
    }
    
    private openDropdown(dropdown: Element): void {
      const menu = dropdown.querySelector('.theme-dropdown-menu');
      const button = dropdown.querySelector('.theme-toggle-button');
      
      if (menu && button) {
        // Remove hidden classes and add visible classes
        menu.classList.remove('opacity-0', 'scale-95', 'pointer-events-none');
        menu.classList.add('opacity-100', 'scale-100', 'pointer-events-auto');
        button.setAttribute('aria-expanded', 'true');
      }
    }
    
    private closeAllDropdowns(): void {
      this.dropdownMenus.forEach(menu => {
        menu.classList.remove('opacity-100', 'scale-100', 'pointer-events-auto');
        menu.classList.add('opacity-0', 'scale-95', 'pointer-events-none');
        
        const button = menu.closest('.theme-toggle-dropdown')?.querySelector('.theme-toggle-button');
        if (button) {
          button.setAttribute('aria-expanded', 'false');
        }
      });
    }
    
    private focusFirstOption(dropdown: Element): void {
      const firstOption = dropdown.querySelector('.theme-option') as HTMLElement;
      if (firstOption) {
        firstOption.focus();
      }
    }
    
    private focusNextOption(currentOption: Element): void {
      const options = Array.from(document.querySelectorAll('.theme-option'));
      const currentIndex = options.indexOf(currentOption);
      const nextIndex = (currentIndex + 1) % options.length;
      (options[nextIndex] as HTMLElement).focus();
    }
    
    private focusPreviousOption(currentOption: Element): void {
      const options = Array.from(document.querySelectorAll('.theme-option'));
      const currentIndex = options.indexOf(currentOption);
      const previousIndex = currentIndex === 0 ? options.length - 1 : currentIndex - 1;
      (options[previousIndex] as HTMLElement).focus();
    }
    
    private focusToggleButton(option: Element): void {
      const dropdown = option.closest('.theme-toggle-dropdown');
      const button = dropdown?.querySelector('.theme-toggle-button') as HTMLElement;
      if (button) {
        button.focus();
      }
    }
    
    private updateUI(): void {
      const currentTheme = this.themeManager.getTheme();
      const currentVariant = this.themeManager.getVariant();
      
      // Enhanced icon transitions
      this.updateIcons(currentTheme);
      this.updateLabels(currentTheme);
      this.updateDropdownSelections(currentTheme, currentVariant);
      this.updateAriaLabels(currentTheme, currentVariant);
    }
    
    private updateIcons(currentTheme: ThemeMode): void {
      document.querySelectorAll('.theme-icon').forEach(icon => {
        icon.classList.remove('opacity-100', 'scale-100', 'rotate-0');
        icon.classList.add('opacity-0', 'scale-75');
        
        if (icon.classList.contains('theme-icon-dark')) {
          icon.classList.add('rotate-180');
        }
      });
      
      // Show current theme icon with animation
      setTimeout(() => {
        document.querySelectorAll(`.theme-icon-${currentTheme}`).forEach(icon => {
          icon.classList.remove('opacity-0', 'scale-75', 'rotate-180');
          icon.classList.add('opacity-100', 'scale-100', 'rotate-0');
        });
      }, this.animationDuration / 2);
    }
    
    private updateLabels(currentTheme: ThemeMode): void {
      document.querySelectorAll('.theme-label span').forEach(label => {
        label.classList.remove('opacity-100', 'translate-y-0');
        label.classList.add('opacity-0', 'translate-y-full');
      });
      
      // Show current theme label with animation
      setTimeout(() => {
        document.querySelectorAll(`.theme-label-${currentTheme}`).forEach(label => {
          label.classList.remove('opacity-0', 'translate-y-full');
          label.classList.add('opacity-100', 'translate-y-0');
        });
      }, this.animationDuration / 2);
    }
    
    private updateDropdownSelections(currentTheme: ThemeMode, currentVariant: ThemeVariant): void {
      // Hide all checkmarks
      document.querySelectorAll('.theme-check, .variant-check').forEach(check => {
        check.classList.remove('opacity-100', 'scale-100');
        check.classList.add('opacity-0', 'scale-75');
      });
      
      // Show current selections
      const currentThemeOption = document.querySelector(`[data-theme="${currentTheme}"] .theme-check`);
      if (currentThemeOption) {
        currentThemeOption.classList.remove('opacity-0', 'scale-75');
        currentThemeOption.classList.add('opacity-100', 'scale-100');
      }
      
      if (currentVariant === 'high-contrast') {
        const variantOption = document.querySelector(`[data-variant="${currentVariant}"] .variant-check`);
        if (variantOption) {
          variantOption.classList.remove('opacity-0', 'scale-75');
          variantOption.classList.add('opacity-100', 'scale-100');
        }
      }
    }
    
    private updateAriaLabels(currentTheme: ThemeMode, currentVariant: ThemeVariant): void {
      this.toggleButtons.forEach(button => {
        const variantText = currentVariant === 'high-contrast' ? ' (High Contrast)' : '';
        const newLabel = `Current theme: ${currentTheme}${variantText}. Click to change theme.`;
        button.setAttribute('aria-label', newLabel);
      });
    }
  }
  
  // Initialize enhanced theme toggle when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => new EnhancedThemeToggle());
  } else {
    new EnhancedThemeToggle();
  }
</script>