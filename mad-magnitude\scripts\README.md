# 构建脚本文档

本目录包含了项目的构建优化、分析和监控脚本。这些脚本帮助确保构建质量、性能优化和错误处理。

## 脚本概览

### 📊 分析脚本

#### `analyze-bundle.js`
分析构建产物的大小和性能，提供优化建议。

```bash
# 运行包分析
npm run analyze:size

# 或直接运行
node scripts/analyze-bundle.js
```

**功能:**
- 分析构建产物总大小
- 按文件类型分类统计
- 识别最大的文件
- 生成优化建议
- 检查性能预算

#### `optimize-images.js`
分析项目中的图片文件，提供优化建议。

```bash
# 运行图片分析
npm run analyze:images

# 或直接运行
node scripts/optimize-images.js
```

**功能:**
- 扫描项目中的图片文件
- 分析文件大小和格式
- 提供格式优化建议
- 生成 Astro 配置建议

### 🔍 监控脚本

#### `build-monitor.js`
监控构建过程，记录性能指标和历史数据。

```bash
# 运行构建监控
npm run build:monitor

# 或直接运行
node scripts/build-monitor.js
```

**功能:**
- 监控构建时间
- 记录构建历史
- 分析构建趋势
- 生成性能报告
- 检测性能回归

#### `build-error-handler.js`
处理构建错误，提供友好的错误信息和解决方案。

```bash
# 设置全局错误处理
node scripts/build-error-handler.js
```

**功能:**
- 分析错误类型
- 提供解决方案建议
- 记录错误日志
- 友好的错误显示
- 错误统计分析

### 🚀 完整构建脚本

#### `build-complete.js`
集成所有构建、检查、分析功能的完整构建流程。

```bash
# 运行完整构建流程
npm run build:complete

# 开发环境构建
npm run build:complete:dev

# 带选项的构建
node scripts/build-complete.js --verbose --skip-tests
```

**选项:**
- `--skip-tests`: 跳过测试
- `--skip-linting`: 跳过代码检查
- `--skip-analysis`: 跳过构建分析
- `--env <env>`: 设置构建环境
- `--verbose`: 详细输出
- `--help`: 显示帮助信息

**构建流程:**
1. 预构建检查 (类型检查、代码检查、测试)
2. 清理构建目录
3. 执行主构建
4. 构建后分析 (包大小、图片优化)
5. 生成构建报告

## 配置文件

### `build.config.js`
包含所有构建相关的配置和优化选项。

**主要配置:**
- `BUILD_ENVIRONMENTS`: 不同环境的构建配置
- `CHUNK_STRATEGY`: 代码分割策略
- `ASSET_NAMING`: 资源文件命名规则
- `BUILD_OPTIMIZATION`: 构建优化选项
- `PERFORMANCE_BUDGET`: 性能预算配置
- `BUILD_MONITORING`: 构建监控配置

## NPM 脚本

### 基础构建脚本
```bash
npm run build              # 基础构建
npm run build:prod         # 生产环境构建
npm run build:analyze      # 带分析的构建
npm run preview            # 预览构建结果
```

### 分析脚本
```bash
npm run analyze:size       # 包大小分析
npm run analyze:images     # 图片分析
npm run build:report       # 完整分析报告
```

### 监控脚本
```bash
npm run build:monitor      # 构建监控
npm run build:complete     # 完整构建流程
npm run build:complete:dev # 开发环境完整构建
```

### 维护脚本
```bash
npm run clean              # 清理构建目录
npm run clean:all          # 清理所有缓存
```

## 报告文件

构建脚本会在 `reports/` 目录下生成以下报告文件:

- `build-metrics.json`: 构建性能历史数据
- `build-errors.log`: 构建错误日志
- 其他分析报告文件

## 使用建议

### 开发阶段
```bash
# 快速构建和检查
npm run build

# 完整的开发构建
npm run build:complete:dev --verbose
```

### 生产部署前
```bash
# 完整的生产构建流程
npm run build:complete

# 或者分步执行
npm run check              # 代码检查
npm run build:prod         # 生产构建
npm run build:report       # 生成报告
```

### 性能优化
```bash
# 分析包大小
npm run analyze:size

# 分析图片优化机会
npm run analyze:images

# 监控构建性能
npm run build:monitor
```

### 错误排查
```bash
# 详细构建输出
npm run build:complete --verbose

# 查看错误日志
cat reports/build-errors.log

# 查看构建历史
cat reports/build-metrics.json
```

## 自定义配置

### 修改构建配置
编辑 `build.config.js` 文件来自定义:
- 代码分割策略
- 资源命名规则
- 性能预算
- 监控选项

### 添加自定义检查
在 `build-complete.js` 中的 `runPreBuildChecks()` 方法中添加自定义检查。

### 扩展分析功能
在相应的分析脚本中添加新的分析逻辑和建议。

## 故障排除

### 常见问题

1. **构建脚本权限错误**
   ```bash
   chmod +x scripts/*.js
   ```

2. **报告目录不存在**
   ```bash
   mkdir -p reports
   ```

3. **Node.js 版本不兼容**
   确保使用 Node.js 18+ 版本

4. **依赖缺失**
   ```bash
   npm install
   ```

### 调试模式
使用 `--verbose` 选项获取详细输出:
```bash
npm run build:complete -- --verbose
```

## 贡献指南

添加新的构建脚本时:
1. 遵循现有的代码风格
2. 添加适当的错误处理
3. 提供详细的日志输出
4. 更新此文档
5. 添加相应的 npm 脚本

## 许可证

这些脚本遵循项目的 MIT 许可证。