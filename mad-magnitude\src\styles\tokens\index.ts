/**
 * 设计令牌系统 - 统一导出
 * 提供完整的设计令牌访问接口
 */

// 导入所有令牌
import { colors } from './colors';
import { typography } from './typography';
import { spacing, semanticSpacing } from './spacing';
import { borderRadius, semanticRadius } from './radius';
import { boxShadow, semanticShadow, darkShadow } from './shadows';

// 重新导出所有令牌和类型
export { colors, type ColorToken, type ColorScale, type SemanticColor } from './colors';
export { typography, type TypographyToken, type FontSize, type FontWeight } from './typography';
export { spacing, semanticSpacing, type SpacingToken, type SemanticSpacing } from './spacing';
export {
  borderRadius,
  semanticRadius,
  type BorderRadiusToken,
  type SemanticRadius,
} from './radius';
export {
  boxShadow,
  semanticShadow,
  darkShadow,
  type BoxShadowToken,
  type SemanticShadow,
} from './shadows';

// 完整的设计令牌集合
export const designTokens = {
  colors,
  typography,
  spacing,
  semanticSpacing,
  borderRadius,
  semanticRadius,
  boxShadow,
  semanticShadow,
  darkShadow,
} as const;

// 设计令牌类型定义
export type DesignTokens = typeof designTokens;
