---
import AcademyStats from '../components/home/<USER>';
import RecentContent from '../components/home/<USER>';
import BookIcon from '../components/icons/BookIcon.astro';
import LightBulbIcon from '../components/icons/LightBulbIcon.astro';
import UsersIcon from '../components/icons/UsersIcon.astro';
import TagCloud from '../components/tags/TagCloud.astro';
import TagSearch from '../components/tags/TagSearch.astro';
import Layout from '../layouts/Layout.astro';
---

<Layout
  title="首页 - Pennfly Private Academy"
  description="Pennfly Private Academy - 个人化私人研究院 · 跨领域学术探索 · 思想与创意的交流平台"
>
  <!-- 英雄区域 -->
  <section class="relative overflow-hidden bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-700 py-20 text-center text-white md:py-32">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 opacity-20">
      <div class="h-full w-full" style="background-image: radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 0%, transparent 50%);"></div>
    </div>

    <div class="relative container mx-auto px-6">
      <!-- 主标题区域 -->
      <div class="mb-12">
        <h1 class="mb-6 text-5xl leading-tight font-bold md:text-6xl lg:text-7xl">
          <span class="bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
            Pennfly
          </span>
          <br />
          <span class="text-white">Private Academy</span>
        </h1>
        <p class="mx-auto mb-6 max-w-3xl text-xl leading-relaxed opacity-90 md:text-2xl">
          个人化私人研究院 · 跨领域学术探索 · 思想与创意的交流平台
        </p>
        <p class="mx-auto max-w-2xl text-lg opacity-75">
          致力于深度思考与知识创造，探索学术研究的无限可能
        </p>
      </div>

      <!-- 快速统计 -->
      <div class="mb-12 grid grid-cols-2 gap-6 md:grid-cols-4">
        <div class="rounded-xl bg-white/10 p-6 backdrop-blur-sm border border-white/20">
          <div class="text-3xl font-bold mb-2">5+</div>
          <div class="text-sm opacity-90">研究所</div>
        </div>
        <div class="rounded-xl bg-white/10 p-6 backdrop-blur-sm border border-white/20">
          <div class="text-3xl font-bold mb-2">50+</div>
          <div class="text-sm opacity-90">研究文章</div>
        </div>
        <div class="rounded-xl bg-white/10 p-6 backdrop-blur-sm border border-white/20">
          <div class="text-3xl font-bold mb-2">100+</div>
          <div class="text-sm opacity-90">研究日志</div>
        </div>
        <div class="rounded-xl bg-white/10 p-6 backdrop-blur-sm border border-white/20">
          <div class="text-3xl font-bold mb-2">持续</div>
          <div class="text-sm opacity-90">更新中</div>
        </div>
      </div>

      <!-- 行动按钮 -->
      <div class="flex flex-col items-center justify-center gap-4 sm:flex-row sm:gap-6">
        <a
          href="/news"
          class="group inline-flex items-center gap-3 rounded-xl bg-white px-8 py-4 font-semibold text-blue-600 shadow-lg transition-all hover:-translate-y-1 hover:bg-gray-50 hover:shadow-xl"
        >
          <span>📰</span>
          <span>最新动态</span>
          <span class="transition-transform group-hover:translate-x-1">→</span>
        </a>
        <a
          href="/research"
          class="group inline-flex items-center gap-3 rounded-xl border-2 border-white/30 bg-white/10 px-8 py-4 font-semibold text-white backdrop-blur-sm transition-all hover:-translate-y-1 hover:bg-white/20 hover:shadow-lg"
        >
          <span>🔬</span>
          <span>研究成果</span>
          <span class="transition-transform group-hover:translate-x-1">→</span>
        </a>
        <a
          href="/admin"
          class="group inline-flex items-center gap-3 rounded-xl border border-white/30 px-6 py-3 text-sm font-medium text-white/90 transition-all hover:bg-white/10 hover:text-white"
        >
          <span>⚙️</span>
          <span>管理后台</span>
        </a>
      </div>
    </div>

    <!-- 底部波浪装饰 -->
    <div class="absolute bottom-0 left-0 w-full">
      <svg viewBox="0 0 1200 120" preserveAspectRatio="none" class="relative block h-16 w-full">
        <path
          d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"
          class="fill-gray-50"></path>
      </svg>
    </div>
  </section>

  <!-- 研究院架构展示 -->
  <main class="bg-gray-50">
    <div class="container mx-auto px-6 py-20">
      <!-- 研究院介绍 -->
      <div class="mb-20 text-center">
        <h2 class="mb-8 text-4xl font-bold text-slate-800 md:text-5xl">私人研究院架构</h2>
        <p class="mx-auto max-w-4xl text-xl leading-relaxed text-slate-600">
          Pennfly Private Academy 采用研究院组织结构，设立多个专业研究所和功能中心，
          致力于跨领域学术研究和思想交流，以学术风格展示个人的思考成果和研究兴趣。
        </p>
      </div>

      <!-- 核心板块 -->
      <div class="mb-20">
        <div class="mb-12 text-center">
          <h3 class="mb-4 text-3xl font-bold text-slate-800">核心板块</h3>
          <p class="mx-auto max-w-2xl text-lg text-slate-600">
            记录思考轨迹，分享研究成果，构建知识体系
          </p>
        </div>
        <div class="grid gap-8 md:grid-cols-2">
          <div class="rounded-xl bg-white p-8 shadow-lg transition-all hover:shadow-xl hover:-translate-y-1">
            <div class="mb-6 flex items-center">
              <div class="mr-4 flex h-16 w-16 items-center justify-center rounded-xl bg-blue-100 text-3xl">📰</div>
              <div>
                <h4 class="text-xl font-semibold text-slate-800">动态资讯</h4>
                <p class="text-slate-600">个人研究动向，概述文章为主</p>
              </div>
            </div>
            <p class="mb-6 text-slate-600 leading-relaxed">
              及时分享研究院的最新动态、研究进展和重要公告，让访问者了解最新的学术思考和发现。
            </p>
            <a href="/news" class="inline-flex items-center gap-2 font-medium text-blue-600 hover:text-blue-800 transition-colors">
              <span>查看动态</span>
              <span>→</span>
            </a>
          </div>

          <div class="rounded-xl bg-white p-8 shadow-lg transition-all hover:shadow-xl hover:-translate-y-1">
            <div class="mb-6 flex items-center">
              <div class="mr-4 flex h-16 w-16 items-center justify-center rounded-xl bg-green-100 text-3xl">📔</div>
              <div>
                <h4 class="text-xl font-semibold text-slate-800">研究日志</h4>
                <p class="text-slate-600">个人日常研究记录</p>
              </div>
            </div>
            <p class="mb-6 text-slate-600 leading-relaxed">
              记录日常的研究思考、学习心得和灵感闪现，展示知识创造和思考演进的真实过程。
            </p>
            <a href="/logs" class="inline-flex items-center gap-2 font-medium text-blue-600 hover:text-blue-800 transition-colors">
              <span>查看日志</span>
              <span>→</span>
            </a>
          </div>
        </div>
      </div>
      <!-- 专业研究所 -->
      <div class="mb-20">
        <div class="mb-12 text-center">
          <h3 class="mb-4 text-3xl font-bold text-slate-800">专业研究所</h3>
          <p class="mx-auto max-w-2xl text-lg text-slate-600">
            多元化的研究领域，深入探索各个学科的前沿思考
          </p>
        </div>

        <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          <!-- 经济研究所 -->
          <div class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-amber-50 to-orange-50 p-8 shadow-lg transition-all hover:-translate-y-2 hover:shadow-2xl">
            <div class="absolute -top-4 -right-4 h-24 w-24 rounded-full bg-gradient-to-br from-amber-200/30 to-orange-200/30"></div>
            <div class="relative">
              <div class="mb-6 flex items-center">
                <div class="mr-4 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-amber-500 to-orange-500 text-2xl text-white shadow-lg">
                  💰
                </div>
                <div>
                  <h4 class="text-xl font-bold text-slate-800">经济研究所</h4>
                  <p class="text-sm text-slate-600">Economics Institute</p>
                </div>
              </div>
              <p class="mb-6 leading-relaxed text-slate-600">
                深入分析市场趋势、政策影响与经济理论，提供独特的经济学视角和前瞻性思考。
              </p>
              <a href="/economics" class="group inline-flex items-center gap-2 font-semibold text-amber-600 transition-all hover:text-amber-700">
                <span>进入研究所</span>
                <span class="transition-transform group-hover:translate-x-1">→</span>
              </a>
            </div>
          </div>

          <!-- 哲学研究所 -->
          <div class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-purple-50 to-indigo-50 p-8 shadow-lg transition-all hover:-translate-y-2 hover:shadow-2xl">
            <div class="absolute -top-4 -right-4 h-24 w-24 rounded-full bg-gradient-to-br from-purple-200/30 to-indigo-200/30"></div>
            <div class="relative">
              <div class="mb-6 flex items-center">
                <div class="mr-4 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-purple-500 to-indigo-500 text-2xl text-white shadow-lg">
                  🤔
                </div>
                <div>
                  <h4 class="text-xl font-bold text-slate-800">哲学研究所</h4>
                  <p class="text-sm text-slate-600">Philosophy Institute</p>
                </div>
              </div>
              <p class="mb-6 leading-relaxed text-slate-600">
                探索思想的深度与广度，进行哲学理论研究和现实问题的哲学思辨。
              </p>
              <a href="/philosophy" class="group inline-flex items-center gap-2 font-semibold text-purple-600 transition-all hover:text-purple-700">
                <span>进入研究所</span>
                <span class="transition-transform group-hover:translate-x-1">→</span>
              </a>
            </div>
          </div>

          <!-- 互联网研究所 -->
          <div class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-50 to-cyan-50 p-8 shadow-lg transition-all hover:-translate-y-2 hover:shadow-2xl">
            <div class="absolute -top-4 -right-4 h-24 w-24 rounded-full bg-gradient-to-br from-blue-200/30 to-cyan-200/30"></div>
            <div class="relative">
              <div class="mb-6 flex items-center">
                <div class="mr-4 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-blue-500 to-cyan-500 text-2xl text-white shadow-lg">
                  🌐
                </div>
                <div>
                  <h4 class="text-xl font-bold text-slate-800">互联网研究所</h4>
                  <p class="text-sm text-slate-600">Internet Institute</p>
                </div>
              </div>
              <p class="mb-6 leading-relaxed text-slate-600">
                关注互联网行业发展趋势，分析技术变革对社会的深远影响。
              </p>
              <a href="/internet" class="group inline-flex items-center gap-2 font-semibold text-blue-600 transition-all hover:text-blue-700">
                <span>进入研究所</span>
                <span class="transition-transform group-hover:translate-x-1">→</span>
              </a>
            </div>
          </div>

          <!-- AI研究所 -->
          <div class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-green-50 to-emerald-50 p-8 shadow-lg transition-all hover:-translate-y-2 hover:shadow-2xl">
            <div class="absolute -top-4 -right-4 h-24 w-24 rounded-full bg-gradient-to-br from-green-200/30 to-emerald-200/30"></div>
            <div class="relative">
              <div class="mb-6 flex items-center">
                <div class="mr-4 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-green-500 to-emerald-500 text-2xl text-white shadow-lg">
                  🤖
                </div>
                <div>
                  <h4 class="text-xl font-bold text-slate-800">人工智能研究所</h4>
                  <p class="text-sm text-slate-600">AI Institute</p>
                </div>
              </div>
              <p class="mb-6 leading-relaxed text-slate-600">
                深入研究AI技术发展，探索人工智能的应用前景与社会意义。
              </p>
              <a href="/ai" class="group inline-flex items-center gap-2 font-semibold text-green-600 transition-all hover:text-green-700">
                <span>进入研究所</span>
                <span class="transition-transform group-hover:translate-x-1">→</span>
              </a>
            </div>
          </div>

          <!-- 未来研究所 -->
          <div class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-rose-50 to-pink-50 p-8 shadow-lg transition-all hover:-translate-y-2 hover:shadow-2xl">
            <div class="absolute -top-4 -right-4 h-24 w-24 rounded-full bg-gradient-to-br from-rose-200/30 to-pink-200/30"></div>
            <div class="relative">
              <div class="mb-6 flex items-center">
                <div class="mr-4 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-rose-500 to-pink-500 text-2xl text-white shadow-lg">
                  🔮
                </div>
                <div>
                  <h4 class="text-xl font-bold text-slate-800">未来研究所</h4>
                  <p class="text-sm text-slate-600">Future Institute</p>
                </div>
              </div>
              <p class="mb-6 leading-relaxed text-slate-600">
                前瞻性思考与趋势预判，探索未来社会发展的多种可能性。
              </p>
              <a href="/future" class="group inline-flex items-center gap-2 font-semibold text-rose-600 transition-all hover:text-rose-700">
                <span>进入研究所</span>
                <span class="transition-transform group-hover:translate-x-1">→</span>
              </a>
            </div>
          </div>

          <!-- 应用开发中心 -->
          <div class="group relative overflow-hidden rounded-xl bg-gradient-to-br from-slate-50 to-gray-50 p-8 shadow-lg transition-all hover:-translate-y-2 hover:shadow-2xl">
            <div class="absolute -top-4 -right-4 h-24 w-24 rounded-full bg-gradient-to-br from-slate-200/30 to-gray-200/30"></div>
            <div class="relative">
              <div class="mb-6 flex items-center">
                <div class="mr-4 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-slate-600 to-gray-600 text-2xl text-white shadow-lg">
                  🛠️
                </div>
                <div>
                  <h4 class="text-xl font-bold text-slate-800">应用开发中心</h4>
                  <p class="text-sm text-slate-600">Development Center</p>
                </div>
              </div>
              <p class="mb-6 leading-relaxed text-slate-600">
                将理论转化为实践，展示创意产品的设计思路与开发过程。
              </p>
              <a href="/products" class="group inline-flex items-center gap-2 font-semibold text-slate-600 transition-all hover:text-slate-700">
                <span>进入中心</span>
                <span class="transition-transform group-hover:translate-x-1">→</span>
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- 研究院统计 -->
      <AcademyStats />

      <!-- 最新内容 -->
      <RecentContent />

      <!-- 标签云展示 -->
      <div class="mb-20">
        <div class="text-center mb-12">
          <h3 class="mb-4 text-3xl font-bold text-slate-800">热门标签</h3>
          <p class="mx-auto max-w-2xl text-lg text-slate-600">
            探索研究院的知识体系，发现感兴趣的主题
          </p>
        </div>
        <div class="mb-8">
          <TagSearch placeholder="搜索感兴趣的标签..." showCategories={true} size="medium" />
        </div>
        <TagCloud maxTags={40} showCount={true} size="medium" interactive={true} />
        <div class="mt-8 text-center">
          <a href="/tags" class="inline-flex items-center gap-2 font-medium text-blue-600 hover:text-blue-800 transition-colors">
            <span>查看所有标签</span>
            <span>→</span>
          </a>
        </div>
      </div>

      <!-- 特色功能 -->
      <div class="relative overflow-hidden rounded-2xl bg-gradient-to-br from-indigo-600 via-purple-600 to-blue-700 p-12 text-white">
        <!-- 背景装饰 -->
        <div class="absolute inset-0 opacity-20">
          <div class="h-full w-full" style="background-image: radial-gradient(circle at 30% 30%, rgba(255,255,255,0.1) 0%, transparent 50%), radial-gradient(circle at 70% 70%, rgba(255,255,255,0.1) 0%, transparent 50%);"></div>
        </div>

        <div class="relative">
          <div class="mb-12 text-center">
            <h3 class="mb-4 text-3xl font-bold">研究院特色</h3>
            <p class="mx-auto max-w-2xl text-lg opacity-90">
              以学术严谨性为基础，融合个人独特视角，构建跨领域知识体系
            </p>
          </div>

          <div class="grid gap-8 md:grid-cols-3">
            <div class="group text-center">
              <div class="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-2xl bg-white/10 backdrop-blur-sm transition-all group-hover:scale-110 group-hover:bg-white/20">
                <BookIcon class="h-10 w-10 text-white" />
              </div>
              <h4 class="mb-4 text-xl font-bold">学术风格</h4>
              <p class="leading-relaxed opacity-90">
                保持专业性但不失可读性，体现学术研究的严谨与深度，让复杂思想变得易于理解
              </p>
            </div>

            <div class="group text-center">
              <div class="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-2xl bg-white/10 backdrop-blur-sm transition-all group-hover:scale-110 group-hover:bg-white/20">
                <LightBulbIcon class="h-10 w-10 text-white" />
              </div>
              <h4 class="mb-4 text-xl font-bold">个人视角</h4>
              <p class="leading-relaxed opacity-90">
                体现独特的思考过程和观点，展示知识创造的真实轨迹，记录思想演进的完整过程
              </p>
            </div>

            <div class="group text-center">
              <div class="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-2xl bg-white/10 backdrop-blur-sm transition-all group-hover:scale-110 group-hover:bg-white/20">
                <UsersIcon class="h-10 w-10 text-white" />
              </div>
              <h4 class="mb-4 text-xl font-bold">跨领域整合</h4>
              <p class="leading-relaxed opacity-90">
                打破学科边界，探索不同领域间的关联和交叉点，构建综合性的知识框架
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>
</Layout>
