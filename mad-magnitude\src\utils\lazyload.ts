/**
 * 图片懒加载工具
 */

export interface LazyLoadOptions {
  root?: Element | null;
  rootMargin?: string;
  threshold?: number | number[];
  loadingClass?: string;
  loadedClass?: string;
  errorClass?: string;
}

export class LazyLoader {
  private observer: IntersectionObserver | null = null;
  private options: LazyLoadOptions;

  constructor(options: LazyLoadOptions = {}) {
    this.options = {
      root: null,
      rootMargin: '50px',
      threshold: 0.1,
      loadingClass: 'lazy-loading',
      loadedClass: 'lazy-loaded',
      errorClass: 'lazy-error',
      ...options,
    };

    this.init();
  }

  private init() {
    if (!('IntersectionObserver' in window)) {
      // 如果不支持 IntersectionObserver，直接加载所有图片
      this.loadAllImages();
      return;
    }

    this.observer = new IntersectionObserver(this.handleIntersection.bind(this), {
      root: this.options.root,
      rootMargin: this.options.rootMargin,
      threshold: this.options.threshold,
    });

    this.observeImages();
  }

  private handleIntersection(entries: IntersectionObserverEntry[]) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target as HTMLImageElement;
        this.loadImage(img);
        this.observer?.unobserve(img);
      }
    });
  }

  private loadImage(img: HTMLImageElement) {
    const src = img.dataset.src;
    const srcset = img.dataset.srcset;

    if (!src) return;

    img.classList.add(this.options.loadingClass!);

    const imageLoader = new Image();

    imageLoader.onload = () => {
      img.src = src;
      if (srcset) {
        img.srcset = srcset;
      }
      img.classList.remove(this.options.loadingClass!);
      img.classList.add(this.options.loadedClass!);
    };

    imageLoader.onerror = () => {
      img.classList.remove(this.options.loadingClass!);
      img.classList.add(this.options.errorClass!);
    };

    imageLoader.src = src;
  }

  private observeImages() {
    const images = document.querySelectorAll('img[data-src]');
    images.forEach(img => {
      this.observer?.observe(img);
    });
  }

  private loadAllImages() {
    const images = document.querySelectorAll('img[data-src]') as NodeListOf<HTMLImageElement>;
    images.forEach(img => {
      this.loadImage(img);
    });
  }

  public destroy() {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
  }

  public refresh() {
    this.observeImages();
  }
}

// 自动初始化懒加载
export function initLazyLoad(options?: LazyLoadOptions) {
  if (typeof window !== 'undefined') {
    const lazyLoader = new LazyLoader(options);

    // 页面加载完成后刷新观察器
    document.addEventListener('DOMContentLoaded', () => {
      lazyLoader.refresh();
    });

    return lazyLoader;
  }
  return null;
}

// 预加载关键图片
export function preloadImage(src: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = reject;
    img.src = src;
  });
}

// 预加载多个图片
export async function preloadImages(srcs: string[]): Promise<void[]> {
  return Promise.all(srcs.map(preloadImage));
}
