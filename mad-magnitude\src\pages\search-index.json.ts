import { getCollection } from 'astro:content';
import { buildSearchIndex } from '../utils/content';

export async function GET() {
  const research = await getCollection('research');
  const reflections = await getCollection('reflections');

  const allPosts = [...research, ...reflections];
  const searchIndex = buildSearchIndex(allPosts);

  return new Response(JSON.stringify(searchIndex), {
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'public, max-age=3600', // 缓存1小时
    },
  });
}
