import { d as contentManager } from "../../../assets/utils.bIDOeBqD.js";
import { i } from "../../../assets/vendor-astro.Dc6apy9i.js";
const prerender = false;
const GET = async ({ url }) => {
  try {
    const searchParams = new URL(url).searchParams;
    const filter = {};
    if (searchParams.get("collection")) {
      filter.collection = searchParams.get("collection");
    }
    if (searchParams.get("draft")) {
      filter.draft = searchParams.get("draft") === "true";
    }
    if (searchParams.get("featured")) {
      filter.featured = searchParams.get("featured") === "true";
    }
    if (searchParams.get("tags")) {
      filter.tags = searchParams.get("tags").split(",");
    }
    if (searchParams.get("author")) {
      filter.author = searchParams.get("author");
    }
    if (searchParams.get("search")) {
      filter.search = searchParams.get("search");
    }
    if (searchParams.get("startDate") && searchParams.get("endDate")) {
      filter.dateRange = {
        start: new Date(searchParams.get("startDate")),
        end: new Date(searchParams.get("endDate"))
      };
    }
    const content = await contentManager.getAllContent(filter);
    const page2 = parseInt(searchParams.get("page") || "1", 10);
    const limit = parseInt(searchParams.get("limit") || "20", 10);
    const offset = (page2 - 1) * limit;
    const paginatedContent = content.slice(offset, offset + limit);
    const response = {
      content: paginatedContent.map((item) => ({
        id: item.id,
        collection: item.collection,
        slug: item.slug,
        title: item.title,
        description: item.description,
        publishDate: item.publishDate.toISOString(),
        updateDate: item.updateDate?.toISOString(),
        draft: item.draft,
        featured: item.featured,
        tags: item.tags,
        author: item.author,
        // 不返回完整内容，只返回摘要
        contentPreview: item.content.substring(0, 200) + (item.content.length > 200 ? "..." : "")
      })),
      pagination: {
        page: page2,
        limit,
        total: content.length,
        totalPages: Math.ceil(content.length / limit),
        hasNext: offset + limit < content.length,
        hasPrev: page2 > 1
      }
    };
    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        "Cache-Control": "public, max-age=300"
        // 缓存5分钟
      }
    });
  } catch (error) {
    console.error("内容列表 API 错误:", error);
    return new Response(
      JSON.stringify({
        error: "Internal server error",
        code: "INTERNAL_ERROR"
      }),
      {
        status: 500,
        headers: {
          "Content-Type": "application/json"
        }
      }
    );
  }
};
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({ __proto__: null, GET, prerender }, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  i as renderers
};
