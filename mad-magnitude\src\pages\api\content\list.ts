/**
 * 内容列表 API
 * 获取内容列表，支持筛选和分页
 */
import type { APIRoute } from 'astro';
import { contentManager, type ContentFilter } from '../../../utils/contentManager';

export const prerender = false;

export const GET: APIRoute = async ({ url }) => {
  try {
    const searchParams = new URL(url).searchParams;

    // 构建筛选器
    const filter: ContentFilter = {};

    if (searchParams.get('collection')) {
      filter.collection = searchParams.get('collection')!;
    }

    if (searchParams.get('draft')) {
      filter.draft = searchParams.get('draft') === 'true';
    }

    if (searchParams.get('featured')) {
      filter.featured = searchParams.get('featured') === 'true';
    }

    if (searchParams.get('tags')) {
      filter.tags = searchParams.get('tags')!.split(',');
    }

    if (searchParams.get('author')) {
      filter.author = searchParams.get('author')!;
    }

    if (searchParams.get('search')) {
      filter.search = searchParams.get('search')!;
    }

    // 日期范围筛选
    if (searchParams.get('startDate') && searchParams.get('endDate')) {
      filter.dateRange = {
        start: new Date(searchParams.get('startDate')!),
        end: new Date(searchParams.get('endDate')!),
      };
    }

    // 获取内容列表
    const content = await contentManager.getAllContent(filter);

    // 分页处理
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '20', 10);
    const offset = (page - 1) * limit;

    const paginatedContent = content.slice(offset, offset + limit);

    // 构建响应
    const response = {
      content: paginatedContent.map(item => ({
        id: item.id,
        collection: item.collection,
        slug: item.slug,
        title: item.title,
        description: item.description,
        publishDate: item.publishDate.toISOString(),
        updateDate: item.updateDate?.toISOString(),
        draft: item.draft,
        featured: item.featured,
        tags: item.tags,
        author: item.author,
        // 不返回完整内容，只返回摘要
        contentPreview: item.content.substring(0, 200) + (item.content.length > 200 ? '...' : ''),
      })),
      pagination: {
        page,
        limit,
        total: content.length,
        totalPages: Math.ceil(content.length / limit),
        hasNext: offset + limit < content.length,
        hasPrev: page > 1,
      },
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=300', // 缓存5分钟
      },
    });
  } catch (error) {
    console.error('内容列表 API 错误:', error);

    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        code: 'INTERNAL_ERROR',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};
