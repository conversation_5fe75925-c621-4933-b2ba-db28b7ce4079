---
/**
 * Enhanced Icon component for Pennfly Private Academy
 * Provides consistent icon rendering with accessibility and theming support
 */

export interface Props {
  name: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  color?:
    | 'current'
    | 'primary'
    | 'secondary'
    | 'tertiary'
    | 'brand'
    | 'success'
    | 'warning'
    | 'error'
    | 'info';
  strokeWidth?: '1' | '1.5' | '2' | '2.5' | '3';
  filled?: boolean;
  interactive?: boolean;
  href?: string;
  target?: '_blank' | '_self' | '_parent' | '_top';
  class?: string;
  'aria-label'?: string;
  'aria-hidden'?: boolean;
}

const {
  name,
  size = 'md',
  color = 'current',
  strokeWidth = '2',
  filled = false,
  interactive = false,
  href,
  target,
  class: className = '',
  'aria-label': ariaLabel,
  'aria-hidden': ariaHidden = !ariaLabel,
  ...rest
} = Astro.props;

// Size configurations
const sizeClasses = {
  xs: 'w-3 h-3',
  sm: 'w-4 h-4',
  md: 'w-5 h-5',
  lg: 'w-6 h-6',
  xl: 'w-8 h-8',
  '2xl': 'w-10 h-10',
};

// Color configurations using theme-aware classes
const colorClasses = {
  current: 'text-current',
  primary: 'text-theme-fg-primary',
  secondary: 'text-theme-fg-secondary',
  tertiary: 'text-theme-fg-tertiary',
  brand: 'text-theme-brand-primary',
  success: 'text-theme-semantic-success',
  warning: 'text-theme-semantic-warning',
  error: 'text-theme-semantic-error',
  info: 'text-theme-semantic-info',
};

// Interactive states
const interactiveClasses =
  interactive || href
    ? `
  transition-all duration-200 ease-in-out
  hover:opacity-80 hover:scale-110
  active:scale-95
  focus:outline-none focus:ring-2 focus:ring-theme-interactive-focus focus:ring-offset-1 focus:rounded-sm
  cursor-pointer
`
    : '';

// Base classes
const baseClasses = `
  inline-block
  ${sizeClasses[size]}
  ${colorClasses[color]}
  ${interactiveClasses}
`;

const combinedClasses = `
  ${baseClasses}
  ${className}
`
  .trim()
  .replace(/\s+/g, ' ');

// Icon definitions - using Heroicons as the base icon set
const icons = {
  // Navigation
  'chevron-left': 'M15.75 19.5L8.25 12l7.5-7.5',
  'chevron-right': 'M8.25 4.5l7.5 7.5-7.5 7.5',
  'chevron-up': 'M4.5 15.75l7.5-7.5 7.5 7.5',
  'chevron-down': 'M19.5 8.25l-7.5 7.5-7.5-7.5',
  'arrow-left': 'M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18',
  'arrow-right': 'M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3',
  'arrow-up': 'M4.5 10.5L12 3m0 0l7.5 7.5M12 3v18',
  'arrow-down': 'M19.5 13.5L12 21m0 0l-7.5-7.5M12 21V3',

  // Actions
  plus: 'M12 4.5v15m7.5-7.5h-15',
  minus: 'M19.5 12h-15',
  x: 'M6 18L18 6M6 6l12 12',
  check: 'M4.5 12.75l6 6 9-13.5',
  search: 'M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z',
  edit: 'M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10',
  trash:
    'M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0',
  copy: 'M15.666 3.888A2.25 2.25 0 0013.5 2.25h-3c-1.03 0-1.9.693-2.166 1.638m7.332 0c.055.194.084.4.084.612v0a.75.75 0 01-.75.75H9a.75.75 0 01-.75-.75v0c0-.212.03-.418.084-.612m7.332 0c.646.049 1.288.11 1.927.184 1.1.128 1.907 1.077 1.907 2.185V19.5a2.25 2.25 0 01-2.25 2.25H6.75A2.25 2.25 0 014.5 19.5V6.257c0-1.108.806-2.057 1.907-2.185a48.208 48.208 0 011.927-.184',

  // Interface
  menu: 'M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5',
  'dots-vertical':
    'M12 6.75a.75.75 0 110-1.5.75.75 0 010 1.5zM12 12.75a.75.75 0 110-1.5.75.75 0 010 1.5zM12 18.75a.75.75 0 110-1.5.75.75 0 010 1.5z',
  'dots-horizontal':
    'M6.75 12a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM12.75 12a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM18.75 12a.75.75 0 11-1.5 0 .75.75 0 011.5 0z',
  cog: 'M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z M15 12a3 3 0 11-6 0 3 3 0 016 0z',

  // Content
  document:
    'M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z',
  folder:
    'M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25H11.69z',
  book: 'M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25',
  tag: 'M9.568 3H5.25A2.25 2.25 0 003 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 005.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 009.568 3z M6.75 7.5a.75.75 0 11-1.5 0 .75.75 0 011.5 0z',

  // Social
  heart:
    'M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12z',
  star: 'M11.48 3.499a.562.562 0 011.04 0l2.125 5.111a.563.563 0 00.475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 00-.182.557l1.285 5.385a.562.562 0 01-.84.61l-4.725-2.885a.563.563 0 00-.586 0L6.982 20.54a.562.562 0 01-.84-.61l1.285-5.386a.562.562 0 00-.182-.557l-4.204-3.602a.562.562 0 01.321-.988l5.518-.442a.563.563 0 00.475-.345L11.48 3.5z',
  share:
    'M7.217 10.907a2.25 2.25 0 100 2.186m0-2.186c.18.324.283.696.283 1.093s-.103.77-.283 1.093m0-2.186l9.566-5.314m-9.566 7.5l9.566 5.314m0 0a2.25 2.25 0 103.935 2.186 2.25 2.25 0 00-3.935-2.186zm0-12.814a2.25 2.25 0 103.933-2.185 2.25 2.25 0 00-3.933 2.185z',

  // Status
  info: 'M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z',
  warning:
    'M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z',
  error: 'M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z',
  success: 'M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z',

  // Theme
  sun: 'M12 3v2.25m6.364.386l-1.591 1.591M21 12h-2.25m-.386 6.364l-1.591-1.591M12 18.75V21m-4.773-4.227l-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636a9 9 0 1012.728 0z',
  moon: 'M21.752 15.002A9.718 9.718 0 0118 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 003 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 009.002-5.998z',
  computer:
    'M9 17.25v1.007a3 3 0 01-.879 2.122L7.5 21h9l-.621-.621A3 3 0 0115 18.257V17.25m6-12V15a2.25 2.25 0 01-2.25 2.25H5.25A2.25 2.25 0 013 15V5.25m18 0A2.25 2.25 0 0018.75 3H5.25A2.25 2.25 0 003 5.25m18 0V12a2.25 2.25 0 01-2.25 2.25H5.25A2.25 2.25 0 013 12V5.25',
};

const iconPath = icons[name as keyof typeof icons];

// Determine if this should render as a link or span
const isLink = href && !rest.disabled;
const Component = isLink ? 'a' : 'span';

// Prepare props for the component
const componentProps = isLink ? { href, target } : {};

// Add role for interactive non-link icons
const accessibilityProps = interactive && !isLink ? { role: 'button', tabindex: '0' } : {};
---

{
  iconPath ? (
    <Component
      class={combinedClasses}
      aria-label={ariaLabel}
      aria-hidden={ariaHidden}
      {...componentProps}
      {...accessibilityProps}
      {...rest}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill={filled ? 'currentColor' : 'none'}
        viewBox="0 0 24 24"
        stroke-width={strokeWidth}
        stroke="currentColor"
        class="h-full w-full"
      >
        <path stroke-linecap="round" stroke-linejoin="round" d={iconPath} />
      </svg>

      {isLink && target === '_blank' && <span class="sr-only">(opens in new tab)</span>}
    </Component>
  ) : (
    <span
      class={`${combinedClasses} text-theme-semantic-error`}
      aria-label={`Unknown icon: ${name}`}
      title={`Icon "${name}" not found`}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="2"
        stroke="currentColor"
        class="h-full w-full"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z"
        />
      </svg>
    </span>
  )
}

<style>
  /* Ensure smooth transitions respect user preferences */
  @media (prefers-reduced-motion: reduce) {
    span,
    a {
      transition: none !important;
      transform: none !important;
    }
  }

  /* Enhanced focus styles for better accessibility */
  span[role='button']:focus-visible,
  a:focus-visible {
    outline: 2px solid var(--color-interactive-focus);
    outline-offset: 2px;
  }

  /* Keyboard interaction for interactive icons */
  span[role='button']:focus,
  span[role='button']:hover {
    opacity: 0.8;
    transform: scale(1.1);
  }

  span[role='button']:active {
    transform: scale(0.95);
  }

  /* SVG optimization */
  svg {
    display: block;
    flex-shrink: 0;
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    svg {
      stroke-width: 3;
    }
  }

  /* Print styles */
  @media print {
    span,
    a {
      color: black !important;
      transform: none !important;
    }
  }

  /* Screen reader only text */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }
</style>
