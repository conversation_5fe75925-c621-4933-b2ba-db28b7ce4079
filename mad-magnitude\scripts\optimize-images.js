#!/usr/bin/env node

/**
 * 图片优化脚本
 * 分析项目中的图片文件并提供优化建议
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class ImageOptimizer {
  constructor() {
    this.srcPath = path.join(__dirname, '../src');
    this.publicPath = path.join(__dirname, '../public');
    this.supportedFormats = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.avif', '.svg'];
    this.results = {
      files: [],
      totalSizeBefore: 0,
      totalSizeAfter: 0,
    };
  }

  /**
   * 格式化文件大小
   */
  formatSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 检查是否为支持的图片格式
   */
  isSupportedImage(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    return this.supportedFormats.includes(ext);
  }

  /**
   * 分析单个图片文件
   */
  analyzeImage(filePath) {
    try {
      const stats = fs.statSync(filePath);
      const ext = path.extname(filePath).toLowerCase();

      return {
        path: filePath,
        name: path.basename(filePath),
        format: ext,
        size: stats.size,
        relativePath: path.relative(process.cwd(), filePath),
      };
    } catch (error) {
      console.warn(`无法分析图片 ${filePath}:`, error.message);
      return null;
    }
  }

  /**
   * 扫描目录中的图片文件
   */
  scanDirectory(dirPath, basePath = '') {
    try {
      if (!fs.existsSync(dirPath)) {
        return;
      }

      const items = fs.readdirSync(dirPath);

      for (const item of items) {
        const fullPath = path.join(dirPath, item);
        const relativeFilePath = path.join(basePath, item);

        try {
          const stats = fs.statSync(fullPath);

          if (stats.isDirectory()) {
            // 跳过 node_modules 和 .git 等目录
            if (!['node_modules', '.git', '.astro', 'dist'].includes(item)) {
              this.scanDirectory(fullPath, relativeFilePath);
            }
          } else if (this.isSupportedImage(fullPath)) {
            const imageInfo = this.analyzeImage(fullPath);
            if (imageInfo) {
              this.results.files.push(imageInfo);
              this.results.totalSizeBefore += imageInfo.size;
            }
          }
        } catch (error) {
          console.warn(`无法访问 ${fullPath}:`, error.message);
        }
      }
    } catch (error) {
      console.warn(`无法扫描目录 ${dirPath}:`, error.message);
    }
  }

  /**
   * 生成优化建议
   */
  generateOptimizationSuggestions() {
    const suggestions = [];

    // 按大小排序
    const largeImages = this.results.files
      .filter(file => file.size > 100 * 1024)
      .sort((a, b) => b.size - a.size);

    if (largeImages.length > 0) {
      suggestions.push({
        type: 'size_optimization',
        title: '大文件优化建议',
        description: `发现 ${largeImages.length} 个大于 100KB 的图片文件`,
        files: largeImages.slice(0, 10),
        recommendations: [
          '使用图片压缩工具减小文件大小',
          '考虑转换为 WebP 或 AVIF 格式',
          '为不同设备提供不同尺寸的图片',
          '使用 Astro 的 <Image> 组件进行自动优化',
        ],
      });
    }

    // 格式优化建议
    const oldFormatImages = this.results.files.filter(file =>
      ['.jpg', '.jpeg', '.png'].includes(file.format)
    );

    if (oldFormatImages.length > 0) {
      suggestions.push({
        type: 'format_optimization',
        title: '格式优化建议',
        description: `发现 ${oldFormatImages.length} 个使用传统格式的图片`,
        files: oldFormatImages.slice(0, 5),
        recommendations: [
          '转换为 WebP 格式可减少 25-35% 的文件大小',
          '转换为 AVIF 格式可减少 50% 的文件大小',
          '在 Astro 配置中启用现代格式支持',
        ],
      });
    }

    return suggestions;
  }

  /**
   * 打印分析结果
   */
  printResults() {
    console.log('\n🖼️  图片优化分析报告');
    console.log('='.repeat(50));

    // 总体统计
    console.log(`\n📊 总体统计:`);
    console.log(`   图片文件数量: ${this.results.files.length}`);
    console.log(`   总大小: ${this.formatSize(this.results.totalSizeBefore)}`);

    // 按格式统计
    const formatStats = {};
    this.results.files.forEach(file => {
      const format = file.format;
      if (!formatStats[format]) {
        formatStats[format] = { count: 0, size: 0 };
      }
      formatStats[format].count++;
      formatStats[format].size += file.size;
    });

    console.log(`\n📁 格式统计:`);
    Object.entries(formatStats).forEach(([format, stats]) => {
      console.log(
        `   ${format.toUpperCase()}: ${stats.count} 个文件, ${this.formatSize(stats.size)}`
      );
    });

    // 最大的图片文件
    const largestFiles = [...this.results.files].sort((a, b) => b.size - a.size).slice(0, 10);

    if (largestFiles.length > 0) {
      console.log(`\n📈 最大的图片文件:`);
      largestFiles.forEach((file, index) => {
        console.log(
          `   ${index + 1}. ${file.relativePath} - ${this.formatSize(file.size)} (${file.format.toUpperCase()})`
        );
      });
    }

    // 优化建议
    const suggestions = this.generateOptimizationSuggestions();
    if (suggestions.length > 0) {
      console.log(`\n💡 优化建议:`);
      suggestions.forEach(suggestion => {
        console.log(`\n   📌 ${suggestion.title}`);
        console.log(`      ${suggestion.description}`);
        console.log(`      建议:`);
        suggestion.recommendations.forEach(rec => {
          console.log(`        • ${rec}`);
        });

        if (suggestion.files.length > 0) {
          console.log(`      相关文件 (显示前${Math.min(5, suggestion.files.length)}个):`);
          suggestion.files.slice(0, 5).forEach(file => {
            console.log(`        - ${file.relativePath} (${this.formatSize(file.size)})`);
          });
        }
      });
    }

    console.log('\n='.repeat(50));
  }

  /**
   * 运行分析
   */
  async run() {
    try {
      console.log('🚀 开始分析图片文件...');

      // 扫描 src 目录
      console.log('📁 扫描 src 目录...');
      this.scanDirectory(this.srcPath, 'src');

      // 扫描 public 目录
      console.log('📁 扫描 public 目录...');
      this.scanDirectory(this.publicPath, 'public');

      // 打印结果
      this.printResults();

      console.log('✅ 图片分析完成');

      if (this.results.files.length === 0) {
        console.log('ℹ️  未找到图片文件');
      }
    } catch (error) {
      console.error('❌ 分析过程中出现错误:', error.message);
      process.exit(1);
    }
  }
}

// 运行分析
const optimizer = new ImageOptimizer();
optimizer.run();
