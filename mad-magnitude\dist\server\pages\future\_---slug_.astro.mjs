import { c as createAstro, a as createComponent, d as renderTemplate, j as defineScriptVars, k as renderSlot, b as addAttribute, m as maybeRenderHead, h as renderComponent } from "../../assets/vendor-astro.Dc6apy9i.js";
import { i } from "../../assets/vendor-astro.Dc6apy9i.js";
import "kleur/colors";
import { h as getEntry } from "../../assets/utils.bIDOeBqD.js";
import { $ as $$Layout } from "../../assets/Layout.DbxDGMbZ.js";
import "clsx";
/* empty css                                    */
var __freeze = Object.freeze;
var __defProp = Object.defineProperty;
var __template = (cooked, raw) => __freeze(__defProp(cooked, "raw", { value: __freeze(raw || cooked.slice()) }));
var _a;
const $$Astro$2 = createAstro("https://pennfly.com");
const $$AcademicContent = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$2, $$props, $$slots);
  Astro2.self = $$AcademicContent;
  const {
    showToc = true,
    showProgress = true,
    enableMermaid = false,
    class: className = ""
  } = Astro2.props;
  return renderTemplate(_a || (_a = __template(["", "<div", " data-astro-cid-uqmtuu4b> <!-- 阅读进度指示器 --> ", " <!-- 目录 --> ", ' <!-- 文章内容 --> <div class="content-body" data-astro-cid-uqmtuu4b> ', " </div> </div> <!-- 学术样式 -->  <script>(function(){", "\n  // 学术内容增强脚本\n  class AcademicContentEnhancer {\n    constructor() {\n      this.showToc = showToc;\n      this.showProgress = showProgress;\n      this.enableMermaid = enableMermaid;\n      this.init();\n    }\n\n    init() {\n      if (this.showToc) {\n        this.generateTableOfContents();\n      }\n      \n      if (this.showProgress) {\n        this.initReadingProgress();\n      }\n      \n      if (this.enableMermaid) {\n        this.initMermaid();\n      }\n      \n      this.enhanceCodeBlocks();\n      this.addCopyButtons();\n      this.initSmoothScrolling();\n    }\n\n    // 生成目录\n    generateTableOfContents() {\n      const headings = document.querySelectorAll('.content-body h1, .content-body h2, .content-body h3, .content-body h4');\n      const tocContent = document.getElementById('toc-content');\n      const tocContainer = document.getElementById('table-of-contents');\n      \n      if (!headings.length || !tocContent) {\n        if (tocContainer) {\n          tocContainer.style.display = 'none';\n        }\n        return;\n      }\n\n      const tocList = document.createElement('ul');\n      tocList.className = 'toc-list';\n\n      headings.forEach((heading, index) => {\n        // 为标题添加ID\n        if (!heading.id) {\n          heading.id = `heading-${index}`;\n        }\n\n        const li = document.createElement('li');\n        const a = document.createElement('a');\n        a.href = `#${heading.id}`;\n        a.textContent = heading.textContent;\n        a.className = `toc-level-${heading.tagName.toLowerCase()}`;\n        \n        li.appendChild(a);\n        tocList.appendChild(li);\n      });\n\n      tocContent.appendChild(tocList);\n    }\n\n    // 阅读进度指示器\n    initReadingProgress() {\n      const progressBar = document.getElementById('reading-progress-bar');\n      if (!progressBar) return;\n\n      const updateProgress = () => {\n        const scrollTop = window.pageYOffset;\n        const docHeight = document.documentElement.scrollHeight - window.innerHeight;\n        const scrollPercent = (scrollTop / docHeight) * 100;\n        progressBar.style.width = `${Math.min(scrollPercent, 100)}%`;\n      };\n\n      window.addEventListener('scroll', updateProgress);\n      updateProgress(); // 初始化\n    }\n\n    // Mermaid 图表支持\n    async initMermaid() {\n      try {\n        const mermaidBlocks = document.querySelectorAll('pre code.language-mermaid');\n        if (mermaidBlocks.length === 0) return;\n\n        // 动态导入 Mermaid\n        const mermaid = await import('mermaid');\n        mermaid.default.initialize({\n          startOnLoad: false,\n          theme: 'default',\n          securityLevel: 'loose',\n        });\n\n        mermaidBlocks.forEach((block, index) => {\n          const code = block.textContent;\n          const container = document.createElement('div');\n          container.className = 'mermaid-container';\n          container.id = `mermaid-${index}`;\n          \n          // 替换代码块\n          block.parentElement.replaceWith(container);\n          \n          // 渲染图表\n          mermaid.default.render(`mermaid-svg-${index}`, code).then(({ svg }) => {\n            container.innerHTML = svg;\n          });\n        });\n      } catch (error) {\n        console.warn('Mermaid initialization failed:', error);\n      }\n    }\n\n    // 增强代码块\n    enhanceCodeBlocks() {\n      const codeBlocks = document.querySelectorAll('pre code');\n      \n      codeBlocks.forEach((block) => {\n        const pre = block.parentElement;\n        if (!pre) return;\n\n        // 添加语言标签\n        const className = block.className;\n        const languageMatch = className.match(/language-(\\w+)/);\n        if (languageMatch) {\n          pre.setAttribute('data-language', languageMatch[1]);\n        }\n\n        // 添加行号（可选）\n        if (block.textContent && block.textContent.split('\\n').length > 5) {\n          this.addLineNumbers(pre, block);\n        }\n      });\n    }\n\n    // 添加行号\n    addLineNumbers(pre, code) {\n      const lines = code.textContent.split('\\n');\n      const lineNumbersWrapper = document.createElement('span');\n      lineNumbersWrapper.className = 'line-numbers-rows';\n      \n      lines.forEach((_, index) => {\n        const lineNumber = document.createElement('span');\n        lineNumber.textContent = (index + 1).toString();\n        lineNumbersWrapper.appendChild(lineNumber);\n      });\n      \n      pre.classList.add('line-numbers');\n      pre.appendChild(lineNumbersWrapper);\n    }\n\n    // 添加复制按钮\n    addCopyButtons() {\n      const codeBlocks = document.querySelectorAll('pre');\n      \n      codeBlocks.forEach((pre) => {\n        const button = document.createElement('button');\n        button.className = 'copy-button absolute top-2 right-2 bg-gray-700 text-white px-2 py-1 rounded text-xs hover:bg-gray-600 transition-colors';\n        button.textContent = '复制';\n        button.style.position = 'absolute';\n        button.style.top = '8px';\n        button.style.right = '8px';\n        \n        button.addEventListener('click', async () => {\n          const code = pre.querySelector('code');\n          if (code) {\n            try {\n              await navigator.clipboard.writeText(code.textContent);\n              button.textContent = '已复制';\n              setTimeout(() => {\n                button.textContent = '复制';\n              }, 2000);\n            } catch (err) {\n              console.error('复制失败:', err);\n            }\n          }\n        });\n        \n        pre.style.position = 'relative';\n        pre.appendChild(button);\n      });\n    }\n\n    // 平滑滚动\n    initSmoothScrolling() {\n      const links = document.querySelectorAll('a[href^=\"#\"]');\n      \n      links.forEach(link => {\n        link.addEventListener('click', (e) => {\n          e.preventDefault();\n          const targetId = link.getAttribute('href').substring(1);\n          const targetElement = document.getElementById(targetId);\n          \n          if (targetElement) {\n            targetElement.scrollIntoView({\n              behavior: 'smooth',\n              block: 'start'\n            });\n          }\n        });\n      });\n    }\n  }\n\n  // 初始化学术内容增强\n  document.addEventListener('DOMContentLoaded', () => {\n    new AcademicContentEnhancer();\n  });\n})();<\/script>"], ["", "<div", " data-astro-cid-uqmtuu4b> <!-- 阅读进度指示器 --> ", " <!-- 目录 --> ", ' <!-- 文章内容 --> <div class="content-body" data-astro-cid-uqmtuu4b> ', " </div> </div> <!-- 学术样式 -->  <script>(function(){", "\n  // 学术内容增强脚本\n  class AcademicContentEnhancer {\n    constructor() {\n      this.showToc = showToc;\n      this.showProgress = showProgress;\n      this.enableMermaid = enableMermaid;\n      this.init();\n    }\n\n    init() {\n      if (this.showToc) {\n        this.generateTableOfContents();\n      }\n      \n      if (this.showProgress) {\n        this.initReadingProgress();\n      }\n      \n      if (this.enableMermaid) {\n        this.initMermaid();\n      }\n      \n      this.enhanceCodeBlocks();\n      this.addCopyButtons();\n      this.initSmoothScrolling();\n    }\n\n    // 生成目录\n    generateTableOfContents() {\n      const headings = document.querySelectorAll('.content-body h1, .content-body h2, .content-body h3, .content-body h4');\n      const tocContent = document.getElementById('toc-content');\n      const tocContainer = document.getElementById('table-of-contents');\n      \n      if (!headings.length || !tocContent) {\n        if (tocContainer) {\n          tocContainer.style.display = 'none';\n        }\n        return;\n      }\n\n      const tocList = document.createElement('ul');\n      tocList.className = 'toc-list';\n\n      headings.forEach((heading, index) => {\n        // 为标题添加ID\n        if (!heading.id) {\n          heading.id = \\`heading-\\${index}\\`;\n        }\n\n        const li = document.createElement('li');\n        const a = document.createElement('a');\n        a.href = \\`#\\${heading.id}\\`;\n        a.textContent = heading.textContent;\n        a.className = \\`toc-level-\\${heading.tagName.toLowerCase()}\\`;\n        \n        li.appendChild(a);\n        tocList.appendChild(li);\n      });\n\n      tocContent.appendChild(tocList);\n    }\n\n    // 阅读进度指示器\n    initReadingProgress() {\n      const progressBar = document.getElementById('reading-progress-bar');\n      if (!progressBar) return;\n\n      const updateProgress = () => {\n        const scrollTop = window.pageYOffset;\n        const docHeight = document.documentElement.scrollHeight - window.innerHeight;\n        const scrollPercent = (scrollTop / docHeight) * 100;\n        progressBar.style.width = \\`\\${Math.min(scrollPercent, 100)}%\\`;\n      };\n\n      window.addEventListener('scroll', updateProgress);\n      updateProgress(); // 初始化\n    }\n\n    // Mermaid 图表支持\n    async initMermaid() {\n      try {\n        const mermaidBlocks = document.querySelectorAll('pre code.language-mermaid');\n        if (mermaidBlocks.length === 0) return;\n\n        // 动态导入 Mermaid\n        const mermaid = await import('mermaid');\n        mermaid.default.initialize({\n          startOnLoad: false,\n          theme: 'default',\n          securityLevel: 'loose',\n        });\n\n        mermaidBlocks.forEach((block, index) => {\n          const code = block.textContent;\n          const container = document.createElement('div');\n          container.className = 'mermaid-container';\n          container.id = \\`mermaid-\\${index}\\`;\n          \n          // 替换代码块\n          block.parentElement.replaceWith(container);\n          \n          // 渲染图表\n          mermaid.default.render(\\`mermaid-svg-\\${index}\\`, code).then(({ svg }) => {\n            container.innerHTML = svg;\n          });\n        });\n      } catch (error) {\n        console.warn('Mermaid initialization failed:', error);\n      }\n    }\n\n    // 增强代码块\n    enhanceCodeBlocks() {\n      const codeBlocks = document.querySelectorAll('pre code');\n      \n      codeBlocks.forEach((block) => {\n        const pre = block.parentElement;\n        if (!pre) return;\n\n        // 添加语言标签\n        const className = block.className;\n        const languageMatch = className.match(/language-(\\\\w+)/);\n        if (languageMatch) {\n          pre.setAttribute('data-language', languageMatch[1]);\n        }\n\n        // 添加行号（可选）\n        if (block.textContent && block.textContent.split('\\\\n').length > 5) {\n          this.addLineNumbers(pre, block);\n        }\n      });\n    }\n\n    // 添加行号\n    addLineNumbers(pre, code) {\n      const lines = code.textContent.split('\\\\n');\n      const lineNumbersWrapper = document.createElement('span');\n      lineNumbersWrapper.className = 'line-numbers-rows';\n      \n      lines.forEach((_, index) => {\n        const lineNumber = document.createElement('span');\n        lineNumber.textContent = (index + 1).toString();\n        lineNumbersWrapper.appendChild(lineNumber);\n      });\n      \n      pre.classList.add('line-numbers');\n      pre.appendChild(lineNumbersWrapper);\n    }\n\n    // 添加复制按钮\n    addCopyButtons() {\n      const codeBlocks = document.querySelectorAll('pre');\n      \n      codeBlocks.forEach((pre) => {\n        const button = document.createElement('button');\n        button.className = 'copy-button absolute top-2 right-2 bg-gray-700 text-white px-2 py-1 rounded text-xs hover:bg-gray-600 transition-colors';\n        button.textContent = '复制';\n        button.style.position = 'absolute';\n        button.style.top = '8px';\n        button.style.right = '8px';\n        \n        button.addEventListener('click', async () => {\n          const code = pre.querySelector('code');\n          if (code) {\n            try {\n              await navigator.clipboard.writeText(code.textContent);\n              button.textContent = '已复制';\n              setTimeout(() => {\n                button.textContent = '复制';\n              }, 2000);\n            } catch (err) {\n              console.error('复制失败:', err);\n            }\n          }\n        });\n        \n        pre.style.position = 'relative';\n        pre.appendChild(button);\n      });\n    }\n\n    // 平滑滚动\n    initSmoothScrolling() {\n      const links = document.querySelectorAll('a[href^=\"#\"]');\n      \n      links.forEach(link => {\n        link.addEventListener('click', (e) => {\n          e.preventDefault();\n          const targetId = link.getAttribute('href').substring(1);\n          const targetElement = document.getElementById(targetId);\n          \n          if (targetElement) {\n            targetElement.scrollIntoView({\n              behavior: 'smooth',\n              block: 'start'\n            });\n          }\n        });\n      });\n    }\n  }\n\n  // 初始化学术内容增强\n  document.addEventListener('DOMContentLoaded', () => {\n    new AcademicContentEnhancer();\n  });\n})();<\/script>"])), maybeRenderHead(), addAttribute(`academic-content prose prose-lg max-w-none ${className}`, "class"), showProgress && renderTemplate`<div class="reading-progress" data-astro-cid-uqmtuu4b> <div class="progress-bar" id="reading-progress-bar" data-astro-cid-uqmtuu4b></div> </div>`, showToc && renderTemplate`<div class="table-of-contents" id="table-of-contents" data-astro-cid-uqmtuu4b> <h3 data-astro-cid-uqmtuu4b>目录</h3> <div id="toc-content" data-astro-cid-uqmtuu4b> <!-- 目录内容将通过JavaScript生成 --> </div> </div>`, renderSlot($$result, $$slots["default"]), defineScriptVars({ showToc, showProgress, enableMermaid }));
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/components/academic/AcademicContent.astro", void 0);
const $$Astro$1 = createAstro("https://pennfly.com");
const $$AcademicLayout = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$1, $$props, $$slots);
  Astro2.self = $$AcademicLayout;
  const {
    title,
    description,
    showToc = true,
    showProgress = true,
    enableMermaid = true,
    breadcrumbs = [],
    article
  } = Astro2.props;
  const breadcrumbItems = breadcrumbs;
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": title, "description": description, "data-astro-cid-kzcwegrn": true }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<main class="min-h-screen bg-gray-50" data-astro-cid-kzcwegrn> <div class="container mx-auto px-6 py-8" data-astro-cid-kzcwegrn> <!-- 简化的面包屑导航 --> ${breadcrumbItems.length > 0 && renderTemplate`<nav class="mb-6 text-sm text-gray-600" data-astro-cid-kzcwegrn> ${breadcrumbItems.map((crumb, index) => renderTemplate`<span data-astro-cid-kzcwegrn> ${index > 0 && renderTemplate`<span class="mx-2" data-astro-cid-kzcwegrn>›</span>`} ${index === breadcrumbItems.length - 1 ? renderTemplate`<span class="font-medium text-gray-900" data-astro-cid-kzcwegrn>${crumb.name}</span>` : renderTemplate`<a${addAttribute(crumb.href, "href")} class="transition-colors hover:text-blue-600" data-astro-cid-kzcwegrn> ${crumb.name} </a>`} </span>`)} </nav>`} <!-- 文章头部信息 --> ${article && renderTemplate`<header class="mb-8 rounded-lg border border-gray-200 bg-white p-8 shadow-sm" data-astro-cid-kzcwegrn> <h1 class="mb-4 text-4xl font-bold text-gray-900" data-astro-cid-kzcwegrn>${title}</h1> <p class="mb-6 text-xl text-gray-600" data-astro-cid-kzcwegrn>${description}</p> <div class="flex flex-wrap items-center gap-4 text-sm text-gray-500" data-astro-cid-kzcwegrn> <span data-astro-cid-kzcwegrn>发布时间：${article.publishDate.toLocaleDateString("zh-CN")}</span> ${article.updateDate && renderTemplate`<span data-astro-cid-kzcwegrn>更新时间：${article.updateDate.toLocaleDateString("zh-CN")}</span>`} ${article.readingTime && renderTemplate`<span data-astro-cid-kzcwegrn>阅读时间：${article.readingTime} 分钟</span>`} ${article.author && renderTemplate`<span data-astro-cid-kzcwegrn>作者：${article.author}</span>`} </div> ${article.tags && article.tags.length > 0 && renderTemplate`<div class="mt-4" data-astro-cid-kzcwegrn> <div class="mb-2 text-sm text-gray-500" data-astro-cid-kzcwegrn>标签：</div> <div class="flex flex-wrap gap-2" data-astro-cid-kzcwegrn> ${article.tags.map((tag) => renderTemplate`<span class="rounded bg-blue-100 px-3 py-1 text-sm text-blue-800" data-astro-cid-kzcwegrn>${tag}</span>`)} </div> </div>`} </header>`} <!-- 学术内容 --> <div class="rounded-lg border border-gray-200 bg-white shadow-sm" data-astro-cid-kzcwegrn> ${renderComponent($$result2, "AcademicContent", $$AcademicContent, { "showToc": showToc, "showProgress": showProgress, "enableMermaid": enableMermaid, "class": "p-8", "data-astro-cid-kzcwegrn": true }, { "default": ($$result3) => renderTemplate` ${renderSlot($$result3, $$slots["default"])} ` })} </div> <!-- 返回按钮 --> <div class="mt-8 text-center" data-astro-cid-kzcwegrn> <button onclick="history.back()" class="inline-block rounded-lg bg-gray-600 px-6 py-2 text-white transition-colors hover:bg-gray-700" data-astro-cid-kzcwegrn>
← 返回上一页
</button> </div> </div> </main> ` })} `;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/layouts/AcademicLayout.astro", void 0);
const $$Astro = createAstro("https://pennfly.com");
const prerender = false;
const $$ = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$;
  const { slug } = Astro2.params;
  if (!slug) {
    return Astro2.redirect("/future");
  }
  const article = await getEntry("future", slug);
  if (!article) {
    return Astro2.redirect("/404");
  }
  const { Content } = await article.render();
  const breadcrumbs = [
    { name: "研究院首页", href: "/" },
    { name: "未来研究所", href: "/future" },
    { name: article.data.title.zh, href: `/future/${article.slug}` }
  ];
  const articleMeta = {
    publishDate: article.data.publishDate,
    updateDate: article.data.updateDate,
    readingTime: article.data.readingTime,
    tags: [
      ...article.data.tags,
      ...article.data.timeHorizon ? [
        article.data.timeHorizon === "short" ? "短期预测" : article.data.timeHorizon === "medium" ? "中期预测" : "长期预测"
      ] : [],
      ...article.data.confidence ? [
        article.data.confidence === "low" ? "低信心度" : article.data.confidence === "medium" ? "中信心度" : "高信心度"
      ] : [],
      ...article.data.domains || []
    ],
    author: article.data.author
  };
  return renderTemplate`${renderComponent($$result, "AcademicLayout", $$AcademicLayout, { "title": article.data.title.zh, "description": article.data.description.zh, "breadcrumbs": breadcrumbs, "article": articleMeta, "showToc": true, "showProgress": true, "enableMermaid": true }, { "default": async ($$result2) => renderTemplate` ${renderComponent($$result2, "Content", Content, {})} ` })}`;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/future/[...slug].astro", void 0);
const $$file = "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/future/[...slug].astro";
const $$url = "/future/[...slug]";
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({ __proto__: null, default: $$, file: $$file, prerender, url: $$url }, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  i as renderers
};
