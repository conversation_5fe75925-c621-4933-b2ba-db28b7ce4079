import { a as createComponent, h as renderComponent, d as renderTemplate, m as maybeRenderHead, b as addAttribute } from "../assets/vendor-astro.Dc6apy9i.js";
import { i } from "../assets/vendor-astro.Dc6apy9i.js";
import "kleur/colors";
import { $ as $$Layout } from "../assets/Layout.DbxDGMbZ.js";
import { e as getCollection } from "../assets/utils.bIDOeBqD.js";
const $$Index = createComponent(async ($$result, $$props, $$slots) => {
  const reflections = await getCollection("reflections", ({ data }) => !data.draft);
  const sortedReflections = reflections.sort(
    (a, b) => new Date(b.data.publishDate).getTime() - new Date(a.data.publishDate).getTime()
  );
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "个人思考 - Pennfly Private Academy" }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<main class="container mx-auto px-4 py-8 max-w-6xl"> <!-- 页面头部 --> <header class="mb-12 text-center"> <h1 class="text-4xl font-bold mb-4 text-gray-800">个人思考</h1> <p class="text-lg text-gray-600">个人感悟与思辨的记录</p> </header> <!-- 文章列表 --> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"> ${sortedReflections.map((post) => renderTemplate`<article class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden"> <div class="p-6"> <h2 class="text-xl font-semibold mb-3 text-gray-800"> <a${addAttribute(`/reflections/${post.slug}`, "href")} class="hover:text-green-600 transition-colors"> ${post.data.title.zh} </a> </h2> <p class="text-gray-600 mb-4 line-clamp-3">${post.data.description.zh}</p> <div class="flex items-center justify-between text-sm text-gray-500 mb-4"> <span>${post.data.publishDate.toLocaleDateString("zh-CN")}</span> ${post.data.mood && renderTemplate`<span class="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs"> ${post.data.mood} </span>`} </div> <!-- 标签 --> <div class="flex flex-wrap gap-2"> ${post.data.tags.slice(0, 3).map((tag) => renderTemplate`<span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs"> ${tag} </span>`)} </div> </div> </article>`)} </div> ${sortedReflections.length === 0 && renderTemplate`<div class="text-center py-12"> <h2 class="text-2xl font-bold mb-4 text-gray-800">暂无个人思考</h2> <p class="text-gray-600">敬请期待更多精彩内容</p> </div>`} </main> ` })}`;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/reflections/index.astro", void 0);
const $$file = "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/reflections/index.astro";
const $$url = "/reflections";
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({ __proto__: null, default: $$Index, file: $$file, url: $$url }, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  i as renderers
};
