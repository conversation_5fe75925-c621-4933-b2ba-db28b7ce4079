/**
 * 内容统计 API
 * 获取内容统计信息
 */
import type { APIRoute } from 'astro';
import { contentManager } from '../../../utils/contentManager';

export const prerender = false;

export const GET: APIRoute = async () => {
  try {
    const stats = await contentManager.getContentStats();

    const response = {
      total: stats.total,
      published: stats.published,
      drafts: stats.drafts,
      featured: stats.featured,
      byCollection: stats.byCollection,
      byAuthor: stats.byAuthor,
      recentActivity: stats.recentActivity.map(activity => ({
        action: activity.action,
        content: activity.content,
        date: activity.date.toISOString(),
      })),
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=300', // 缓存5分钟
      },
    });
  } catch (error) {
    console.error('内容统计 API 错误:', error);

    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        code: 'INTERNAL_ERROR',
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      }
    );
  }
};
