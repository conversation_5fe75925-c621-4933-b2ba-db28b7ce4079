---
import { getCollection } from 'astro:content';
import Layout from '../../layouts/Layout.astro';
import { formatDate, groupByMonth } from '../../utils/dateUtils';

// 获取动态资讯
const newsArticles = await getCollection('news');
const sortedNews = newsArticles
  .filter((news: any) => !news.data.draft)
  .sort((a: any, b: any) => b.data.publishDate.getTime() - a.data.publishDate.getTime());

// 按类型分组
const newsByType = {
  all: sortedNews,
  research: sortedNews.filter((news: any) => news.data.type === 'research'),
  announcement: sortedNews.filter((news: any) => news.data.type === 'announcement'),
  reflection: sortedNews.filter((news: any) => news.data.type === 'reflection'),
  milestone: sortedNews.filter((news: any) => news.data.type === 'milestone'),
};

// 按月份分组（用于时间线视图）
const newsByMonth = groupByMonth(sortedNews);

// 统计信息
const stats = {
  total: sortedNews.length,
  research: newsByType.research.length,
  announcement: newsByType.announcement.length,
  reflection: newsByType.reflection.length,
  milestone: newsByType.milestone.length,
  featured: sortedNews.filter((news: any) => news.data.featured).length,
};
---

<Layout
  title="动态资讯 - Pennfly Private Academy"
  description="Pennfly Private Academy 的最新动态、研究进展和重要公告"
>
  <main class="min-h-screen bg-gray-50">
    <!-- 页面头部 -->
    <div class="bg-gradient-to-r from-blue-500 to-cyan-600 py-16 text-white">
      <div class="container mx-auto px-6">
        <div class="mb-6 flex items-center">
          <span class="mr-4 text-5xl">📰</span>
          <div>
            <h1 class="mb-2 text-4xl font-bold">动态资讯</h1>
            <p class="text-xl opacity-90">News & Updates</p>
          </div>
        </div>
        <p class="max-w-3xl text-lg opacity-90">
          及时分享研究院的最新动态、研究进展和重要公告。
          了解最新的学术思考、项目进展和未来规划，与研究院保持同步。
        </p>
      </div>
    </div>

    <!-- 动态列表 -->
    <div class="container mx-auto px-6 py-12">
      <!-- 统计和筛选 -->
      <div class="mb-8">
        <div class="mb-6 flex flex-wrap items-center justify-between gap-4">
          <h2 class="text-2xl font-bold text-gray-800">最新动态</h2>
          <div class="flex items-center space-x-4">
            <!-- 视图切换 -->
            <div class="flex rounded-lg border border-gray-300 bg-white">
              <button
                class="view-toggle active rounded-l-lg px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
                data-view="list"
              >
                📋 列表视图
              </button>
              <button
                class="view-toggle rounded-r-lg border-l border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
                data-view="timeline"
              >
                📅 时间线
              </button>
            </div>
          </div>
        </div>

        <!-- 分类筛选 -->
        <div class="mb-6 flex flex-wrap gap-2">
          <button class="filter-btn active" data-type="all">
            全部 ({stats.total})
          </button>
          <button class="filter-btn" data-type="research">
            🔬 研究动态 ({stats.research})
          </button>
          <button class="filter-btn" data-type="announcement">
            📢 重要公告 ({stats.announcement})
          </button>
          <button class="filter-btn" data-type="reflection">
            💭 个人思考 ({stats.reflection})
          </button>
          {
            stats.milestone > 0 && (
              <button class="filter-btn" data-type="milestone">
                🎯 里程碑 ({stats.milestone})
              </button>
            )
          }
        </div>

        <!-- 统计信息 -->
        <div class="mb-8 grid grid-cols-2 gap-4 md:grid-cols-4">
          <div class="rounded-lg bg-blue-50 p-4 text-center">
            <div class="text-2xl font-bold text-blue-600">{stats.total}</div>
            <div class="text-sm text-blue-800">总动态数</div>
          </div>
          <div class="rounded-lg bg-green-50 p-4 text-center">
            <div class="text-2xl font-bold text-green-600">{stats.research}</div>
            <div class="text-sm text-green-800">研究动态</div>
          </div>
          <div class="rounded-lg bg-purple-50 p-4 text-center">
            <div class="text-2xl font-bold text-purple-600">{stats.announcement}</div>
            <div class="text-sm text-purple-800">重要公告</div>
          </div>
          <div class="rounded-lg bg-orange-50 p-4 text-center">
            <div class="text-2xl font-bold text-orange-600">{stats.featured}</div>
            <div class="text-sm text-orange-800">置顶动态</div>
          </div>
        </div>
      </div>

      <div class="mb-12">
        <!-- 列表视图 -->
        <div id="list-view" class="view-content">
          {
            sortedNews.length > 0 ? (
              <div class="space-y-6">
                {Object.entries(newsByType).map(([type, articles]) => (
                  <div class={`news-category ${type === 'all' ? '' : 'hidden'}`} data-type={type}>
                    {articles.map((news: any) => (
                      <article
                        class="news-item mb-6 rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition-all hover:-translate-y-1 hover:shadow-md"
                        data-type={news.data.type}
                      >
                        <div class="mb-4 flex items-start justify-between">
                          <div class="flex-1">
                            <div class="mb-2 flex items-center">
                              <span class="mr-3 text-2xl">
                                {news.data.type === 'research'
                                  ? '🔬'
                                  : news.data.type === 'announcement'
                                    ? '📢'
                                    : news.data.type === 'milestone'
                                      ? '🎯'
                                      : '💭'}
                              </span>
                              <span
                                class={`rounded px-2 py-1 text-xs font-medium ${
                                  news.data.type === 'research'
                                    ? 'bg-blue-100 text-blue-800'
                                    : news.data.type === 'announcement'
                                      ? 'bg-purple-100 text-purple-800'
                                      : news.data.type === 'milestone'
                                        ? 'bg-green-100 text-green-800'
                                        : 'bg-orange-100 text-orange-800'
                                }`}
                              >
                                {news.data.type === 'research'
                                  ? '研究动态'
                                  : news.data.type === 'announcement'
                                    ? '重要公告'
                                    : news.data.type === 'milestone'
                                      ? '里程碑'
                                      : '个人思考'}
                              </span>
                              {news.data.relatedInstitute &&
                                news.data.relatedInstitute.length > 0 && (
                                  <div class="ml-2 flex space-x-1">
                                    {news.data.relatedInstitute.map((institute: string) => (
                                      <span class="rounded bg-gray-100 px-1 py-0.5 text-xs text-gray-600">
                                        {institute === 'economics'
                                          ? '💰'
                                          : institute === 'philosophy'
                                            ? '🤔'
                                            : institute === 'internet'
                                              ? '🌐'
                                              : institute === 'ai'
                                                ? '🤖'
                                                : '🔮'}
                                      </span>
                                    ))}
                                  </div>
                                )}
                            </div>

                            <h3 class="mb-2 text-xl font-semibold text-gray-800">
                              <a
                                href={`/news/${news.slug}`}
                                class="transition-colors hover:text-blue-600"
                              >
                                {news.data.title.zh}
                              </a>
                            </h3>

                            {news.data.summary && (
                              <p class="mb-3 text-gray-600">{news.data.summary}</p>
                            )}

                            <p class="mb-3 text-gray-600">{news.data.description.zh}</p>

                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                              <span>📅 {formatDate(news.data.publishDate)}</span>
                              {news.data.readingTime && (
                                <span>⏱️ {news.data.readingTime} 分钟</span>
                              )}
                              {news.data.author && <span>👤 {news.data.author}</span>}
                            </div>
                          </div>

                          {news.data.featured && (
                            <div class="ml-4">
                              <span class="rounded bg-red-100 px-2 py-1 text-xs font-medium text-red-800">
                                📌 置顶
                              </span>
                            </div>
                          )}
                        </div>

                        {news.data.tags && news.data.tags.length > 0 && (
                          <div class="flex flex-wrap gap-2">
                            {news.data.tags.map((tag: string) => (
                              <span class="cursor-pointer rounded-full bg-gray-100 px-3 py-1 text-xs text-gray-700 transition-colors hover:bg-gray-200">
                                #{tag}
                              </span>
                            ))}
                          </div>
                        )}
                      </article>
                    ))}
                  </div>
                ))}
              </div>
            ) : (
              <div class="py-12 text-center">
                <div class="mb-4 text-6xl">📰</div>
                <h3 class="mb-2 text-xl font-semibold text-gray-800">暂无动态资讯</h3>
                <p class="mb-6 text-gray-600">研究院动态正在更新中，敬请期待最新的研究进展</p>
                <a
                  href="/admin"
                  class="inline-block rounded-lg bg-blue-600 px-6 py-2 text-white transition-colors hover:bg-blue-700"
                >
                  发布动态
                </a>
              </div>
            )
          }
        </div>

        <!-- 时间线视图 -->
        <div id="timeline-view" class="view-content hidden">
          {
            Object.entries(newsByMonth).map(([month, articles]) => (
              <div class="mb-8">
                <div class="mb-4 flex items-center">
                  <div class="mr-4 rounded-lg bg-blue-100 px-3 py-1 text-sm font-medium text-blue-800">
                    {month}
                  </div>
                  <div class="h-px flex-1 bg-gray-200" />
                </div>

                <div class="space-y-4">
                  {articles.map((news: any) => (
                    <div class="news-item flex items-start space-x-4" data-type={news.data.type}>
                      <div class="flex-shrink-0">
                        <div class="flex h-10 w-10 items-center justify-center rounded-full border-2 border-blue-200 bg-white">
                          <span class="text-sm">
                            {news.data.type === 'research'
                              ? '🔬'
                              : news.data.type === 'announcement'
                                ? '📢'
                                : news.data.type === 'milestone'
                                  ? '🎯'
                                  : '💭'}
                          </span>
                        </div>
                      </div>

                      <div class="flex-1 rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-shadow hover:shadow-md">
                        <div class="mb-2 flex items-center justify-between">
                          <h4 class="font-semibold text-gray-800">
                            <a
                              href={`/news/${news.slug}`}
                              class="transition-colors hover:text-blue-600"
                            >
                              {news.data.title.zh}
                            </a>
                          </h4>
                          <span class="text-xs text-gray-500">
                            {formatDate(news.data.publishDate)}
                          </span>
                        </div>

                        <p class="mb-2 text-sm text-gray-600">{news.data.description.zh}</p>

                        {news.data.tags && news.data.tags.length > 0 && (
                          <div class="flex flex-wrap gap-1">
                            {news.data.tags.slice(0, 3).map((tag: string) => (
                              <span class="rounded bg-gray-100 px-2 py-0.5 text-xs text-gray-600">
                                #{tag}
                              </span>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))
          }
        </div>
      </div>

      <!-- 返回首页 -->
      <div class="text-center">
        <a
          href="/"
          class="inline-block rounded-lg bg-gray-600 px-6 py-2 text-white transition-colors hover:bg-gray-700"
        >
          ← 返回研究院首页
        </a>
      </div>
    </div>
  </main>
</Layout>

<style>
  .filter-btn {
    cursor: pointer;
    border-radius: 0.5rem;
    border: 1px solid #d1d5db;
    background-color: white;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #4b5563;
    transition: colors 0.2s;
  }

  .filter-btn:hover {
    background-color: #f9fafb;
  }

  .filter-btn.active {
    border-color: #2563eb;
    background-color: #2563eb;
    color: white;
  }

  .filter-btn.active:hover {
    background-color: #1d4ed8;
  }

  .view-toggle {
    cursor: pointer;
    transition: colors 0.2s;
  }

  .view-toggle.active {
    background-color: #eff6ff;
    color: #2563eb;
  }

  .news-item {
    transition: all 0.3s ease;
  }

  .news-item.hidden {
    display: none;
  }

  .view-content {
    transition: opacity 0.3s ease;
  }

  .view-content.hidden {
    display: none;
  }

  /* 时间线样式 */
  #timeline-view .news-item:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 20px;
    top: 60px;
    width: 2px;
    height: calc(100% + 16px);
    background: linear-gradient(to bottom, #e5e7eb, transparent);
  }

  #timeline-view .news-item {
    position: relative;
  }
</style>

<script>
  // 筛选功能
  const filterButtons = document.querySelectorAll('.filter-btn');
  const newsItems = document.querySelectorAll('.news-item');

  filterButtons.forEach(button => {
    const buttonElement = button as HTMLElement;
    buttonElement.addEventListener('click', () => {
      const targetType = buttonElement.dataset.type;

      // 更新按钮状态
      filterButtons.forEach(btn => btn.classList.remove('active'));
      buttonElement.classList.add('active');

      // 筛选内容
      newsItems.forEach(item => {
        const itemElement = item as HTMLElement;
        if (targetType === 'all' || itemElement.dataset.type === targetType) {
          itemElement.classList.remove('hidden');
        } else {
          itemElement.classList.add('hidden');
        }
      });
    });
  });

  // 视图切换功能
  const viewToggleButtons = document.querySelectorAll('.view-toggle');
  const listView = document.getElementById('list-view');
  const timelineView = document.getElementById('timeline-view');

  viewToggleButtons.forEach(button => {
    const buttonElement = button as HTMLElement;
    buttonElement.addEventListener('click', () => {
      const targetView = buttonElement.dataset.view;

      // 更新按钮状态
      viewToggleButtons.forEach(btn => btn.classList.remove('active'));
      buttonElement.classList.add('active');

      // 切换视图
      if (targetView === 'list') {
        listView?.classList.remove('hidden');
        timelineView?.classList.add('hidden');
      } else {
        listView?.classList.add('hidden');
        timelineView?.classList.remove('hidden');
      }
    });
  });

  // 标签点击功能
  document.querySelectorAll('.rounded-full').forEach(tag => {
    tag.addEventListener('click', () => {
      const tagText = tag.textContent?.replace('#', '').trim();
      if (tagText) {
        // 这里可以添加标签搜索功能
        // 暂时不做处理，避免console警告
      }
    });
  });

  // 平滑滚动到锚点
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    const anchorElement = anchor as HTMLAnchorElement;
    anchorElement.addEventListener('click', function (e) {
      e.preventDefault();
      const href = anchorElement.getAttribute('href');
      if (href) {
        const target = document.querySelector(href);
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
          });
        }
      }
    });
  });
</script>
