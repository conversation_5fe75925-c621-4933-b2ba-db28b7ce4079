import { c as createAstro, a as createComponent, m as maybeRenderHead, b as addAttribute, r as renderScript, d as renderTemplate } from "./vendor-astro.Dc6apy9i.js";
import "kleur/colors";
import "clsx";
import { g as globalTagManager } from "./utils.bIDOeBqD.js";
const $$Astro$3 = createAstro("https://pennfly.com");
const $$TagList = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$3, $$props, $$slots);
  Astro2.self = $$TagList;
  const {
    tags = [],
    variant = "default",
    showCount = false,
    maxTags,
    linkable = true,
    size = "medium"
  } = Astro2.props;
  const displayTags = maxTags ? tags.slice(0, maxTags) : tags;
  const hasMoreTags = maxTags && tags.length > maxTags;
  const remainingCount = hasMoreTags ? tags.length - maxTags : 0;
  function getTagColor(tag) {
    const colors = [
      "#3b82f6",
      "#10b981",
      "#8b5cf6",
      "#f59e0b",
      "#ef4444",
      "#6b7280",
      "#64748b",
      "#06b6d4"
    ];
    let hash = 0;
    for (let i = 0; i < tag.length; i++) {
      hash = tag.charCodeAt(i) + ((hash << 5) - hash);
    }
    return colors[Math.abs(hash) % colors.length];
  }
  return renderTemplate`${maybeRenderHead()}<div${addAttribute(`tag-list tag-list--${variant} tag-list--${size}`, "class")} data-astro-cid-jeqx33lf> ${displayTags.map(
    (tag) => linkable ? renderTemplate`<a${addAttribute(`/tags/${encodeURIComponent(tag)}`, "href")} class="tag-item tag-link"${addAttribute(`--tag-color: ${getTagColor(tag)}`, "style")}${addAttribute(`查看标签"${tag}"的所有内容`, "title")} data-astro-cid-jeqx33lf> <span class="tag-text" data-astro-cid-jeqx33lf>${tag}</span> ${showCount && renderTemplate`<span class="tag-count" data-astro-cid-jeqx33lf>(0)</span>`} </a>` : renderTemplate`<span class="tag-item tag-static"${addAttribute(`--tag-color: ${getTagColor(tag)}`, "style")} data-astro-cid-jeqx33lf> <span class="tag-text" data-astro-cid-jeqx33lf>${tag}</span> ${showCount && renderTemplate`<span class="tag-count" data-astro-cid-jeqx33lf>(0)</span>`} </span>`
  )} ${hasMoreTags && renderTemplate`<span class="tag-item tag-more"${addAttribute(`还有 ${remainingCount} 个标签`, "title")} data-astro-cid-jeqx33lf>
+${remainingCount} </span>`} </div>  ${renderScript($$result, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/components/tags/TagList.astro?astro&type=script&index=0&lang.ts")}`;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/components/tags/TagList.astro", void 0);
const $$Astro$2 = createAstro("https://pennfly.com");
const $$TagCloud = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$2, $$props, $$slots);
  Astro2.self = $$TagCloud;
  const { maxTags = 30, showCount = true, size = "medium", interactive = true } = Astro2.props;
  const tagStats = globalTagManager ? await globalTagManager.getTagStats() : {
    mostPopularTags: []
  };
  const popularTags = tagStats.mostPopularTags.slice(0, maxTags);
  const maxCount = Math.max(...popularTags.map((tag) => tag.count));
  const minCount = Math.min(...popularTags.map((tag) => tag.count));
  function getTagSize(count) {
    const ratio = (count - minCount) / (maxCount - minCount);
    switch (size) {
      case "small":
        return `${0.8 + ratio * 0.6}rem`;
      // 0.8rem - 1.4rem
      case "large":
        return `${1.2 + ratio * 1.2}rem`;
      // 1.2rem - 2.4rem
      default:
        return `${1 + ratio * 0.8}rem`;
    }
  }
  function getTagOpacity(count) {
    const ratio = (count - minCount) / (maxCount - minCount);
    return 0.6 + ratio * 0.4;
  }
  return renderTemplate`${maybeRenderHead()}<div class="tag-cloud"${addAttribute(interactive, "data-interactive")} data-astro-cid-n2s74bzm> <div class="tag-cloud-container" data-astro-cid-n2s74bzm> ${popularTags.map((tag) => renderTemplate`<a${addAttribute(`/tags/${encodeURIComponent(tag.name)}`, "href")} class="tag-item"${addAttribute(`
          font-size: ${getTagSize(tag.count)};
          opacity: ${getTagOpacity(tag.count)};
          color: ${tag.color};
        `, "style")}${addAttribute(tag.count, "data-count")}${addAttribute(tag.category, "data-category")}${addAttribute(`${tag.name} (${tag.count} 篇内容)`, "title")} data-astro-cid-n2s74bzm> ${tag.name} ${showCount && renderTemplate`<span class="tag-count" data-astro-cid-n2s74bzm>(${tag.count})</span>`} </a>`)} </div> </div>  `;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/components/tags/TagCloud.astro", void 0);
const $$Astro$1 = createAstro("https://pennfly.com");
const $$TagStats = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$1, $$props, $$slots);
  Astro2.self = $$TagStats;
  const { showCategories = true, showTrends = true, compact = false } = Astro2.props;
  const tagStats = globalTagManager ? await globalTagManager.getTagStats() : {
    totalTags: 0,
    totalUniqueContent: 0,
    mostPopularTags: [],
    tagsByCategory: {},
    recentTags: []
  };
  const categoryStats = Object.entries(tagStats.tagsByCategory).map(([category, tags]) => ({
    name: category,
    count: tags.length,
    totalUsage: tags.reduce((sum, tag) => sum + tag.count, 0),
    topTag: tags[0]?.name || "",
    color: tags[0]?.color || "#64748b"
  })).sort((a, b) => b.totalUsage - a.totalUsage);
  function getCategoryDisplayName(category) {
    const names = {
      technology: "技术",
      economics: "经济",
      philosophy: "哲学",
      society: "社会",
      research: "研究",
      tools: "工具",
      general: "通用"
    };
    return names[category] || category;
  }
  function getCategoryIcon(category) {
    const icons = {
      technology: "🔬",
      economics: "💰",
      philosophy: "🤔",
      society: "🏛️",
      research: "📊",
      tools: "🛠️",
      general: "📝"
    };
    return icons[category] || "📝";
  }
  return renderTemplate`${maybeRenderHead()}<div${addAttribute(`tag-stats ${compact ? "tag-stats--compact" : ""}`, "class")} data-astro-cid-kgynm5rn> <!-- 总体统计 --> <div class="stats-overview" data-astro-cid-kgynm5rn> <div class="stat-card" data-astro-cid-kgynm5rn> <div class="stat-icon" data-astro-cid-kgynm5rn>🏷️</div> <div class="stat-content" data-astro-cid-kgynm5rn> <div class="stat-number" data-astro-cid-kgynm5rn>${tagStats.totalTags}</div> <div class="stat-label" data-astro-cid-kgynm5rn>总标签数</div> </div> </div> <div class="stat-card" data-astro-cid-kgynm5rn> <div class="stat-icon" data-astro-cid-kgynm5rn>📚</div> <div class="stat-content" data-astro-cid-kgynm5rn> <div class="stat-number" data-astro-cid-kgynm5rn>${tagStats.totalUniqueContent}</div> <div class="stat-label" data-astro-cid-kgynm5rn>标签使用次数</div> </div> </div> <div class="stat-card" data-astro-cid-kgynm5rn> <div class="stat-icon" data-astro-cid-kgynm5rn>⭐</div> <div class="stat-content" data-astro-cid-kgynm5rn> <div class="stat-number" data-astro-cid-kgynm5rn>${tagStats.mostPopularTags[0]?.count || 0}</div> <div class="stat-label" data-astro-cid-kgynm5rn>最热标签使用数</div> </div> </div> </div> ${showCategories && renderTemplate`<div class="category-stats" data-astro-cid-kgynm5rn> <h3 class="section-title" data-astro-cid-kgynm5rn> <span class="title-icon" data-astro-cid-kgynm5rn>📊</span>
标签分类统计
</h3> <div class="category-grid" data-astro-cid-kgynm5rn> ${categoryStats.map((category) => renderTemplate`<div class="category-card"${addAttribute(`--category-color: ${category.color}`, "style")} data-astro-cid-kgynm5rn> <div class="category-header" data-astro-cid-kgynm5rn> <span class="category-icon" data-astro-cid-kgynm5rn>${getCategoryIcon(category.name)}</span> <span class="category-name" data-astro-cid-kgynm5rn>${getCategoryDisplayName(category.name)}</span> </div> <div class="category-metrics" data-astro-cid-kgynm5rn> <div class="metric" data-astro-cid-kgynm5rn> <span class="metric-value" data-astro-cid-kgynm5rn>${category.count}</span> <span class="metric-label" data-astro-cid-kgynm5rn>个标签</span> </div> <div class="metric" data-astro-cid-kgynm5rn> <span class="metric-value" data-astro-cid-kgynm5rn>${category.totalUsage}</span> <span class="metric-label" data-astro-cid-kgynm5rn>次使用</span> </div> </div> ${category.topTag && renderTemplate`<div class="category-top-tag" data-astro-cid-kgynm5rn> <span class="top-tag-label" data-astro-cid-kgynm5rn>热门:</span> <span class="top-tag-name" data-astro-cid-kgynm5rn>${category.topTag}</span> </div>`} </div>`)} </div> </div>`} ${showTrends && tagStats.recentTags.length > 0 && renderTemplate`<div class="trend-stats" data-astro-cid-kgynm5rn> <h3 class="section-title" data-astro-cid-kgynm5rn> <span class="title-icon" data-astro-cid-kgynm5rn>📈</span>
热门标签趋势
</h3> <div class="trend-list" data-astro-cid-kgynm5rn> ${tagStats.recentTags.slice(0, 8).map((tagName, index) => {
    const tagInfo = tagStats.mostPopularTags.find((t) => t.name === tagName);
    return tagInfo && renderTemplate`<div class="trend-item" data-astro-cid-kgynm5rn> <div class="trend-rank" data-astro-cid-kgynm5rn>#${index + 1}</div> <div class="trend-content" data-astro-cid-kgynm5rn> <span class="trend-name" data-astro-cid-kgynm5rn>${tagName}</span> <span class="trend-count" data-astro-cid-kgynm5rn>${tagInfo.count} 次</span> </div> <div class="trend-bar" data-astro-cid-kgynm5rn> <div class="trend-fill"${addAttribute(`width: ${tagInfo.count / tagStats.mostPopularTags[0].count * 100}%; background-color: ${tagInfo.color}`, "style")} data-astro-cid-kgynm5rn></div> </div> </div>`;
  })} </div> </div>`} </div>  ${renderScript($$result, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/components/tags/TagStats.astro?astro&type=script&index=0&lang.ts")}`;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/components/tags/TagStats.astro", void 0);
const $$Astro = createAstro("https://pennfly.com");
const $$TagSearch = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$TagSearch;
  const {
    placeholder = "搜索标签...",
    showCategories = true,
    maxSuggestions = 8,
    size = "medium",
    autoFocus = false
  } = Astro2.props;
  return renderTemplate`${maybeRenderHead()}<div${addAttribute(`tag-search tag-search--${size}`, "class")} data-astro-cid-76dt3kya> <div class="search-container" data-astro-cid-76dt3kya> <div class="search-input-wrapper" data-astro-cid-76dt3kya> <input type="text" class="search-input"${addAttribute(placeholder, "placeholder")} autocomplete="off" spellcheck="false"${addAttribute(autoFocus, "autofocus")}${addAttribute(maxSuggestions, "data-max-suggestions")} data-astro-cid-76dt3kya> <div class="search-icon" data-astro-cid-76dt3kya> <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" data-astro-cid-76dt3kya> <circle cx="11" cy="11" r="8" data-astro-cid-76dt3kya></circle> <path d="m21 21-4.35-4.35" data-astro-cid-76dt3kya></path> </svg> </div> <button class="clear-button" style="display: none;" title="清除搜索" data-astro-cid-76dt3kya> <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" data-astro-cid-76dt3kya> <line x1="18" y1="6" x2="6" y2="18" data-astro-cid-76dt3kya></line> <line x1="6" y1="6" x2="18" y2="18" data-astro-cid-76dt3kya></line> </svg> </button> </div> ${showCategories && renderTemplate`<div class="category-filter" data-astro-cid-76dt3kya> <select class="category-select" data-astro-cid-76dt3kya> <option value="all" data-astro-cid-76dt3kya>所有分类</option> <option value="technology" data-astro-cid-76dt3kya>🔬 技术</option> <option value="economics" data-astro-cid-76dt3kya>💰 经济</option> <option value="philosophy" data-astro-cid-76dt3kya>🤔 哲学</option> <option value="society" data-astro-cid-76dt3kya>🏛️ 社会</option> <option value="research" data-astro-cid-76dt3kya>📊 研究</option> <option value="tools" data-astro-cid-76dt3kya>🛠️ 工具</option> <option value="general" data-astro-cid-76dt3kya>📝 通用</option> </select> </div>`} </div> <!-- 搜索建议下拉框 --> <div class="suggestions-dropdown" style="display: none;" data-astro-cid-76dt3kya> <div class="suggestions-header" data-astro-cid-76dt3kya> <span class="suggestions-title" data-astro-cid-76dt3kya>搜索建议</span> <span class="suggestions-count" data-astro-cid-76dt3kya></span> </div> <div class="suggestions-list" data-astro-cid-76dt3kya></div> <div class="suggestions-footer" data-astro-cid-76dt3kya> <button class="view-all-button" data-astro-cid-76dt3kya>查看所有结果</button> </div> </div> <!-- 加载状态 --> <div class="loading-indicator" style="display: none;" data-astro-cid-76dt3kya> <div class="loading-spinner" data-astro-cid-76dt3kya></div> <span class="loading-text" data-astro-cid-76dt3kya>搜索中...</span> </div> <!-- 无结果状态 --> <div class="no-results" style="display: none;" data-astro-cid-76dt3kya> <div class="no-results-icon" data-astro-cid-76dt3kya>🔍</div> <div class="no-results-text" data-astro-cid-76dt3kya>未找到相关标签</div> <div class="no-results-suggestion" data-astro-cid-76dt3kya>尝试使用其他关键词</div> </div> </div>  ${renderScript($$result, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/components/tags/TagSearch.astro?astro&type=script&index=0&lang.ts")}`;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/components/tags/TagSearch.astro", void 0);
export {
  $$TagList as $,
  $$TagStats as a,
  $$TagCloud as b,
  $$TagSearch as c
};
