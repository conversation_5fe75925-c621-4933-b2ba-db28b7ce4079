/* 可访问性增强样式 */

/* 基础可访问性改进 */
:root {
  /* 高对比度颜色 */
  --color-text-high-contrast: #000000;
  --color-bg-high-contrast: #ffffff;
  --color-link-high-contrast: #0000ff;
  --color-focus: #3b82f6;

  /* 最小字体大小 */
  --font-size-min: 16px;
  --line-height-accessible: 1.6;

  /* 最小点击区域 */
  --min-touch-target: 44px;
}

/* 确保所有文本都有足够的对比度 */
body {
  color: #1f2937; /* 深灰色，对比度更高 */
  line-height: var(--line-height-accessible);
}

/* 改善链接的可访问性 */
a {
  color: #1d4ed8; /* 更深的蓝色，提高对比度 */
  text-decoration: underline;
  text-underline-offset: 2px;
}

a:hover {
  color: #1e40af;
  text-decoration-thickness: 2px;
}

a:focus {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
  border-radius: 2px;
}

/* 确保所有交互元素都有足够的点击区域 */
button,
a,
input,
select,
textarea,
[role='button'],
[tabindex='0'] {
  min-height: var(--min-touch-target);
  min-width: var(--min-touch-target);
}

/* 改善按钮的可访问性 */
button {
  background-color: #f3f4f6;
  border: 2px solid #d1d5db;
  color: #374151;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

button:hover {
  background-color: #e5e7eb;
  border-color: #9ca3af;
}

button:focus {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 改善表单元素的可访问性 */
input,
textarea,
select {
  border: 2px solid #d1d5db;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: var(--font-size-min);
  line-height: var(--line-height-accessible);
}

input:focus,
textarea:focus,
select:focus {
  outline: 2px solid var(--color-focus);
  outline-offset: 2px;
  border-color: var(--color-focus);
}

/* 改善标题的层次结构 */
h1,
h2,
h3,
h4,
h5,
h6 {
  color: #111827; /* 更深的颜色提高对比度 */
  font-weight: 700;
  line-height: 1.3;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
}

h1 {
  font-size: 2.25rem; /* 36px */
}

h2 {
  font-size: 1.875rem; /* 30px */
}

h3 {
  font-size: 1.5rem; /* 24px */
}

h4 {
  font-size: 1.25rem; /* 20px */
}

h5 {
  font-size: 1.125rem; /* 18px */
}

h6 {
  font-size: 1rem; /* 16px */
}

/* 改善段落的可读性 */
p {
  line-height: var(--line-height-accessible);
  margin-bottom: 1em;
  max-width: 70ch; /* 限制行长度提高可读性 */
}

/* 改善列表的可访问性 */
ul,
ol {
  padding-left: 2em;
  line-height: var(--line-height-accessible);
}

li {
  margin-bottom: 0.5em;
}

/* 改善表格的可访问性 */
table {
  border-collapse: collapse;
  width: 100%;
  margin: 1em 0;
}

th,
td {
  border: 1px solid #d1d5db;
  padding: 12px;
  text-align: left;
}

th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
}

/* 屏幕阅读器专用内容 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --color-text: var(--color-text-high-contrast);
    --color-bg: var(--color-bg-high-contrast);
  }

  body {
    background-color: var(--color-bg-high-contrast);
    color: var(--color-text-high-contrast);
  }

  a {
    color: var(--color-link-high-contrast);
    text-decoration: underline;
  }

  button {
    background-color: #f0f0f0;
    border: 2px solid #000000;
    color: #000000;
  }

  input,
  textarea,
  select {
    background-color: #ffffff;
    border: 2px solid #000000;
    color: #000000;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* 大字体模式支持 */
@media (min-resolution: 2dppx) {
  body {
    font-size: 18px;
  }
}

/* 焦点指示器增强 */
*:focus-visible {
  outline: 3px solid var(--color-focus);
  outline-offset: 2px;
  border-radius: 3px;
}

/* 确保图片有替代文本提示 */
img:not([alt]) {
  border: 3px solid #ef4444;
}

img[alt=''] {
  border: 2px solid #f59e0b;
}

/* 改善代码块的可访问性 */
code {
  background-color: #f3f4f6;
  color: #1f2937;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', Courier, monospace;
  font-size: 0.9em;
}

pre {
  background-color: #1f2937;
  color: #f9fafb;
  padding: 16px;
  border-radius: 8px;
  overflow-x: auto;
  line-height: 1.5;
}

pre code {
  background-color: transparent;
  color: inherit;
  padding: 0;
}

/* 改善引用的可访问性 */
blockquote {
  border-left: 4px solid #3b82f6;
  background-color: #f8fafc;
  padding: 16px 20px;
  margin: 16px 0;
  font-style: italic;
  color: #475569;
}

/* 键盘导航增强 */
.keyboard-navigation-active *:focus {
  outline: 3px solid var(--color-focus);
  outline-offset: 2px;
}

/* 错误和警告消息的可访问性 */
.error {
  color: #dc2626;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  padding: 12px;
  border-radius: 6px;
  font-weight: 500;
}

.warning {
  color: #d97706;
  background-color: #fffbeb;
  border: 1px solid #fed7aa;
  padding: 12px;
  border-radius: 6px;
  font-weight: 500;
}

.success {
  color: #059669;
  background-color: #f0fdf4;
  border: 1px solid #bbf7d0;
  padding: 12px;
  border-radius: 6px;
  font-weight: 500;
}

/* 移动端可访问性改进 */
@media (max-width: 768px) {
  /* 增大移动端的点击区域 */
  button,
  a,
  input,
  select,
  textarea {
    min-height: 48px;
    min-width: 48px;
  }

  /* 增大移动端字体 */
  body {
    font-size: 18px;
  }

  /* 改善移动端的行高 */
  p,
  li {
    line-height: 1.7;
  }
}

/* 打印样式的可访问性 */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  a,
  a:visited {
    text-decoration: underline;
    color: black !important;
  }

  a[href]:after {
    content: ' (' attr(href) ')';
    font-size: 12px;
  }

  .skip-links,
  .accessibility-toolbar {
    display: none !important;
  }
}

/* 语言特定的改进 */
:lang(zh) {
  font-family:
    'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', sans-serif;
  line-height: 1.8; /* 中文需要更大的行高 */
}

:lang(en) {
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
}

/* 确保内容在缩放到200%时仍然可用 */
@media (min-width: 1280px) {
  .container {
    max-width: 1200px;
  }
}

/* 改善导航的可访问性 */
nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

nav a {
  display: block;
  padding: 12px 16px;
  text-decoration: none;
  border-radius: 6px;
  transition: all 0.2s ease;
}

nav a:hover,
nav a:focus {
  background-color: #f3f4f6;
  text-decoration: underline;
}

nav a[aria-current='page'] {
  background-color: #dbeafe;
  color: #1d4ed8;
  font-weight: 600;
}

/* 改善模态框的可访问性 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  padding: 24px;
  border-radius: 8px;
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* 改善工具提示的可访问性 */
.tooltip {
  position: relative;
}

.tooltip[aria-describedby] {
  cursor: help;
}

.tooltip-content {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: #1f2937;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  white-space: nowrap;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
}

.tooltip:hover .tooltip-content,
.tooltip:focus .tooltip-content {
  opacity: 1;
  visibility: visible;
}
