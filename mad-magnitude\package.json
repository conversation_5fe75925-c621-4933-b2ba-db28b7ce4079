{"name": "pennfly-private-academy", "type": "module", "version": "1.0.0", "description": "Pennfly Private Academy - 个人化私人研究院 · 跨领域学术探索 · 思想与创意的交流平台", "keywords": ["astro", "education", "research", "blog", "i18n", "tailwindcss", "typescript"], "author": "Pennfly", "license": "MIT", "homepage": "https://pennfly.com", "repository": {"type": "git", "url": "https://github.com/pennfly/pennfly-academy.git"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "scripts": {"dev": "astro dev", "build": "astro build", "build:prod": "cross-env NODE_ENV=production astro build", "build:analyze": "astro build --analyze", "build:size": "npm run build:prod && npm run analyze:size", "preview": "astro preview", "astro": "astro", "lint": "eslint src --ext .ts,.js --fix --config eslint.config.js", "lint:check": "eslint src --ext .ts,.js --config eslint.config.js", "lint:strict": "eslint src --ext .ts,.astro --fix", "lint:astro": "eslint src --ext .astro --fix", "format": "prettier --write \"src/**/*.{ts,js,md,json}\" --ignore-path .prettierignore", "format:check": "prettier --check \"src/**/*.{ts,js,md,json}\" --ignore-path .prettierignore", "format:astro": "prettier --write \"src/**/*.astro\" --ignore-path .prettierignore", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:run": "vitest run", "analyze:size": "node scripts/analyze-bundle.js", "analyze:images": "node scripts/optimize-images.js", "build:monitor": "node scripts/build-monitor.js", "build:report": "npm run build:prod && npm run analyze:size && npm run analyze:images", "build:complete": "node scripts/build-complete.js", "build:complete:dev": "node scripts/build-complete.js --env development", "clean": "rimraf dist .astro node_modules/.cache", "clean:all": "npm run clean && rimraf node_modules package-lock.json", "prepare": "husky install", "precommit": "npm run check:lint", "check": "npm run type-check", "check:lint": "npm run lint:check && npm run type-check", "check:basic": "npm run lint:check && npm run format:check && npm run type-check", "check:full": "npm run lint:strict:check && npm run format:astro && npm run type-check", "fix": "npm run lint && npm run format", "security:audit": "npm audit --audit-level=moderate", "security:audit:fix": "npm audit fix", "security:audit:ci": "audit-ci --moderate", "security:audit:detailed": "better-npm-audit audit", "security:check": "npm run security:audit && npm run security:deps && npm run security:lint", "security:deps": "node scripts/security-check.js", "security:lint": "eslint src --ext .ts,.astro --config eslint.config.js --no-fix | grep -i security || echo 'No security issues found'", "security:full": "npm run security:check && npm run security:audit:detailed", "security:headers": "node -e \"console.log('Security headers configured in src/utils/security.ts')\"", "security:validate": "npm run security:check && npm run type-check && npm run test:run", "create:research": "node scripts/create-content.js research", "create:log": "node scripts/create-content.js log", "create:news": "node scripts/create-content.js news", "create:help": "node scripts/create-content.js --help", "prebuild": "npm run clean && npm run check", "prebuild:full": "npm run clean && npm run check:full", "postbuild": "npm run analyze:size"}, "dependencies": {"@astrojs/node": "^9.4.0", "@astrojs/rss": "^4.0.12", "@tailwindcss/vite": "^4.1.11", "astro": "^5.12.9", "fuse.js": "^7.1.0", "mermaid": "^11.9.0", "remark-gfm": "^4.0.1", "tailwindcss": "^4.1.11"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^9.33.0", "@testing-library/jest-dom": "^6.6.4", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "audit-ci": "^7.1.0", "better-npm-audit": "^3.11.0", "cross-env": "^7.0.3", "eslint": "^9.33.0", "eslint-plugin-astro": "^1.3.1", "eslint-plugin-security": "^3.0.1", "husky": "^9.1.7", "jsdom": "^26.1.0", "lint-staged": "^16.1.5", "prettier": "^3.6.2", "prettier-plugin-astro": "^0.14.1", "prettier-plugin-tailwindcss": "^0.6.14", "rimraf": "^6.0.1", "typescript": "^5.9.2", "vitest": "^3.2.4"}, "lint-staged": {"*.{ts,astro}": ["eslint --fix", "prettier --write", "npm run security:check"], "*.{md,json}": ["prettier --write"]}}