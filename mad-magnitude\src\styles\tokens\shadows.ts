/**
 * 设计令牌 - 阴影系统
 * 定义统一的阴影效果
 */

export const boxShadow = {
  // 基础阴影
  sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  DEFAULT: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
  md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
  '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
  inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',
  none: '0 0 #0000',

  // 彩色阴影 (用于强调)
  colored: {
    primary: '0 4px 14px 0 rgb(37 99 235 / 0.2)',
    secondary: '0 4px 14px 0 rgb(245 158 11 / 0.2)',
    success: '0 4px 14px 0 rgb(16 185 129 / 0.2)',
    warning: '0 4px 14px 0 rgb(245 158 11 / 0.2)',
    error: '0 4px 14px 0 rgb(239 68 68 / 0.2)',
  },
} as const;

// 语义化阴影定义
export const semanticShadow = {
  // 卡片阴影
  card: {
    rest: boxShadow.sm, // 静止状态
    hover: boxShadow.md, // 悬停状态
    active: boxShadow.lg, // 激活状态
  },

  // 按钮阴影
  button: {
    rest: boxShadow.sm, // 静止状态
    hover: boxShadow.DEFAULT, // 悬停状态
    active: boxShadow.inner, // 按下状态
    focus: '0 0 0 3px rgb(37 99 235 / 0.1)', // 焦点状态
  },

  // 弹窗阴影
  modal: {
    sm: boxShadow.lg, // 小弹窗
    md: boxShadow.xl, // 默认弹窗
    lg: boxShadow['2xl'], // 大弹窗
  },

  // 下拉菜单阴影
  dropdown: {
    sm: boxShadow.md, // 小下拉菜单
    md: boxShadow.lg, // 默认下拉菜单
    lg: boxShadow.xl, // 大下拉菜单
  },

  // 输入框阴影
  input: {
    rest: boxShadow.inner, // 静止状态
    focus: '0 0 0 3px rgb(37 99 235 / 0.1)', // 焦点状态
    error: '0 0 0 3px rgb(239 68 68 / 0.1)', // 错误状态
  },

  // 图片阴影
  image: {
    subtle: boxShadow.sm, // 轻微阴影
    normal: boxShadow.DEFAULT, // 正常阴影
    prominent: boxShadow.md, // 突出阴影
  },
} as const;

// 暗色主题阴影 (使用更深的阴影)
export const darkShadow = {
  sm: '0 1px 2px 0 rgb(0 0 0 / 0.3)',
  DEFAULT: '0 1px 3px 0 rgb(0 0 0 / 0.4), 0 1px 2px -1px rgb(0 0 0 / 0.4)',
  md: '0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4)',
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.4)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.4), 0 8px 10px -6px rgb(0 0 0 / 0.4)',
  '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.5)',
} as const;

// 导出类型定义
export type BoxShadowToken = typeof boxShadow;
export type SemanticShadow = typeof semanticShadow;
