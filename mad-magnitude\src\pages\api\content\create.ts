/**
 * 创建内容 API
 * 创建新的内容项
 */
import type { APIRoute } from 'astro';
import { contentManager } from '../../../utils/contentManager';

export const prerender = false;

export const POST: APIRoute = async ({ request }) => {
  try {
    const body = await request.json();
    const { collection, slug, frontmatter, content } = body;

    // 验证必需字段
    if (!collection || !slug || !frontmatter || content === undefined) {
      return new Response(
        JSON.stringify({
          error: 'Collection, slug, frontmatter, and content are required',
          code: 'MISSING_REQUIRED_FIELDS',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    // 验证集合名称
    const validCollections = [
      'news',
      'logs',
      'research',
      'reflections',
      'economics',
      'philosophy',
      'internet',
      'ai',
      'future',
      'products',
    ];

    if (!validCollections.includes(collection)) {
      return new Response(
        JSON.stringify({
          error: 'Invalid collection name',
          code: 'INVALID_COLLECTION',
          validCollections,
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    // 验证 slug 格式（只允许字母、数字、连字符和下划线）
    const slugRegex = /^[a-zA-Z0-9_-]+$/;
    if (!slugRegex.test(slug)) {
      return new Response(
        JSON.stringify({
          error: 'Invalid slug format. Only letters, numbers, hyphens, and underscores are allowed',
          code: 'INVALID_SLUG',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    // 检查是否已存在相同的内容
    const existingContent = await contentManager.getContentById(`${collection}/${slug}`);
    if (existingContent) {
      return new Response(
        JSON.stringify({
          error: 'Content with this slug already exists in the collection',
          code: 'CONTENT_EXISTS',
        }),
        {
          status: 409,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    // 创建内容
    const newContent = await contentManager.createContent(collection, slug, frontmatter, content);

    const response = {
      id: newContent.id,
      collection: newContent.collection,
      slug: newContent.slug,
      title: newContent.title,
      description: newContent.description,
      publishDate: newContent.publishDate.toISOString(),
      updateDate: newContent.updateDate?.toISOString(),
      draft: newContent.draft,
      featured: newContent.featured,
      tags: newContent.tags,
      author: newContent.author,
      content: newContent.content,
    };

    return new Response(JSON.stringify(response), {
      status: 201,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('创建内容 API 错误:', error);

    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        code: 'INTERNAL_ERROR',
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      }
    );
  }
};
