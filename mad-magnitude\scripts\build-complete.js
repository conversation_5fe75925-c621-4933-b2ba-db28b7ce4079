#!/usr/bin/env node

import { execSync } from 'child_process';
import { existsSync, mkdirSync } from 'fs';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';
import BuildErrorHandler from './build-error-handler.js';
import BuildMonitor from './build-monitor.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * 完整的构建流程管理器
 * 集成构建、分析、监控和错误处理
 */
class CompleteBuildManager {
  constructor(options = {}) {
    this.projectRoot = join(__dirname, '..');
    this.reportsDir = join(this.projectRoot, 'reports');
    this.options = {
      skipTests: false,
      skipLinting: false,
      skipAnalysis: false,
      environment: 'production',
      verbose: false,
      ...options
    };
    
    this.errorHandler = new BuildErrorHandler();
    this.buildMonitor = new BuildMonitor();
    this.startTime = Date.now();
  }

  /**
   * 确保必要的目录存在
   */
  ensureDirectories() {
    if (!existsSync(this.reportsDir)) {
      mkdirSync(this.reportsDir, { recursive: true });
    }
  }

  /**
   * 打印构建开始信息
   */
  printBuildStart() {
    console.log('\n🚀 开始完整构建流程');
    console.log('='.repeat(60));
    console.log(`环境: ${this.options.environment}`);
    console.log(`时间: ${new Date().toLocaleString()}`);
    console.log(`Node.js: ${process.version}`);
    console.log(`平台: ${process.platform} (${process.arch})`);
    console.log('='.repeat(60));
  }

  /**
   * 运行预构建检查
   */
  async runPreBuildChecks() {
    console.log('\n📋 运行预构建检查...');
    
    const checks = [];
    
    // 类型检查
    if (!this.options.skipLinting) {
      checks.push({
        name: 'TypeScript 类型检查',
        command: 'npm run type-check',
        critical: true
      });
      
      checks.push({
        name: 'ESLint 代码检查',
        command: 'npm run lint:check',
        critical: false
      });
      
      checks.push({
        name: 'Prettier 格式检查',
        command: 'npm run format:check',
        critical: false
      });
    }
    
    // 测试
    if (!this.options.skipTests) {
      checks.push({
        name: '单元测试',
        command: 'npm run test:run',
        critical: false
      });
    }
    
    let passedChecks = 0;
    let failedChecks = 0;
    
    for (const check of checks) {
      try {
        console.log(`   ⏳ ${check.name}...`);
        
        const result = execSync(check.command, {
          cwd: this.projectRoot,
          encoding: 'utf8',
          stdio: this.options.verbose ? 'inherit' : 'pipe'
        });
        
        console.log(`   ✅ ${check.name} - 通过`);
        passedChecks++;
        
      } catch (error) {
        console.log(`   ${check.critical ? '❌' : '⚠️'} ${check.name} - ${check.critical ? '失败' : '警告'}`);
        
        if (this.options.verbose) {
          console.log(`      错误: ${error.message}`);
        }
        
        failedChecks++;
        
        if (check.critical) {
          throw new Error(`关键检查失败: ${check.name}`);
        }
      }
    }
    
    console.log(`\n📊 预构建检查结果: ${passedChecks} 通过, ${failedChecks} 失败/警告`);
    
    if (failedChecks > 0 && !this.options.skipLinting) {
      console.log('⚠️  建议修复警告后再进行构建');
    }
  }

  /**
   * 清理构建目录
   */
  cleanBuildDirectory() {
    console.log('\n🧹 清理构建目录...');
    
    try {
      execSync('npm run clean', {
        cwd: this.projectRoot,
        stdio: this.options.verbose ? 'inherit' : 'pipe'
      });
      console.log('   ✅ 构建目录已清理');
    } catch (error) {
      console.warn('   ⚠️  清理构建目录失败:', error.message);
    }
  }

  /**
   * 执行主构建
   */
  async runMainBuild() {
    console.log('\n🔨 执行主构建...');
    
    const buildCommand = this.options.environment === 'production' 
      ? 'npm run build:prod' 
      : 'npm run build';
    
    try {
      const buildStart = Date.now();
      
      execSync(buildCommand, {
        cwd: this.projectRoot,
        stdio: 'inherit',
        env: {
          ...process.env,
          NODE_ENV: this.options.environment
        }
      });
      
      const buildTime = Date.now() - buildStart;
      console.log(`   ✅ 构建完成 (耗时: ${buildTime}ms)`);
      
      return { success: true, buildTime };
      
    } catch (error) {
      console.log('   ❌ 构建失败');
      
      // 使用错误处理器分析错误
      this.errorHandler.handleError(error);
      
      return { success: false, error };
    }
  }

  /**
   * 运行构建后分析
   */
  async runPostBuildAnalysis() {
    if (this.options.skipAnalysis) {
      console.log('\n⏭️  跳过构建分析');
      return;
    }
    
    console.log('\n📊 运行构建后分析...');
    
    const analyses = [
      {
        name: '包大小分析',
        command: 'npm run analyze:size',
        optional: true
      },
      {
        name: '图片优化分析',
        command: 'npm run analyze:images',
        optional: true
      }
    ];
    
    for (const analysis of analyses) {
      try {
        console.log(`   ⏳ ${analysis.name}...`);
        
        execSync(analysis.command, {
          cwd: this.projectRoot,
          stdio: this.options.verbose ? 'inherit' : 'pipe'
        });
        
        console.log(`   ✅ ${analysis.name} - 完成`);
        
      } catch (error) {
        console.log(`   ⚠️  ${analysis.name} - 失败`);
        
        if (this.options.verbose) {
          console.log(`      错误: ${error.message}`);
        }
        
        if (!analysis.optional) {
          throw error;
        }
      }
    }
  }

  /**
   * 生成构建报告
   */
  generateBuildReport(buildResult) {
    console.log('\n📋 生成构建报告...');
    
    const totalTime = Date.now() - this.startTime;
    const report = {
      timestamp: new Date().toISOString(),
      environment: this.options.environment,
      totalTime,
      buildTime: buildResult.buildTime || 0,
      success: buildResult.success,
      nodeVersion: process.version,
      platform: process.platform,
      options: this.options,
      memory: process.memoryUsage()
    };
    
    // 打印摘要
    console.log('\n📈 构建摘要');
    console.log('-'.repeat(40));
    console.log(`状态: ${report.success ? '✅ 成功' : '❌ 失败'}`);
    console.log(`总耗时: ${report.totalTime}ms`);
    console.log(`构建耗时: ${report.buildTime}ms`);
    console.log(`环境: ${report.environment}`);
    console.log(`内存使用: ${(report.memory.heapUsed / 1024 / 1024).toFixed(2)} MB`);
    console.log('-'.repeat(40));
    
    return report;
  }

  /**
   * 运行完整构建流程
   */
  async run() {
    try {
      this.ensureDirectories();
      this.printBuildStart();
      
      // 设置错误处理
      this.errorHandler.setupGlobalErrorHandlers();
      
      // 预构建检查
      await this.runPreBuildChecks();
      
      // 清理构建目录
      this.cleanBuildDirectory();
      
      // 主构建
      const buildResult = await this.runMainBuild();
      
      if (!buildResult.success) {
        console.log('\n❌ 构建失败，停止后续流程');
        process.exit(1);
      }
      
      // 构建后分析
      await this.runPostBuildAnalysis();
      
      // 生成报告
      const report = this.generateBuildReport(buildResult);
      
      console.log('\n🎉 完整构建流程成功完成!');
      console.log(`📊 详细报告已保存到: ${this.reportsDir}`);
      
      return report;
      
    } catch (error) {
      console.error('\n💥 构建流程失败:', error.message);
      
      // 生成失败报告
      const report = this.generateBuildReport({ success: false, error });
      
      process.exit(1);
    }
  }
}

// 解析命令行参数
function parseArgs() {
  const args = process.argv.slice(2);
  const options = {};
  
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    switch (arg) {
      case '--skip-tests':
        options.skipTests = true;
        break;
      case '--skip-linting':
        options.skipLinting = true;
        break;
      case '--skip-analysis':
        options.skipAnalysis = true;
        break;
      case '--env':
        options.environment = args[++i] || 'production';
        break;
      case '--verbose':
      case '-v':
        options.verbose = true;
        break;
      case '--help':
      case '-h':
        console.log(`
使用方法: node scripts/build-complete.js [选项]

选项:
  --skip-tests      跳过测试
  --skip-linting    跳过代码检查
  --skip-analysis   跳过构建分析
  --env <env>       设置构建环境 (development|production)
  --verbose, -v     详细输出
  --help, -h        显示帮助信息

示例:
  node scripts/build-complete.js
  node scripts/build-complete.js --env production --verbose
  node scripts/build-complete.js --skip-tests --skip-linting
        `);
        process.exit(0);
        break;
    }
  }
  
  return options;
}

// 如果直接运行此脚本
const isMainModule = process.argv[1] && import.meta.url.includes(process.argv[1].split(/[/\\]/).pop());
if (isMainModule) {
  const options = parseArgs();
  const buildManager = new CompleteBuildManager(options);
  buildManager.run();
}

export default CompleteBuildManager;