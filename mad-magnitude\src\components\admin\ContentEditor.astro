---
/**
 * 内容编辑器组件
 * 提供 Markdown 编辑和实时预览功能
 */
export interface Props {
  initialContent?: {
    id?: string;
    collection: string;
    slug: string;
    title: string;
    description?: string;
    draft: boolean;
    featured: boolean;
    tags: string[];
    author: string;
    content: string;
  };
  mode?: 'create' | 'edit';
}

const { initialContent, mode = 'create' } = Astro.props;

// 默认内容
const defaultContent = {
  collection: 'news',
  slug: '',
  title: '',
  description: '',
  draft: true,
  featured: false,
  tags: [],
  author: 'Pennfly',
  content: '# 标题\n\n在这里开始编写内容...',
};

const content = initialContent || defaultContent;
---

<div class="content-editor">
  <!-- 编辑器头部 -->
  <div class="editor-header">
    <div class="editor-title">
      <h2>{mode === 'create' ? '创建新内容' : '编辑内容'}</h2>
      <div class="editor-status">
        <span id="save-status" class="status-indicator">未保存</span>
      </div>
    </div>
    <div class="editor-actions">
      <button id="preview-toggle" class="btn btn--secondary">
        <span class="btn-icon">👁️</span>
        预览
      </button>
      <button id="save-draft" class="btn btn--secondary">
        <span class="btn-icon">💾</span>
        保存草稿
      </button>
      <button id="publish-content" class="btn btn--primary">
        <span class="btn-icon">🚀</span>
        发布
      </button>
    </div>
  </div>

  <!-- 编辑器主体 -->
  <div class="editor-body">
    <!-- 元数据面板 -->
    <div class="metadata-panel">
      <div class="metadata-form">
        <div class="form-row">
          <div class="form-group">
            <label for="content-collection" class="form-label">集合</label>
            <select id="content-collection" class="form-select" value={content.collection}>
              <option value="news">📰 动态资讯</option>
              <option value="logs">📔 研究日志</option>
              <option value="research">📊 研究报告</option>
              <option value="reflections">💭 反思记录</option>
              <option value="economics">💰 经济研究</option>
              <option value="philosophy">🤔 哲学研究</option>
              <option value="internet">🌐 互联网研究</option>
              <option value="ai">🤖 AI研究</option>
              <option value="future">🔮 未来研究</option>
              <option value="products">🛠️ 产品发布</option>
            </select>
          </div>

          <div class="form-group">
            <label for="content-slug" class="form-label">URL 标识符</label>
            <input
              type="text"
              id="content-slug"
              class="form-input"
              value={content.slug}
              placeholder="url-friendly-slug"
              pattern="[a-zA-Z0-9_-]+"
              title="只允许字母、数字、连字符和下划线"
            />
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="content-title" class="form-label">标题</label>
            <input
              type="text"
              id="content-title"
              class="form-input"
              value={content.title}
              placeholder="输入内容标题"
            />
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="content-description" class="form-label">描述</label>
            <textarea
              id="content-description"
              class="form-textarea"
              rows="2"
              placeholder="简短描述内容...">{content.description}</textarea
            >
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="content-tags" class="form-label">标签</label>
            <input
              type="text"
              id="content-tags"
              class="form-input"
              value={content.tags.join(', ')}
              placeholder="标签1, 标签2, 标签3"
            />
            <div class="form-help">用逗号分隔多个标签</div>
          </div>

          <div class="form-group">
            <label for="content-author" class="form-label">作者</label>
            <input type="text" id="content-author" class="form-input" value={content.author} />
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <div class="checkbox-group">
              <label class="checkbox-label">
                <input
                  type="checkbox"
                  id="content-draft"
                  class="checkbox-input"
                  checked={content.draft}
                />
                <span class="checkbox-text">保存为草稿</span>
              </label>
            </div>
          </div>

          <div class="form-group">
            <div class="checkbox-group">
              <label class="checkbox-label">
                <input
                  type="checkbox"
                  id="content-featured"
                  class="checkbox-input"
                  checked={content.featured}
                />
                <span class="checkbox-text">设为特色内容</span>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑器和预览区域 -->
    <div class="editor-content">
      <div class="editor-tabs">
        <button id="edit-tab" class="tab-button active">编辑</button>
        <button id="preview-tab" class="tab-button">预览</button>
        <button id="split-tab" class="tab-button">分屏</button>
      </div>

      <div class="editor-panes">
        <!-- 编辑面板 -->
        <div id="edit-pane" class="editor-pane active">
          <div class="editor-toolbar">
            <div class="toolbar-group">
              <button class="toolbar-btn" data-action="bold" title="粗体">
                <strong>B</strong>
              </button>
              <button class="toolbar-btn" data-action="italic" title="斜体">
                <em>I</em>
              </button>
              <button class="toolbar-btn" data-action="heading" title="标题"> H </button>
              <button class="toolbar-btn" data-action="link" title="链接"> 🔗 </button>
              <button class="toolbar-btn" data-action="image" title="图片"> 🖼️ </button>
              <button class="toolbar-btn" data-action="code" title="代码"> &lt;/&gt; </button>
              <button class="toolbar-btn" data-action="list" title="列表"> 📝 </button>
              <button class="toolbar-btn" data-action="quote" title="引用"> 💬 </button>
            </div>
            <div class="toolbar-info">
              <span id="word-count">0 字</span>
              <span id="line-count">0 行</span>
            </div>
          </div>
          <textarea
            id="content-editor"
            class="markdown-editor"
            placeholder="在这里使用 Markdown 语法编写内容...">{content.content}</textarea
          >
        </div>

        <!-- 预览面板 -->
        <div id="preview-pane" class="preview-pane">
          <div class="preview-content" id="preview-content">
            <div class="preview-loading">
              <div class="loading-spinner"></div>
              <span>生成预览中...</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 保存确认对话框 -->
  <div id="save-dialog" class="dialog-overlay" style="display: none;">
    <div class="dialog">
      <div class="dialog-header">
        <h3>保存内容</h3>
        <button id="close-dialog" class="dialog-close">✕</button>
      </div>
      <div class="dialog-body">
        <p>确定要保存这个内容吗？</p>
        <div class="dialog-info">
          <div><strong>标题:</strong> <span id="dialog-title"></span></div>
          <div><strong>集合:</strong> <span id="dialog-collection"></span></div>
          <div><strong>状态:</strong> <span id="dialog-status"></span></div>
        </div>
      </div>
      <div class="dialog-actions">
        <button id="confirm-save" class="btn btn--primary">确认保存</button>
        <button id="cancel-save" class="btn btn--secondary">取消</button>
      </div>
    </div>
  </div>
</div>

<style>
  .content-editor {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: white;
    border-radius: 0.75rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  /* 编辑器头部 */
  .editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
  }

  .editor-title h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
  }

  .status-indicator {
    font-size: 0.875rem;
    color: #64748b;
    margin-top: 0.25rem;
  }

  .editor-actions {
    display: flex;
    gap: 0.75rem;
  }

  /* 按钮样式 */
  .btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .btn--primary {
    background: #3b82f6;
    color: white;
  }

  .btn--primary:hover {
    background: #2563eb;
  }

  .btn--secondary {
    background: #f1f5f9;
    color: #64748b;
    border: 1px solid #e2e8f0;
  }

  .btn--secondary:hover {
    background: #e2e8f0;
    color: #475569;
  }

  .btn-icon {
    font-size: 1rem;
  }

  /* 编辑器主体 */
  .editor-body {
    display: flex;
    flex: 1;
    overflow: hidden;
  }

  /* 元数据面板 */
  .metadata-panel {
    width: 300px;
    background: #f8fafc;
    border-right: 1px solid #e2e8f0;
    overflow-y: auto;
  }

  .metadata-form {
    padding: 1.5rem;
  }

  .form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .form-row:last-child {
    margin-bottom: 0;
  }

  .form-group {
    flex: 1;
  }

  .form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
  }

  .form-input,
  .form-select,
  .form-textarea {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    background: white;
    transition: border-color 0.2s ease;
  }

  .form-input:focus,
  .form-select:focus,
  .form-textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .form-textarea {
    resize: vertical;
    min-height: 60px;
  }

  .form-help {
    font-size: 0.75rem;
    color: #64748b;
    margin-top: 0.25rem;
  }

  .checkbox-group {
    display: flex;
    align-items: center;
  }

  .checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
  }

  .checkbox-input {
    width: 1rem;
    height: 1rem;
  }

  .checkbox-text {
    font-size: 0.875rem;
    color: #374151;
  }

  /* 编辑器内容区域 */
  .editor-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .editor-tabs {
    display: flex;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
  }

  .tab-button {
    padding: 0.75rem 1.5rem;
    border: none;
    background: none;
    font-size: 0.875rem;
    font-weight: 500;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 2px solid transparent;
  }

  .tab-button:hover {
    color: #374151;
    background: #f1f5f9;
  }

  .tab-button.active {
    color: #3b82f6;
    border-bottom-color: #3b82f6;
    background: white;
  }

  .editor-panes {
    flex: 1;
    display: flex;
    overflow: hidden;
  }

  .editor-pane {
    flex: 1;
    display: none;
    flex-direction: column;
    overflow: hidden;
  }

  .editor-pane.active {
    display: flex;
  }

  .editor-pane.split {
    display: flex;
    flex: 1;
  }

  /* 编辑器工具栏 */
  .editor-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
  }

  .toolbar-group {
    display: flex;
    gap: 0.25rem;
  }

  .toolbar-btn {
    padding: 0.5rem;
    border: none;
    background: none;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .toolbar-btn:hover {
    background: #e2e8f0;
  }

  .toolbar-info {
    display: flex;
    gap: 1rem;
    font-size: 0.75rem;
    color: #64748b;
  }

  /* Markdown 编辑器 */
  .markdown-editor {
    flex: 1;
    padding: 1rem;
    border: none;
    outline: none;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.6;
    resize: none;
    background: white;
  }

  /* 预览面板 */
  .preview-pane {
    flex: 1;
    display: none;
    overflow-y: auto;
    background: white;
  }

  .preview-pane.active {
    display: block;
  }

  .preview-pane.split {
    display: block;
    border-left: 1px solid #e2e8f0;
  }

  .preview-content {
    padding: 1rem;
    max-width: none;
  }

  .preview-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 2rem;
    color: #64748b;
  }

  .loading-spinner {
    width: 1rem;
    height: 1rem;
    border: 2px solid #e2e8f0;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  /* 对话框 */
  .dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .dialog {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
  }

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e2e8f0;
  }

  .dialog-header h3 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
  }

  .dialog-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    color: #64748b;
    padding: 0.25rem;
  }

  .dialog-close:hover {
    color: #374151;
  }

  .dialog-body {
    padding: 1.5rem;
  }

  .dialog-info {
    margin-top: 1rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 0.375rem;
    font-size: 0.875rem;
  }

  .dialog-info div {
    margin-bottom: 0.5rem;
  }

  .dialog-info div:last-child {
    margin-bottom: 0;
  }

  .dialog-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
    border-top: 1px solid #e2e8f0;
  }

  /* 响应式设计 */
  @media (max-width: 1024px) {
    .metadata-panel {
      width: 250px;
    }

    .form-row {
      flex-direction: column;
      gap: 0.5rem;
    }
  }

  @media (max-width: 768px) {
    .editor-header {
      flex-direction: column;
      gap: 1rem;
      align-items: stretch;
    }

    .editor-actions {
      justify-content: center;
    }

    .editor-body {
      flex-direction: column;
    }

    .metadata-panel {
      width: 100%;
      max-height: 300px;
    }

    .toolbar-group {
      flex-wrap: wrap;
    }

    .toolbar-info {
      flex-direction: column;
      gap: 0.25rem;
    }
  }
</style>

<script>
  class ContentEditor {
    private mode: 'create' | 'edit';
    private contentId?: string;
    private currentView: 'edit' | 'preview' | 'split' = 'edit';
    private isDirty = false;
    private autoSaveTimer?: number;

    constructor(mode: 'create' | 'edit', contentId?: string) {
      this.mode = mode;
      this.contentId = contentId;
      this.bindEvents();
      this.initializeEditor();
    }

    private bindEvents() {
      // 标签页切换
      document.getElementById('edit-tab')?.addEventListener('click', () => this.switchView('edit'));
      document
        .getElementById('preview-tab')
        ?.addEventListener('click', () => this.switchView('preview'));
      document
        .getElementById('split-tab')
        ?.addEventListener('click', () => this.switchView('split'));

      // 编辑器工具栏
      document.querySelectorAll('.toolbar-btn').forEach(btn => {
        btn.addEventListener('click', e => {
          const action = (e.target as HTMLElement).dataset.action;
          if (action) this.executeToolbarAction(action);
        });
      });

      // 保存按钮
      document.getElementById('save-draft')?.addEventListener('click', () => this.saveDraft());
      document
        .getElementById('publish-content')
        ?.addEventListener('click', () => this.publishContent());

      // 对话框
      document.getElementById('close-dialog')?.addEventListener('click', () => this.closeDialog());
      document.getElementById('confirm-save')?.addEventListener('click', () => this.confirmSave());
      document.getElementById('cancel-save')?.addEventListener('click', () => this.closeDialog());

      // 编辑器内容变化
      const editor = document.getElementById('content-editor') as HTMLTextAreaElement;
      editor?.addEventListener('input', () => {
        this.markDirty();
        this.updateWordCount();
        this.updatePreview();
        this.scheduleAutoSave();
      });

      // 标题变化时自动生成 slug
      const titleInput = document.getElementById('content-title') as HTMLInputElement;
      titleInput?.addEventListener('input', () => {
        this.generateSlug();
        this.markDirty();
      });

      // 其他表单字段变化
      const formInputs = document.querySelectorAll(
        '.form-input, .form-select, .form-textarea, .checkbox-input'
      );
      formInputs.forEach(input => {
        input.addEventListener('change', () => this.markDirty());
      });

      // 页面离开前确认
      window.addEventListener('beforeunload', e => {
        if (this.isDirty) {
          e.preventDefault();
          e.returnValue = '您有未保存的更改，确定要离开吗？';
        }
      });
    }

    private initializeEditor() {
      this.updateWordCount();
      this.updatePreview();
      this.switchView('edit');
    }

    private switchView(view: 'edit' | 'preview' | 'split') {
      this.currentView = view;

      // 更新标签页状态
      document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
      document.getElementById(`${view}-tab`)?.classList.add('active');

      // 更新面板显示
      const editPane = document.getElementById('edit-pane');
      const previewPane = document.getElementById('preview-pane');

      if (!editPane || !previewPane) return;

      editPane.classList.remove('active', 'split');
      previewPane.classList.remove('active', 'split');

      switch (view) {
        case 'edit':
          editPane.classList.add('active');
          break;
        case 'preview':
          previewPane.classList.add('active');
          this.updatePreview();
          break;
        case 'split':
          editPane.classList.add('split');
          previewPane.classList.add('split');
          this.updatePreview();
          break;
      }
    }

    private executeToolbarAction(action: string) {
      const editor = document.getElementById('content-editor') as HTMLTextAreaElement;
      if (!editor) return;

      const start = editor.selectionStart;
      const end = editor.selectionEnd;
      const selectedText = editor.value.substring(start, end);
      let replacement = '';

      switch (action) {
        case 'bold':
          replacement = `**${selectedText || '粗体文本'}**`;
          break;
        case 'italic':
          replacement = `*${selectedText || '斜体文本'}*`;
          break;
        case 'heading':
          replacement = `## ${selectedText || '标题'}`;
          break;
        case 'link':
          replacement = `[${selectedText || '链接文本'}](URL)`;
          break;
        case 'image':
          replacement = `![${selectedText || '图片描述'}](图片URL)`;
          break;
        case 'code':
          replacement = selectedText.includes('\n')
            ? `\`\`\`\n${selectedText || '代码'}\n\`\`\``
            : `\`${selectedText || '代码'}\``;
          break;
        case 'list':
          replacement = `- ${selectedText || '列表项'}`;
          break;
        case 'quote':
          replacement = `> ${selectedText || '引用内容'}`;
          break;
      }

      editor.value = editor.value.substring(0, start) + replacement + editor.value.substring(end);
      editor.focus();

      // 设置光标位置
      const newPos = start + replacement.length;
      editor.setSelectionRange(newPos, newPos);

      this.markDirty();
      this.updateWordCount();
      this.updatePreview();
    }

    private generateSlug() {
      const titleInput = document.getElementById('content-title') as HTMLInputElement;
      const slugInput = document.getElementById('content-slug') as HTMLInputElement;

      if (!titleInput || !slugInput || slugInput.value) return;

      const slug = titleInput.value
        .toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();

      slugInput.value = slug;
    }

    private updateWordCount() {
      const editor = document.getElementById('content-editor') as HTMLTextAreaElement;
      const wordCountEl = document.getElementById('word-count');
      const lineCountEl = document.getElementById('line-count');

      if (!editor || !wordCountEl || !lineCountEl) return;

      const content = editor.value;
      const wordCount = content.trim() ? content.trim().split(/\s+/).length : 0;
      const lineCount = content.split('\n').length;

      wordCountEl.textContent = `${wordCount} 字`;
      lineCountEl.textContent = `${lineCount} 行`;
    }

    private async updatePreview() {
      if (this.currentView === 'edit') return;

      const editor = document.getElementById('content-editor') as HTMLTextAreaElement;
      const previewContent = document.getElementById('preview-content');

      if (!editor || !previewContent) return;

      const markdown = editor.value;

      // 简单的 Markdown 预览（生产环境建议使用专业的 Markdown 解析器）
      const html = this.parseMarkdown(markdown);
      previewContent.innerHTML = html;
    }

    private parseMarkdown(markdown: string): string {
      // 简化的 Markdown 解析器
      const html = markdown
      // 标题
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      // 粗体和斜体
      .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
      .replace(/\*(.*)\*/gim, '<em>$1</em>')
      // 链接
      .replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2">$1</a>')
      // 代码
      .replace(/`([^`]+)`/gim, '<code>$1</code>')
      // 换行
      .replace(/\n/gim, '<br>');

      return html || '<p>开始编写内容...</p>';
    }

    private markDirty() {
      this.isDirty = true;
      const statusEl = document.getElementById('save-status');
      if (statusEl) {
        statusEl.textContent = '未保存';
        statusEl.style.color = '#ef4444';
      }
    }

    private markClean() {
      this.isDirty = false;
      const statusEl = document.getElementById('save-status');
      if (statusEl) {
        statusEl.textContent = '已保存';
        statusEl.style.color = '#10b981';
      }
    }

    private scheduleAutoSave() {
      if (this.autoSaveTimer) {
        clearTimeout(this.autoSaveTimer);
      }

      this.autoSaveTimer = window.setTimeout(() => {
        this.saveDraft(true);
      }, 30000); // 30秒后自动保存
    }

    private async saveDraft(isAutoSave = false) {
      const data = this.collectFormData();
      data.draft = true;

      try {
        await this.saveContent(data);
        if (!isAutoSave) {
          alert('草稿保存成功！');
        }
      } catch (error) {
        if (!isAutoSave) {
          alert('保存失败：' + error);
        }
      }
    }

    private publishContent() {
      const data = this.collectFormData();

      // 显示确认对话框
      this.showSaveDialog(data, false);
    }

    private showSaveDialog(data: any, isDraft: boolean) {
      const dialog = document.getElementById('save-dialog');
      const titleEl = document.getElementById('dialog-title');
      const collectionEl = document.getElementById('dialog-collection');
      const statusEl = document.getElementById('dialog-status');

      if (!dialog || !titleEl || !collectionEl || !statusEl) return;

      titleEl.textContent = data.title || '未命名';
      collectionEl.textContent = this.getCollectionDisplayName(data.collection);
      statusEl.textContent = isDraft ? '草稿' : '发布';

      dialog.style.display = 'flex';

      // 存储数据供确认时使用
      (dialog as any)._pendingData = { ...data, draft: isDraft };
    }

    private closeDialog() {
      const dialog = document.getElementById('save-dialog');
      if (dialog) {
        dialog.style.display = 'none';
      }
    }

    private async confirmSave() {
      const dialog = document.getElementById('save-dialog');
      const data = (dialog as any)?._pendingData;

      if (!data) return;

      try {
        await this.saveContent(data);
        this.closeDialog();
        alert(data.draft ? '草稿保存成功！' : '内容发布成功！');

        if (!data.draft) {
          // 发布后跳转到内容管理页面
          window.location.href = '/admin/content';
        }
      } catch (error) {
        alert('保存失败：' + error);
      }
    }

    private collectFormData() {
      const getElementValue = (id: string) => {
        const el = document.getElementById(id) as
          | HTMLInputElement
          | HTMLTextAreaElement
          | HTMLSelectElement;
        return el?.value || '';
      };

      const getCheckboxValue = (id: string) => {
        const el = document.getElementById(id) as HTMLInputElement;
        return el?.checked || false;
      };

      const tags = getElementValue('content-tags')
        .split(',')
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0);

      return {
        collection: getElementValue('content-collection'),
        slug: getElementValue('content-slug'),
        title: getElementValue('content-title'),
        description: getElementValue('content-description'),
        tags,
        author: getElementValue('content-author'),
        draft: getCheckboxValue('content-draft'),
        featured: getCheckboxValue('content-featured'),
        content: getElementValue('content-editor'),
      };
    }

    private async saveContent(data: any) {
      const url =
        this.mode === 'create'
          ? '/api/content/create'
          : `/api/content/${encodeURIComponent(this.contentId!)}`;

      const method = this.mode === 'create' ? 'POST' : 'PUT';

      const payload = this.mode === 'create' ? data : { frontmatter: data, content: data.content };

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || '保存失败');
      }

      const result = await response.json();

      // 如果是创建模式，切换到编辑模式
      if (this.mode === 'create') {
        this.mode = 'edit';
        this.contentId = result.id;
        // 更新 URL
        window.history.replaceState({}, '', `/admin/content/edit/${encodeURIComponent(result.id)}`);
      }

      this.markClean();
      return result;
    }

    private getCollectionDisplayName(collection: string): string {
      const names: Record<string, string> = {
        news: '动态资讯',
        logs: '研究日志',
        research: '研究报告',
        reflections: '反思记录',
        economics: '经济研究',
        philosophy: '哲学研究',
        internet: '互联网研究',
        ai: 'AI研究',
        future: '未来研究',
        products: '产品发布',
      };
      return names[collection] || collection;
    }
  }

  // 初始化编辑器
  document.addEventListener('DOMContentLoaded', () => {
    const pathParts = window.location.pathname.split('/');
    const isEdit = pathParts.includes('edit');
    const contentId = isEdit ? decodeURIComponent(pathParts[pathParts.length - 1]) : undefined;

    new ContentEditor(isEdit ? 'edit' : 'create', contentId);
  });
</script>
