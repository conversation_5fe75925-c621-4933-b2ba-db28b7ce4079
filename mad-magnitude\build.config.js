/**
 * 构建配置和优化设置
 * 这个文件包含了所有构建相关的配置和优化选项
 */

import BuildErrorHandler from './scripts/build-error-handler.js';

// 构建环境配置
export const BUILD_ENVIRONMENTS = {
  development: {
    minify: false,
    sourcemap: true,
    cssMinify: false,
    reportCompressedSize: false,
    chunkSizeWarningLimit: 2000,
  },
  production: {
    minify: 'esbuild',
    sourcemap: false,
    cssMinify: true,
    reportCompressedSize: true,
    chunkSizeWarningLimit: 1000,
  },
  analyze: {
    minify: 'esbuild',
    sourcemap: true,
    cssMinify: true,
    reportCompressedSize: true,
    chunkSizeWarningLimit: 500,
  }
};

// 代码分割策略
export const CHUNK_STRATEGY = {
  // 第三方库分割
  vendor: (id) => {
    if (id.includes('node_modules')) {
      if (id.includes('astro')) return 'vendor-astro';
      if (id.includes('tailwindcss')) return 'vendor-tailwind';
      if (id.includes('react') || id.includes('vue') || id.includes('svelte')) return 'vendor-framework';
      return 'vendor';
    }
    return null;
  },
  
  // 应用代码分割
  app: (id) => {
    if (id.includes('src/utils')) return 'utils';
    if (id.includes('src/components')) return 'components';
    if (id.includes('src/layouts')) return 'layouts';
    if (id.includes('src/pages')) return 'pages';
    if (id.includes('src/content')) return 'content';
    return null;
  },
  
  // 组合分割策略
  combined: (id) => {
    return CHUNK_STRATEGY.vendor(id) || CHUNK_STRATEGY.app(id);
  }
};

// 资源文件命名策略
export const ASSET_NAMING = {
  // 入口文件
  entryFileNames: (chunkInfo) => {
    const isDev = process.env.NODE_ENV === 'development';
    return isDev ? 'assets/[name].js' : 'assets/[name].[hash].js';
  },
  
  // 代码块文件
  chunkFileNames: (chunkInfo) => {
    const isDev = process.env.NODE_ENV === 'development';
    return isDev ? 'assets/[name].js' : 'assets/[name].[hash].js';
  },
  
  // 资源文件
  assetFileNames: (assetInfo) => {
    const isDev = process.env.NODE_ENV === 'development';
    const info = assetInfo.name.split('.');
    const ext = info[info.length - 1];
    
    // 图片文件
    if (/\.(png|jpe?g|svg|gif|tiff|bmp|ico|webp|avif)$/i.test(assetInfo.name)) {
      return isDev 
        ? `assets/images/[name].${ext}`
        : `assets/images/[name].[hash].${ext}`;
    }
    
    // 字体文件
    if (/\.(woff2?|eot|ttf|otf)$/i.test(assetInfo.name)) {
      return isDev 
        ? `assets/fonts/[name].${ext}`
        : `assets/fonts/[name].[hash].${ext}`;
    }
    
    // CSS 文件
    if (/\.css$/i.test(assetInfo.name)) {
      return isDev 
        ? `assets/styles/[name].${ext}`
        : `assets/styles/[name].[hash].${ext}`;
    }
    
    // 其他文件
    return isDev 
      ? `assets/[name].${ext}`
      : `assets/[name].[hash].${ext}`;
  }
};

// 构建优化配置
export const BUILD_OPTIMIZATION = {
  // 依赖预构建
  optimizeDeps: {
    include: [
      'tailwindcss',
      // 添加其他需要预构建的依赖
    ],
    exclude: [
      // 排除不需要预构建的依赖
    ],
  },
  
  // CSS 优化
  css: {
    modules: {
      localsConvention: 'camelCase',
    },
    postcss: {
      // PostCSS 插件配置
    },
  },
  
  // 静态资源处理
  assetsInclude: [
    '**/*.woff',
    '**/*.woff2', 
    '**/*.ttf',
    '**/*.otf',
    '**/*.pdf',
  ],
  
  // 构建目标
  target: 'es2022',
  
  // 内联限制
  assetsInlineLimit: 4096, // 4KB
};

// 性能预算配置
export const PERFORMANCE_BUDGET = {
  // 文件大小限制
  maxAssetSize: 500 * 1024, // 500KB
  maxEntrypointSize: 1000 * 1024, // 1MB
  
  // 警告阈值
  warningThresholds: {
    javascript: 200 * 1024, // 200KB
    css: 100 * 1024, // 100KB
    images: 300 * 1024, // 300KB
    fonts: 150 * 1024, // 150KB
  },
  
  // 错误阈值
  errorThresholds: {
    javascript: 500 * 1024, // 500KB
    css: 200 * 1024, // 200KB
    images: 1000 * 1024, // 1MB
    fonts: 300 * 1024, // 300KB
  },
};

// 构建监控配置
export const BUILD_MONITORING = {
  // 启用监控功能
  enabled: true,
  
  // 报告目录
  reportsDir: './reports',
  
  // 保留的构建历史数量
  maxHistoryEntries: 50,
  
  // 性能警告阈值
  performanceThresholds: {
    buildTime: 60000, // 1分钟
    bundleSize: 5 * 1024 * 1024, // 5MB
    chunkCount: 50,
  },
  
  // 错误处理
  errorHandling: {
    logToFile: true,
    showFriendlyErrors: true,
    generateSolutions: true,
  },
};

// 获取当前环境的构建配置
export function getBuildConfig(env = process.env.NODE_ENV || 'development') {
  const baseConfig = BUILD_ENVIRONMENTS[env] || BUILD_ENVIRONMENTS.development;
  
  return {
    ...baseConfig,
    rollupOptions: {
      output: {
        // 文件命名
        entryFileNames: ASSET_NAMING.entryFileNames,
        chunkFileNames: ASSET_NAMING.chunkFileNames,
        assetFileNames: ASSET_NAMING.assetFileNames,
        
        // 代码分割
        manualChunks: CHUNK_STRATEGY.combined,
        
        // 压缩选项
        compact: env === 'production',
        comments: env !== 'production',
      },
      
      // 外部依赖
      external: [],
      
      // 插件
      plugins: [],
    },
    
    // 其他优化选项
    ...BUILD_OPTIMIZATION,
  };
}

// 设置构建错误处理
export function setupBuildErrorHandling() {
  if (BUILD_MONITORING.errorHandling.logToFile) {
    const errorHandler = new BuildErrorHandler();
    errorHandler.setupGlobalErrorHandlers();
    return errorHandler;
  }
  return null;
}

// 验证构建配置
export function validateBuildConfig(config) {
  const warnings = [];
  const errors = [];
  
  // 检查性能预算
  if (config.chunkSizeWarningLimit > PERFORMANCE_BUDGET.maxAssetSize) {
    warnings.push(`chunkSizeWarningLimit (${config.chunkSizeWarningLimit}) 超过建议的最大资源大小 (${PERFORMANCE_BUDGET.maxAssetSize})`);
  }
  
  // 检查目标环境
  if (!['es2020', 'es2021', 'es2022', 'esnext'].includes(config.target)) {
    warnings.push(`构建目标 "${config.target}" 可能不被所有浏览器支持`);
  }
  
  // 检查源码映射
  if (config.sourcemap && process.env.NODE_ENV === 'production') {
    warnings.push('生产环境启用了源码映射，可能会暴露源代码');
  }
  
  return { warnings, errors };
}

// 导出默认配置
export default {
  BUILD_ENVIRONMENTS,
  CHUNK_STRATEGY,
  ASSET_NAMING,
  BUILD_OPTIMIZATION,
  PERFORMANCE_BUDGET,
  BUILD_MONITORING,
  getBuildConfig,
  setupBuildErrorHandling,
  validateBuildConfig,
};