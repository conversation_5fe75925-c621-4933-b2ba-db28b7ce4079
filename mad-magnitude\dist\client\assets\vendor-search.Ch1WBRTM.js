function m(e){return Array.isArray?Array.isArray(e):st(e)==="[object Array]"}function at(e){if(typeof e=="string")return e;let t=e+"";return t=="0"&&1/e==-1/0?"-0":t}function lt(e){return e==null?"":at(e)}function M(e){return typeof e=="string"}function tt(e){return typeof e=="number"}function ft(e){return e===!0||e===!1||dt(e)&&st(e)=="[object Boolean]"}function et(e){return typeof e=="object"}function dt(e){return et(e)&&e!==null}function E(e){return e!=null}function P(e){return!e.trim().length}function st(e){return e==null?e===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}const gt="Incorrect 'index' type",At=e=>`Invalid value for key ${e}`,pt=e=>`Pattern length exceeds max of ${e}.`,Et=e=>`Missing ${e} property in key`,Ct=e=>`Property 'weight' in key '${e}' must be a positive integer`,J=Object.prototype.hasOwnProperty;class Ft{constructor(t){this._keys=[],this._keyMap={};let s=0;t.forEach(n=>{let r=nt(n);this._keys.push(r),this._keyMap[r.id]=r,s+=r.weight}),this._keys.forEach(n=>{n.weight/=s})}get(t){return this._keyMap[t]}keys(){return this._keys}toJSON(){return JSON.stringify(this._keys)}}function nt(e){let t=null,s=null,n=null,r=1,i=null;if(M(e)||m(e))n=e,t=U(e),s=j(e);else{if(!J.call(e,"name"))throw new Error(Et("name"));const u=e.name;if(n=u,J.call(e,"weight")&&(r=e.weight,r<=0))throw new Error(Ct(u));t=U(u),s=j(u),i=e.getFn}return{path:t,id:s,weight:r,src:n,getFn:i}}function U(e){return m(e)?e:e.split(".")}function j(e){return m(e)?e.join("."):e}function Bt(e,t){let s=[],n=!1;const r=(i,u,c)=>{if(E(i))if(!u[c])s.push(i);else{let o=u[c];const h=i[o];if(!E(h))return;if(c===u.length-1&&(M(h)||tt(h)||ft(h)))s.push(lt(h));else if(m(h)){n=!0;for(let a=0,f=h.length;a<f;a+=1)r(h[a],u,c+1)}else u.length&&r(h,u,c+1)}};return r(e,M(t)?t.split("."):t,0),n?s:s[0]}const Mt={includeMatches:!1,findAllMatches:!1,minMatchCharLength:1},Dt={isCaseSensitive:!1,ignoreDiacritics:!1,includeScore:!1,keys:[],shouldSort:!0,sortFn:(e,t)=>e.score===t.score?e.idx<t.idx?-1:1:e.score<t.score?-1:1},mt={location:0,threshold:.6,distance:100},yt={useExtendedSearch:!1,getFn:Bt,ignoreLocation:!1,ignoreFieldNorm:!1,fieldNormWeight:1};var l={...Dt,...Mt,...mt,...yt};const xt=/[^ ]+/g;function _t(e=1,t=3){const s=new Map,n=Math.pow(10,t);return{get(r){const i=r.match(xt).length;if(s.has(i))return s.get(i);const u=1/Math.pow(i,.5*e),c=parseFloat(Math.round(u*n)/n);return s.set(i,c),c},clear(){s.clear()}}}class Y{constructor({getFn:t=l.getFn,fieldNormWeight:s=l.fieldNormWeight}={}){this.norm=_t(s,3),this.getFn=t,this.isCreated=!1,this.setIndexRecords()}setSources(t=[]){this.docs=t}setIndexRecords(t=[]){this.records=t}setKeys(t=[]){this.keys=t,this._keysMap={},t.forEach((s,n)=>{this._keysMap[s.id]=n})}create(){this.isCreated||!this.docs.length||(this.isCreated=!0,M(this.docs[0])?this.docs.forEach((t,s)=>{this._addString(t,s)}):this.docs.forEach((t,s)=>{this._addObject(t,s)}),this.norm.clear())}add(t){const s=this.size();M(t)?this._addString(t,s):this._addObject(t,s)}removeAt(t){this.records.splice(t,1);for(let s=t,n=this.size();s<n;s+=1)this.records[s].i-=1}getValueForItemAtKeyId(t,s){return t[this._keysMap[s]]}size(){return this.records.length}_addString(t,s){if(!E(t)||P(t))return;let n={v:t,i:s,n:this.norm.get(t)};this.records.push(n)}_addObject(t,s){let n={i:s,$:{}};this.keys.forEach((r,i)=>{let u=r.getFn?r.getFn(t):this.getFn(t,r.path);if(E(u)){if(m(u)){let c=[];const o=[{nestedArrIndex:-1,value:u}];for(;o.length;){const{nestedArrIndex:h,value:a}=o.pop();if(E(a))if(M(a)&&!P(a)){let f={v:a,i:h,n:this.norm.get(a)};c.push(f)}else m(a)&&a.forEach((f,d)=>{o.push({nestedArrIndex:d,value:f})})}n.$[i]=c}else if(M(u)&&!P(u)){let c={v:u,n:this.norm.get(u)};n.$[i]=c}}}),this.records.push(n)}toJSON(){return{keys:this.keys,records:this.records}}}function rt(e,t,{getFn:s=l.getFn,fieldNormWeight:n=l.fieldNormWeight}={}){const r=new Y({getFn:s,fieldNormWeight:n});return r.setKeys(e.map(nt)),r.setSources(t),r.create(),r}function It(e,{getFn:t=l.getFn,fieldNormWeight:s=l.fieldNormWeight}={}){const{keys:n,records:r}=e,i=new Y({getFn:t,fieldNormWeight:s});return i.setKeys(n),i.setIndexRecords(r),i}function $(e,{errors:t=0,currentLocation:s=0,expectedLocation:n=0,distance:r=l.distance,ignoreLocation:i=l.ignoreLocation}={}){const u=t/e.length;if(i)return u;const c=Math.abs(n-s);return r?u+c/r:c?1:u}function St(e=[],t=l.minMatchCharLength){let s=[],n=-1,r=-1,i=0;for(let u=e.length;i<u;i+=1){let c=e[i];c&&n===-1?n=i:!c&&n!==-1&&(r=i-1,r-n+1>=t&&s.push([n,r]),n=-1)}return e[i-1]&&i-n>=t&&s.push([n,i-1]),s}const w=32;function wt(e,t,s,{location:n=l.location,distance:r=l.distance,threshold:i=l.threshold,findAllMatches:u=l.findAllMatches,minMatchCharLength:c=l.minMatchCharLength,includeMatches:o=l.includeMatches,ignoreLocation:h=l.ignoreLocation}={}){if(t.length>w)throw new Error(pt(w));const a=t.length,f=e.length,d=Math.max(0,Math.min(n,f));let g=i,A=d;const p=c>1||o,F=p?Array(f):[];let y;for(;(y=e.indexOf(t,A))>-1;){let C=$(t,{currentLocation:y,expectedLocation:d,distance:r,ignoreLocation:h});if(g=Math.min(C,g),A=y+a,p){let x=0;for(;x<a;)F[y+x]=1,x+=1}}A=-1;let D=[],L=1,S=a+f;const ht=1<<a-1;for(let C=0;C<a;C+=1){let x=0,_=S;for(;x<_;)$(t,{errors:C,currentLocation:d+_,expectedLocation:d,distance:r,ignoreLocation:h})<=g?x=_:S=_,_=Math.floor((S-x)/2+x);S=_;let V=Math.max(1,d-_+1),T=u?f:Math.min(d+_,f)+a,R=Array(T+2);R[T+1]=(1<<C)-1;for(let B=T;B>=V;B-=1){let O=B-1,Q=s[e.charAt(O)];if(p&&(F[O]=+!!Q),R[B]=(R[B+1]<<1|1)&Q,C&&(R[B]|=(D[B+1]|D[B])<<1|1|D[B+1]),R[B]&ht&&(L=$(t,{errors:C,currentLocation:O,expectedLocation:d,distance:r,ignoreLocation:h}),L<=g)){if(g=L,A=O,A<=d)break;V=Math.max(1,2*d-A)}}if($(t,{errors:C+1,currentLocation:d,expectedLocation:d,distance:r,ignoreLocation:h})>g)break;D=R}const v={isMatch:A>=0,score:Math.max(.001,L)};if(p){const C=St(F,c);C.length?o&&(v.indices=C):v.isMatch=!1}return v}function Lt(e){let t={};for(let s=0,n=e.length;s<n;s+=1){const r=e.charAt(s);t[r]=(t[r]||0)|1<<n-s-1}return t}const k=String.prototype.normalize?e=>e.normalize("NFD").replace(/[\u0300-\u036F\u0483-\u0489\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u064B-\u065F\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u0711\u0730-\u074A\u07A6-\u07B0\u07EB-\u07F3\u07FD\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u08D3-\u08E1\u08E3-\u0903\u093A-\u093C\u093E-\u094F\u0951-\u0957\u0962\u0963\u0981-\u0983\u09BC\u09BE-\u09C4\u09C7\u09C8\u09CB-\u09CD\u09D7\u09E2\u09E3\u09FE\u0A01-\u0A03\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A70\u0A71\u0A75\u0A81-\u0A83\u0ABC\u0ABE-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AE2\u0AE3\u0AFA-\u0AFF\u0B01-\u0B03\u0B3C\u0B3E-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B62\u0B63\u0B82\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD7\u0C00-\u0C04\u0C3E-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C81-\u0C83\u0CBC\u0CBE-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CE2\u0CE3\u0D00-\u0D03\u0D3B\u0D3C\u0D3E-\u0D44\u0D46-\u0D48\u0D4A-\u0D4D\u0D57\u0D62\u0D63\u0D82\u0D83\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DF2\u0DF3\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0EB1\u0EB4-\u0EB9\u0EBB\u0EBC\u0EC8-\u0ECD\u0F18\u0F19\u0F35\u0F37\u0F39\u0F3E\u0F3F\u0F71-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102B-\u103E\u1056-\u1059\u105E-\u1060\u1062-\u1064\u1067-\u106D\u1071-\u1074\u1082-\u108D\u108F\u109A-\u109D\u135D-\u135F\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17B4-\u17D3\u17DD\u180B-\u180D\u1885\u1886\u18A9\u1920-\u192B\u1930-\u193B\u1A17-\u1A1B\u1A55-\u1A5E\u1A60-\u1A7C\u1A7F\u1AB0-\u1ABE\u1B00-\u1B04\u1B34-\u1B44\u1B6B-\u1B73\u1B80-\u1B82\u1BA1-\u1BAD\u1BE6-\u1BF3\u1C24-\u1C37\u1CD0-\u1CD2\u1CD4-\u1CE8\u1CED\u1CF2-\u1CF4\u1CF7-\u1CF9\u1DC0-\u1DF9\u1DFB-\u1DFF\u20D0-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302F\u3099\u309A\uA66F-\uA672\uA674-\uA67D\uA69E\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA823-\uA827\uA880\uA881\uA8B4-\uA8C5\uA8E0-\uA8F1\uA8FF\uA926-\uA92D\uA947-\uA953\uA980-\uA983\uA9B3-\uA9C0\uA9E5\uAA29-\uAA36\uAA43\uAA4C\uAA4D\uAA7B-\uAA7D\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEB-\uAAEF\uAAF5\uAAF6\uABE3-\uABEA\uABEC\uABED\uFB1E\uFE00-\uFE0F\uFE20-\uFE2F]/g,""):e=>e;class it{constructor(t,{location:s=l.location,threshold:n=l.threshold,distance:r=l.distance,includeMatches:i=l.includeMatches,findAllMatches:u=l.findAllMatches,minMatchCharLength:c=l.minMatchCharLength,isCaseSensitive:o=l.isCaseSensitive,ignoreDiacritics:h=l.ignoreDiacritics,ignoreLocation:a=l.ignoreLocation}={}){if(this.options={location:s,threshold:n,distance:r,includeMatches:i,findAllMatches:u,minMatchCharLength:c,isCaseSensitive:o,ignoreDiacritics:h,ignoreLocation:a},t=o?t:t.toLowerCase(),t=h?k(t):t,this.pattern=t,this.chunks=[],!this.pattern.length)return;const f=(g,A)=>{this.chunks.push({pattern:g,alphabet:Lt(g),startIndex:A})},d=this.pattern.length;if(d>w){let g=0;const A=d%w,p=d-A;for(;g<p;)f(this.pattern.substr(g,w),g),g+=w;if(A){const F=d-w;f(this.pattern.substr(F),F)}}else f(this.pattern,0)}searchIn(t){const{isCaseSensitive:s,ignoreDiacritics:n,includeMatches:r}=this.options;if(t=s?t:t.toLowerCase(),t=n?k(t):t,this.pattern===t){let p={isMatch:!0,score:0};return r&&(p.indices=[[0,t.length-1]]),p}const{location:i,distance:u,threshold:c,findAllMatches:o,minMatchCharLength:h,ignoreLocation:a}=this.options;let f=[],d=0,g=!1;this.chunks.forEach(({pattern:p,alphabet:F,startIndex:y})=>{const{isMatch:D,score:L,indices:S}=wt(t,p,F,{location:i+y,distance:u,threshold:c,findAllMatches:o,minMatchCharLength:h,includeMatches:r,ignoreLocation:a});D&&(g=!0),d+=L,D&&S&&(f=[...f,...S])});let A={isMatch:g,score:g?d/this.chunks.length:1};return g&&r&&(A.indices=f),A}}class I{constructor(t){this.pattern=t}static isMultiMatch(t){return X(t,this.multiRegex)}static isSingleMatch(t){return X(t,this.singleRegex)}search(){}}function X(e,t){const s=e.match(t);return s?s[1]:null}class Rt extends I{constructor(t){super(t)}static get type(){return"exact"}static get multiRegex(){return/^="(.*)"$/}static get singleRegex(){return/^=(.*)$/}search(t){const s=t===this.pattern;return{isMatch:s,score:s?0:1,indices:[0,this.pattern.length-1]}}}class bt extends I{constructor(t){super(t)}static get type(){return"inverse-exact"}static get multiRegex(){return/^!"(.*)"$/}static get singleRegex(){return/^!(.*)$/}search(t){const n=t.indexOf(this.pattern)===-1;return{isMatch:n,score:n?0:1,indices:[0,t.length-1]}}}class Ot extends I{constructor(t){super(t)}static get type(){return"prefix-exact"}static get multiRegex(){return/^\^"(.*)"$/}static get singleRegex(){return/^\^(.*)$/}search(t){const s=t.startsWith(this.pattern);return{isMatch:s,score:s?0:1,indices:[0,this.pattern.length-1]}}}class $t extends I{constructor(t){super(t)}static get type(){return"inverse-prefix-exact"}static get multiRegex(){return/^!\^"(.*)"$/}static get singleRegex(){return/^!\^(.*)$/}search(t){const s=!t.startsWith(this.pattern);return{isMatch:s,score:s?0:1,indices:[0,t.length-1]}}}class kt extends I{constructor(t){super(t)}static get type(){return"suffix-exact"}static get multiRegex(){return/^"(.*)"\$$/}static get singleRegex(){return/^(.*)\$$/}search(t){const s=t.endsWith(this.pattern);return{isMatch:s,score:s?0:1,indices:[t.length-this.pattern.length,t.length-1]}}}class Nt extends I{constructor(t){super(t)}static get type(){return"inverse-suffix-exact"}static get multiRegex(){return/^!"(.*)"\$$/}static get singleRegex(){return/^!(.*)\$$/}search(t){const s=!t.endsWith(this.pattern);return{isMatch:s,score:s?0:1,indices:[0,t.length-1]}}}class ut extends I{constructor(t,{location:s=l.location,threshold:n=l.threshold,distance:r=l.distance,includeMatches:i=l.includeMatches,findAllMatches:u=l.findAllMatches,minMatchCharLength:c=l.minMatchCharLength,isCaseSensitive:o=l.isCaseSensitive,ignoreDiacritics:h=l.ignoreDiacritics,ignoreLocation:a=l.ignoreLocation}={}){super(t),this._bitapSearch=new it(t,{location:s,threshold:n,distance:r,includeMatches:i,findAllMatches:u,minMatchCharLength:c,isCaseSensitive:o,ignoreDiacritics:h,ignoreLocation:a})}static get type(){return"fuzzy"}static get multiRegex(){return/^"(.*)"$/}static get singleRegex(){return/^(.*)$/}search(t){return this._bitapSearch.searchIn(t)}}class ct extends I{constructor(t){super(t)}static get type(){return"include"}static get multiRegex(){return/^'"(.*)"$/}static get singleRegex(){return/^'(.*)$/}search(t){let s=0,n;const r=[],i=this.pattern.length;for(;(n=t.indexOf(this.pattern,s))>-1;)s=n+i,r.push([n,s-1]);const u=!!r.length;return{isMatch:u,score:u?0:1,indices:r}}}const K=[Rt,ct,Ot,$t,Nt,kt,bt,ut],Z=K.length,vt=/ +(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)/,Tt="|";function Pt(e,t={}){return e.split(Tt).map(s=>{let n=s.trim().split(vt).filter(i=>i&&!!i.trim()),r=[];for(let i=0,u=n.length;i<u;i+=1){const c=n[i];let o=!1,h=-1;for(;!o&&++h<Z;){const a=K[h];let f=a.isMultiMatch(c);f&&(r.push(new a(f,t)),o=!0)}if(!o)for(h=-1;++h<Z;){const a=K[h];let f=a.isSingleMatch(c);if(f){r.push(new a(f,t));break}}}return r})}const jt=new Set([ut.type,ct.type]);class Kt{constructor(t,{isCaseSensitive:s=l.isCaseSensitive,ignoreDiacritics:n=l.ignoreDiacritics,includeMatches:r=l.includeMatches,minMatchCharLength:i=l.minMatchCharLength,ignoreLocation:u=l.ignoreLocation,findAllMatches:c=l.findAllMatches,location:o=l.location,threshold:h=l.threshold,distance:a=l.distance}={}){this.query=null,this.options={isCaseSensitive:s,ignoreDiacritics:n,includeMatches:r,minMatchCharLength:i,findAllMatches:c,ignoreLocation:u,location:o,threshold:h,distance:a},t=s?t:t.toLowerCase(),t=n?k(t):t,this.pattern=t,this.query=Pt(this.pattern,this.options)}static condition(t,s){return s.useExtendedSearch}searchIn(t){const s=this.query;if(!s)return{isMatch:!1,score:1};const{includeMatches:n,isCaseSensitive:r,ignoreDiacritics:i}=this.options;t=r?t:t.toLowerCase(),t=i?k(t):t;let u=0,c=[],o=0;for(let h=0,a=s.length;h<a;h+=1){const f=s[h];c.length=0,u=0;for(let d=0,g=f.length;d<g;d+=1){const A=f[d],{isMatch:p,indices:F,score:y}=A.search(t);if(p){if(u+=1,o+=y,n){const D=A.constructor.type;jt.has(D)?c=[...c,...F]:c.push(F)}}else{o=0,u=0,c.length=0;break}}if(u){let d={isMatch:!0,score:o/u};return n&&(d.indices=c),d}}return{isMatch:!1,score:1}}}const W=[];function Wt(...e){W.push(...e)}function z(e,t){for(let s=0,n=W.length;s<n;s+=1){let r=W[s];if(r.condition(e,t))return new r(e,t)}return new it(e,t)}const N={AND:"$and",OR:"$or"},G={PATH:"$path",PATTERN:"$val"},H=e=>!!(e[N.AND]||e[N.OR]),zt=e=>!!e[G.PATH],Gt=e=>!m(e)&&et(e)&&!H(e),q=e=>({[N.AND]:Object.keys(e).map(t=>({[t]:e[t]}))});function ot(e,t,{auto:s=!0}={}){const n=r=>{let i=Object.keys(r);const u=zt(r);if(!u&&i.length>1&&!H(r))return n(q(r));if(Gt(r)){const o=u?r[G.PATH]:i[0],h=u?r[G.PATTERN]:r[o];if(!M(h))throw new Error(At(o));const a={keyId:j(o),pattern:h};return s&&(a.searcher=z(h,t)),a}let c={children:[],operator:i[0]};return i.forEach(o=>{const h=r[o];m(h)&&h.forEach(a=>{c.children.push(n(a))})}),c};return H(e)||(e=q(e)),n(e)}function Ht(e,{ignoreFieldNorm:t=l.ignoreFieldNorm}){e.forEach(s=>{let n=1;s.matches.forEach(({key:r,norm:i,score:u})=>{const c=r?r.weight:null;n*=Math.pow(u===0&&c?Number.EPSILON:u,(c||1)*(t?1:i))}),s.score=n})}function Yt(e,t){const s=e.matches;t.matches=[],E(s)&&s.forEach(n=>{if(!E(n.indices)||!n.indices.length)return;const{indices:r,value:i}=n;let u={indices:r,value:i};n.key&&(u.key=n.key.src),n.idx>-1&&(u.refIndex=n.idx),t.matches.push(u)})}function Vt(e,t){t.score=e.score}function Qt(e,t,{includeMatches:s=l.includeMatches,includeScore:n=l.includeScore}={}){const r=[];return s&&r.push(Yt),n&&r.push(Vt),e.map(i=>{const{idx:u}=i,c={item:t[u],refIndex:u};return r.length&&r.forEach(o=>{o(i,c)}),c})}class b{constructor(t,s={},n){this.options={...l,...s},this.options.useExtendedSearch,this._keyStore=new Ft(this.options.keys),this.setCollection(t,n)}setCollection(t,s){if(this._docs=t,s&&!(s instanceof Y))throw new Error(gt);this._myIndex=s||rt(this.options.keys,this._docs,{getFn:this.options.getFn,fieldNormWeight:this.options.fieldNormWeight})}add(t){E(t)&&(this._docs.push(t),this._myIndex.add(t))}remove(t=()=>!1){const s=[];for(let n=0,r=this._docs.length;n<r;n+=1){const i=this._docs[n];t(i,n)&&(this.removeAt(n),n-=1,r-=1,s.push(i))}return s}removeAt(t){this._docs.splice(t,1),this._myIndex.removeAt(t)}getIndex(){return this._myIndex}search(t,{limit:s=-1}={}){const{includeMatches:n,includeScore:r,shouldSort:i,sortFn:u,ignoreFieldNorm:c}=this.options;let o=M(t)?M(this._docs[0])?this._searchStringList(t):this._searchObjectList(t):this._searchLogical(t);return Ht(o,{ignoreFieldNorm:c}),i&&o.sort(u),tt(s)&&s>-1&&(o=o.slice(0,s)),Qt(o,this._docs,{includeMatches:n,includeScore:r})}_searchStringList(t){const s=z(t,this.options),{records:n}=this._myIndex,r=[];return n.forEach(({v:i,i:u,n:c})=>{if(!E(i))return;const{isMatch:o,score:h,indices:a}=s.searchIn(i);o&&r.push({item:i,idx:u,matches:[{score:h,value:i,norm:c,indices:a}]})}),r}_searchLogical(t){const s=ot(t,this.options),n=(c,o,h)=>{if(!c.children){const{keyId:f,searcher:d}=c,g=this._findMatches({key:this._keyStore.get(f),value:this._myIndex.getValueForItemAtKeyId(o,f),searcher:d});return g&&g.length?[{idx:h,item:o,matches:g}]:[]}const a=[];for(let f=0,d=c.children.length;f<d;f+=1){const g=c.children[f],A=n(g,o,h);if(A.length)a.push(...A);else if(c.operator===N.AND)return[]}return a},r=this._myIndex.records,i={},u=[];return r.forEach(({$:c,i:o})=>{if(E(c)){let h=n(s,c,o);h.length&&(i[o]||(i[o]={idx:o,item:c,matches:[]},u.push(i[o])),h.forEach(({matches:a})=>{i[o].matches.push(...a)}))}}),u}_searchObjectList(t){const s=z(t,this.options),{keys:n,records:r}=this._myIndex,i=[];return r.forEach(({$:u,i:c})=>{if(!E(u))return;let o=[];n.forEach((h,a)=>{o.push(...this._findMatches({key:h,value:u[a],searcher:s}))}),o.length&&i.push({idx:c,item:u,matches:o})}),i}_findMatches({key:t,value:s,searcher:n}){if(!E(s))return[];let r=[];if(m(s))s.forEach(({v:i,i:u,n:c})=>{if(!E(i))return;const{isMatch:o,score:h,indices:a}=n.searchIn(i);o&&r.push({score:h,key:t,value:i,idx:u,norm:c,indices:a})});else{const{v:i,n:u}=s,{isMatch:c,score:o,indices:h}=n.searchIn(i);c&&r.push({score:o,key:t,value:i,norm:u,indices:h})}return r}}b.version="7.1.0";b.createIndex=rt;b.parseIndex=It;b.config=l;b.parseQuery=ot;Wt(Kt);export{b as F};
