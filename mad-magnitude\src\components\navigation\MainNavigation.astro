---
import SearchBox from '../SearchBox.astro';

// 导航菜单配置
const navigationItems = [
  {
    label: '首页',
    href: '/',
    icon: '🏠'
  },
  {
    label: '动态资讯',
    href: '/news',
    icon: '📰'
  },
  {
    label: '研究日志',
    href: '/logs',
    icon: '📔'
  },
  {
    label: '研究所',
    icon: '🏛️',
    children: [
      {
        label: '经济研究所',
        href: '/economics',
        icon: '💰',
        description: '经济分析与市场洞察'
      },
      {
        label: '哲学研究所',
        href: '/philosophy',
        icon: '🤔',
        description: '思想探讨与理论研究'
      },
      {
        label: '互联网研究所',
        href: '/internet',
        icon: '🌐',
        description: '行业分析与趋势预测'
      },
      {
        label: 'AI研究所',
        href: '/ai',
        icon: '🤖',
        description: 'AI技术与应用研究'
      },
      {
        label: '未来研究所',
        href: '/future',
        icon: '🔮',
        description: '前瞻性思考与趋势判断'
      }
    ]
  },
  {
    label: '产品发布',
    href: '/products',
    icon: '🚀'
  },
  {
    label: '关于',
    href: '/about',
    icon: '👤'
  }
];

// 获取当前路径
const currentPath = Astro.url.pathname;
---

<header class="sticky top-0 z-50 bg-white shadow-sm border-b border-gray-200">
  <div class="container mx-auto px-4">
    <!-- 主导航栏 -->
    <div class="flex items-center justify-between py-4">
      <!-- Logo -->
      <a href="/" class="flex items-center space-x-3 hover:opacity-80 transition-opacity" aria-label="Pennfly Private Academy 首页">
        <img 
          src="/ppa-logo.PNG" 
          alt="Pennfly Private Academy 标志" 
          class="h-8 w-auto" 
          width="32" 
          height="32"
          loading="eager"
          decoding="async"
        />
        <div class="hidden sm:block">
          <div class="font-bold text-xl text-slate-800">Pennfly Private Academy</div>
          <div class="text-xs text-slate-600">私人研究院</div>
        </div>
      </a>
      
      <!-- 桌面端导航 -->
      <nav class="hidden lg:flex items-center space-x-1" role="navigation" aria-label="主导航">
        {navigationItems.map((item) => (
          <div class="relative group">
            {item.children ? (
              <div>
                <button 
                  class="flex items-center space-x-1 px-3 py-2 rounded-lg text-slate-700 hover:text-blue-600 hover:bg-slate-50 transition-colors"
                  aria-haspopup="true"
                  aria-expanded="false"
                  aria-label={`${item.label}菜单`}
                >
                  <span class="text-sm" aria-hidden="true">{item.icon}</span>
                  <span class="font-medium">{item.label}</span>
                  <svg class="w-4 h-4 transition-transform group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </button>
                
                <!-- 下拉内容 -->
                <div class="absolute top-full left-0 mt-1 w-64 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200" role="menu" aria-label={`${item.label}子菜单`}>
                  <div class="p-2">
                    {item.children.map((child) => (
                      <a 
                        href={child.href} 
                        class={`flex items-start space-x-3 p-3 rounded-lg hover:bg-slate-50 transition-colors ${
                          currentPath.startsWith(child.href) ? 'bg-blue-50 text-blue-600' : 'text-slate-700'
                        }`}
                        role="menuitem"
                        aria-current={currentPath.startsWith(child.href) ? 'page' : undefined}
                      >
                        <span class="text-lg" aria-hidden="true">{child.icon}</span>
                        <div>
                          <div class="font-medium">{child.label}</div>
                          <div class="text-xs text-slate-600 mt-1">{child.description}</div>
                        </div>
                      </a>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              <!-- 普通链接 -->
              <a 
                href={item.href} 
                class={`flex items-center space-x-1 px-3 py-2 rounded-lg font-medium transition-colors ${
                  currentPath === item.href 
                    ? 'text-blue-600 bg-blue-50' 
                    : 'text-slate-700 hover:text-blue-600 hover:bg-slate-50'
                }`}
                aria-current={currentPath === item.href ? 'page' : undefined}
              >
                <span class="text-sm" aria-hidden="true">{item.icon}</span>
                <span>{item.label}</span>
              </a>
            )}
          </div>
        ))}
      </nav>
      
      <!-- 右侧工具栏 -->
      <div class="flex items-center space-x-3">
        <!-- 搜索框 -->
        <div class="hidden md:block">
          <SearchBox />
        </div>
        
        <!-- 移动端搜索按钮 -->
        <button 
          class="md:hidden p-2 rounded-lg text-slate-600 hover:text-blue-600 hover:bg-slate-50 transition-colors" 
          id="mobile-search-button"
          aria-label="搜索"
          aria-expanded="false"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
        </button>
        
        <!-- 移动端菜单按钮 -->
        <button 
          class="lg:hidden p-2 rounded-lg text-slate-600 hover:text-blue-600 hover:bg-slate-50 transition-colors" 
          id="mobile-menu-button"
          aria-label="打开菜单"
          aria-expanded="false"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>
      </div>
    </div>
    
    <!-- 移动端搜索栏 -->
    <div id="mobile-search" class="hidden md:hidden pb-4">
      <SearchBox />
    </div>
  </div>
  
  <!-- 移动端菜单 -->
  <div id="mobile-menu" class="hidden lg:hidden bg-white border-t border-gray-200" role="navigation" aria-label="移动端导航菜单">
    <nav class="container mx-auto px-4 py-4">
      <div class="space-y-2">
        {navigationItems.map((item) => (
          <div>
            {item.children ? (
              <!-- 移动端下拉菜单 -->
              <div>
                <button 
                  class="w-full flex items-center justify-between p-3 rounded-lg text-slate-700 hover:bg-slate-50 transition-colors mobile-dropdown-button"
                  aria-expanded="false"
                  aria-label={`${item.label}菜单`}
                >
                  <div class="flex items-center space-x-2">
                    <span aria-hidden="true">{item.icon}</span>
                    <span class="font-medium">{item.label}</span>
                  </div>
                  <svg class="w-4 h-4 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </button>
                
                <div class="hidden ml-6 mt-2 space-y-1 mobile-dropdown-content" role="menu" aria-label={`${item.label}子菜单`}>
                  {item.children.map((child) => (
                    <a 
                      href={child.href} 
                      class={`flex items-center space-x-2 p-2 rounded-lg transition-colors ${
                        currentPath.startsWith(child.href) ? 'bg-blue-50 text-blue-600' : 'text-slate-600 hover:bg-slate-50'
                      }`}
                      role="menuitem"
                      aria-current={currentPath.startsWith(child.href) ? 'page' : undefined}
                    >
                      <span class="text-sm" aria-hidden="true">{child.icon}</span>
                      <span class="text-sm">{child.label}</span>
                    </a>
                  ))}
                </div>
              </div>
            ) : (
              <!-- 移动端普通链接 -->
              <a 
                href={item.href} 
                class={`flex items-center space-x-2 p-3 rounded-lg transition-colors ${
                  currentPath === item.href 
                    ? 'text-blue-600 bg-blue-50' 
                    : 'text-slate-700 hover:bg-slate-50'
                }`}
                aria-current={currentPath === item.href ? 'page' : undefined}
              >
                <span aria-hidden="true">{item.icon}</span>
                <span class="font-medium">{item.label}</span>
              </a>
            )}
          </div>
        ))}
      </div>
    </nav>
  </div>
</header>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    // 移动端菜单切换
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    
    mobileMenuButton?.addEventListener('click', () => {
      const isHidden = mobileMenu?.classList.contains('hidden');
      
      if (isHidden) {
        mobileMenu?.classList.remove('hidden');
        mobileMenuButton.setAttribute('aria-expanded', 'true');
        mobileMenuButton.setAttribute('aria-label', '关闭菜单');
        // 将焦点移到菜单的第一个链接
        const firstLink = mobileMenu?.querySelector('a, button') as HTMLElement;
        firstLink?.focus();
      } else {
        mobileMenu?.classList.add('hidden');
        mobileMenuButton.setAttribute('aria-expanded', 'false');
        mobileMenuButton.setAttribute('aria-label', '打开菜单');
      }
    });
    
    // 移动端搜索切换
    const mobileSearchButton = document.getElementById('mobile-search-button');
    const mobileSearch = document.getElementById('mobile-search');
    
    mobileSearchButton?.addEventListener('click', () => {
      const isHidden = mobileSearch?.classList.contains('hidden');
      
      if (isHidden) {
        mobileSearch?.classList.remove('hidden');
        mobileSearchButton.setAttribute('aria-expanded', 'true');
        mobileSearchButton.setAttribute('aria-label', '关闭搜索');
        // 将焦点移到搜索输入框
        const searchInput = mobileSearch?.querySelector('input') as HTMLInputElement;
        searchInput?.focus();
      } else {
        mobileSearch?.classList.add('hidden');
        mobileSearchButton.setAttribute('aria-expanded', 'false');
        mobileSearchButton.setAttribute('aria-label', '搜索');
      }
    });
    
    // 移动端下拉菜单
    document.querySelectorAll('.mobile-dropdown-button').forEach(button => {
      button.addEventListener('click', () => {
        const content = button.nextElementSibling;
        const icon = button.querySelector('svg');
        const isHidden = content?.classList.contains('hidden');
        
        if (isHidden) {
          content?.classList.remove('hidden');
          icon?.classList.add('rotate-180');
          button.setAttribute('aria-expanded', 'true');
        } else {
          content?.classList.add('hidden');
          icon?.classList.remove('rotate-180');
          button.setAttribute('aria-expanded', 'false');
        }
      });
    });
    
    // 键盘导航支持
    document.addEventListener('keydown', (e: KeyboardEvent) => {
      // ESC 键关闭所有菜单
      if (e.key === 'Escape') {
        mobileMenu?.classList.add('hidden');
        mobileSearch?.classList.add('hidden');
        mobileMenuButton?.setAttribute('aria-expanded', 'false');
        mobileSearchButton?.setAttribute('aria-expanded', 'false');
        mobileMenuButton?.setAttribute('aria-label', '打开菜单');
        mobileSearchButton?.setAttribute('aria-label', '搜索');
        
        // 关闭所有下拉菜单
        document.querySelectorAll('.mobile-dropdown-content').forEach(content => {
          content.classList.add('hidden');
        });
        document.querySelectorAll('.mobile-dropdown-button').forEach(button => {
          button.setAttribute('aria-expanded', 'false');
          const icon = button.querySelector('svg');
          icon?.classList.remove('rotate-180');
        });
      }
    });
    
    // 点击外部关闭菜单
    document.addEventListener('click', (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (!target?.closest('header')) {
        mobileMenu?.classList.add('hidden');
        mobileSearch?.classList.add('hidden');
        mobileMenuButton?.setAttribute('aria-expanded', 'false');
        mobileSearchButton?.setAttribute('aria-expanded', 'false');
        mobileMenuButton?.setAttribute('aria-label', '打开菜单');
        mobileSearchButton?.setAttribute('aria-label', '搜索');
      }
    });

    // 桌面端下拉菜单键盘导航
    document.querySelectorAll('[aria-haspopup="true"]').forEach((button) => {
      const dropdown = button.nextElementSibling;
      
      (button as HTMLElement).addEventListener('keydown', (e: KeyboardEvent) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          // 显示下拉菜单并聚焦第一个项目
          const firstMenuItem = dropdown?.querySelector('[role="menuitem"]') as HTMLElement;
          firstMenuItem?.focus();
        }
        
        if (e.key === 'ArrowDown') {
          e.preventDefault();
          const firstMenuItem = dropdown?.querySelector('[role="menuitem"]') as HTMLElement;
          firstMenuItem?.focus();
        }
      });
    });

    // 下拉菜单项键盘导航
    document.querySelectorAll('[role="menuitem"]').forEach((item, index, items) => {
      (item as HTMLElement).addEventListener('keydown', (e: KeyboardEvent) => {
        if (e.key === 'ArrowDown') {
          e.preventDefault();
          const nextItem = items[index + 1] || items[0];
          (nextItem as HTMLElement).focus();
        }
        
        if (e.key === 'ArrowUp') {
          e.preventDefault();
          const prevItem = items[index - 1] || items[items.length - 1];
          (prevItem as HTMLElement).focus();
        }
        
        if (e.key === 'Escape') {
          e.preventDefault();
          // 返回到触发按钮
          const parentButton = (item as HTMLElement).closest('[role="menu"]')?.previousElementSibling as HTMLElement;
          parentButton?.focus();
        }
      });
    });
  });
</script>

<style>
  /* 确保下拉菜单在悬停时保持可见 */
  .group:hover .group-hover\:opacity-100 {
    opacity: 1;
  }
  
  .group:hover .group-hover\:visible {
    visibility: visible;
  }
</style>