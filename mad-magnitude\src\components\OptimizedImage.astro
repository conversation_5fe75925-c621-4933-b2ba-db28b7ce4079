---
import { Image } from 'astro:assets';

export interface Props {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  class?: string;
  loading?: 'lazy' | 'eager';
  quality?: number;
  format?: 'webp' | 'avif' | 'png' | 'jpg';
  sizes?: string;
}

const {
  src,
  alt,
  width,
  height,
  class: className = '',
  loading = 'lazy',
  quality = 80,
  format = 'webp',
  sizes,
  ...rest
} = Astro.props;

// 如果是外部链接，直接使用 img 标签
const isExternal = src.startsWith('http') || src.startsWith('//');
---

{isExternal ? (
  <img
    src={src}
    alt={alt}
    width={width}
    height={height}
    class={className}
    loading={loading}
    {...rest}
  />
) : (
  <Image
    src={src}
    alt={alt}
    width={width}
    height={height}
    class={className}
    loading={loading}
    quality={quality}
    format={format}
    sizes={sizes}
    {...rest}
  />
)}