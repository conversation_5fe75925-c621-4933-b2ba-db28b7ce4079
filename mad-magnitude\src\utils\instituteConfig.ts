export interface InstituteConfig {
  id: string;
  name: string;
  nameEn: string;
  icon: string;
  description: string;
  color: string;
  gradientFrom: string;
  gradientTo: string;
  fields: Array<{
    name: string;
    description: string;
  }>;
}

export const instituteConfigs: Record<string, InstituteConfig> = {
  economics: {
    id: 'economics',
    name: '经济研究所',
    nameEn: 'Economics Research Institute',
    icon: '💰',
    description:
      '专注于经济分析、市场洞察和政策解读。从个人视角探讨经济现象，分析市场趋势，解读政策影响，为理解复杂的经济世界提供独特的观点和思考。',
    color: 'text-green-600',
    gradientFrom: 'from-green-500',
    gradientTo: 'to-emerald-600',
    fields: [
      { name: '市场分析', description: '股市、债市、商品市场的趋势分析' },
      { name: '政策解读', description: '货币政策、财政政策的影响分析' },
      { name: '经济理论', description: '经济学理论的现实应用和思考' },
      { name: '数据分析', description: '经济数据的深度挖掘和解读' },
    ],
  },
  philosophy: {
    id: 'philosophy',
    name: '哲学研究所',
    nameEn: 'Philosophy Research Institute',
    icon: '🤔',
    description:
      '探索人类思维的深层问题，从古典哲学到现代思辨，从形而上学到伦理学，致力于通过哲学思考来理解世界、人生和价值的本质。',
    color: 'text-amber-600',
    gradientFrom: 'from-amber-500',
    gradientTo: 'to-orange-600',
    fields: [
      { name: '伦理学', description: '道德哲学和价值判断的研究' },
      { name: '形而上学', description: '存在、现实和本质的哲学探讨' },
      { name: '认识论', description: '知识、真理和信念的哲学分析' },
      { name: '逻辑学', description: '推理和论证的形式化研究' },
      { name: '美学', description: '艺术、美和审美体验的哲学思考' },
    ],
  },
  internet: {
    id: 'internet',
    name: '互联网研究所',
    nameEn: 'Internet Research Institute',
    icon: '🌐',
    description:
      '深入研究互联网行业的发展趋势、商业模式和技术创新。关注数字化转型、平台经济、网络文化等现象，探索互联网对社会的深远影响。',
    color: 'text-blue-600',
    gradientFrom: 'from-blue-500',
    gradientTo: 'to-cyan-600',
    fields: [
      { name: '平台经济', description: '互联网平台的商业模式和生态研究' },
      { name: '数字化转型', description: '传统行业的数字化升级路径' },
      { name: '网络文化', description: '互联网时代的文化现象和社会影响' },
      { name: '技术创新', description: '互联网技术的发展趋势和应用' },
      { name: '用户行为', description: '网络用户的行为模式和心理分析' },
    ],
  },
  ai: {
    id: 'ai',
    name: '人工智能研究所',
    nameEn: 'Artificial Intelligence Research Institute',
    icon: '🤖',
    description:
      '专注于人工智能技术研究、应用思考和伦理探讨。从技术原理到实际应用，从算法创新到社会影响，全方位探索AI技术的发展轨迹和未来可能。',
    color: 'text-purple-600',
    gradientFrom: 'from-purple-500',
    gradientTo: 'to-indigo-600',
    fields: [
      { name: '机器学习', description: '深度学习、强化学习等算法研究' },
      { name: '自然语言处理', description: '大语言模型、对话系统等技术' },
      { name: '计算机视觉', description: '图像识别、生成模型等应用' },
      { name: 'AI伦理', description: '人工智能的伦理问题和治理' },
      { name: '应用场景', description: 'AI在各行业的实际应用案例' },
      { name: '通用人工智能', description: 'AGI的发展路径和可能性' },
    ],
  },
  future: {
    id: 'future',
    name: '未来研究所',
    nameEn: 'Future Studies Institute',
    icon: '🔮',
    description:
      '致力于未来趋势预测和情景分析，探索科技、社会、环境等各领域的发展可能性。通过系统性思考和跨学科研究，为理解和塑造未来提供洞察。',
    color: 'text-pink-600',
    gradientFrom: 'from-pink-500',
    gradientTo: 'to-rose-600',
    fields: [
      { name: '科技趋势', description: '新兴技术的发展预测和影响分析' },
      { name: '社会变迁', description: '社会结构和文化的未来演变' },
      { name: '环境未来', description: '气候变化和可持续发展的前景' },
      { name: '工作未来', description: '就业形态和工作方式的变化趋势' },
      { name: '生活方式', description: '未来人类生活方式的可能形态' },
      { name: '风险评估', description: '未来可能面临的挑战和风险' },
    ],
  },
};

export function getInstituteConfig(instituteId: string): InstituteConfig | undefined {
  return instituteConfigs[instituteId];
}

export function getAllInstituteConfigs(): InstituteConfig[] {
  return Object.values(instituteConfigs);
}
