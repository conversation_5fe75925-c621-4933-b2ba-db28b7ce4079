class u{mode;contentId;currentView="edit";isDirty=!1;autoSaveTimer;constructor(t,e){this.mode=t,this.contentId=e,this.bindEvents(),this.initializeEditor()}bindEvents(){document.getElementById("edit-tab")?.addEventListener("click",()=>this.switchView("edit")),document.getElementById("preview-tab")?.addEventListener("click",()=>this.switchView("preview")),document.getElementById("split-tab")?.addEventListener("click",()=>this.switchView("split")),document.querySelectorAll(".toolbar-btn").forEach(s=>{s.addEventListener("click",i=>{const o=i.target.dataset.action;o&&this.executeToolbarAction(o)})}),document.getElementById("save-draft")?.addEventListener("click",()=>this.saveDraft()),document.getElementById("publish-content")?.addEventListener("click",()=>this.publishContent()),document.getElementById("close-dialog")?.addEventListener("click",()=>this.closeDialog()),document.getElementById("confirm-save")?.addEventListener("click",()=>this.confirmSave()),document.getElementById("cancel-save")?.addEventListener("click",()=>this.closeDialog()),document.getElementById("content-editor")?.addEventListener("input",()=>{this.markDirty(),this.updateWordCount(),this.updatePreview(),this.scheduleAutoSave()}),document.getElementById("content-title")?.addEventListener("input",()=>{this.generateSlug(),this.markDirty()}),document.querySelectorAll(".form-input, .form-select, .form-textarea, .checkbox-input").forEach(s=>{s.addEventListener("change",()=>this.markDirty())}),window.addEventListener("beforeunload",s=>{this.isDirty&&(s.preventDefault(),s.returnValue="您有未保存的更改，确定要离开吗？")})}initializeEditor(){this.updateWordCount(),this.updatePreview(),this.switchView("edit")}switchView(t){this.currentView=t,document.querySelectorAll(".tab-button").forEach(s=>s.classList.remove("active")),document.getElementById(`${t}-tab`)?.classList.add("active");const e=document.getElementById("edit-pane"),n=document.getElementById("preview-pane");if(!(!e||!n))switch(e.classList.remove("active","split"),n.classList.remove("active","split"),t){case"edit":e.classList.add("active");break;case"preview":n.classList.add("active"),this.updatePreview();break;case"split":e.classList.add("split"),n.classList.add("split"),this.updatePreview();break}}executeToolbarAction(t){const e=document.getElementById("content-editor");if(!e)return;const n=e.selectionStart,s=e.selectionEnd,i=e.value.substring(n,s);let o="";switch(t){case"bold":o=`**${i||"粗体文本"}**`;break;case"italic":o=`*${i||"斜体文本"}*`;break;case"heading":o=`## ${i||"标题"}`;break;case"link":o=`[${i||"链接文本"}](URL)`;break;case"image":o=`![${i||"图片描述"}](图片URL)`;break;case"code":o=i.includes(`
`)?`\`\`\`
${i||"代码"}
\`\`\``:`\`${i||"代码"}\``;break;case"list":o=`- ${i||"列表项"}`;break;case"quote":o=`> ${i||"引用内容"}`;break}e.value=e.value.substring(0,n)+o+e.value.substring(s),e.focus();const a=n+o.length;e.setSelectionRange(a,a),this.markDirty(),this.updateWordCount(),this.updatePreview()}generateSlug(){const t=document.getElementById("content-title"),e=document.getElementById("content-slug");if(!t||!e||e.value)return;const n=t.value.toLowerCase().replace(/[^\w\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim();e.value=n}updateWordCount(){const t=document.getElementById("content-editor"),e=document.getElementById("word-count"),n=document.getElementById("line-count");if(!t||!e||!n)return;const s=t.value,i=s.trim()?s.trim().split(/\s+/).length:0,o=s.split(`
`).length;e.textContent=`${i} 字`,n.textContent=`${o} 行`}async updatePreview(){if(this.currentView==="edit")return;const t=document.getElementById("content-editor"),e=document.getElementById("preview-content");if(!t||!e)return;const n=t.value,s=this.parseMarkdown(n);e.innerHTML=s}parseMarkdown(t){return t.replace(/^### (.*$)/gim,"<h3>$1</h3>").replace(/^## (.*$)/gim,"<h2>$1</h2>").replace(/^# (.*$)/gim,"<h1>$1</h1>").replace(/\*\*(.*)\*\*/gim,"<strong>$1</strong>").replace(/\*(.*)\*/gim,"<em>$1</em>").replace(/\[([^\]]+)\]\(([^)]+)\)/gim,'<a href="$2">$1</a>').replace(/`([^`]+)`/gim,"<code>$1</code>").replace(/\n/gim,"<br>")||"<p>开始编写内容...</p>"}markDirty(){this.isDirty=!0;const t=document.getElementById("save-status");t&&(t.textContent="未保存",t.style.color="#ef4444")}markClean(){this.isDirty=!1;const t=document.getElementById("save-status");t&&(t.textContent="已保存",t.style.color="#10b981")}scheduleAutoSave(){this.autoSaveTimer&&clearTimeout(this.autoSaveTimer),this.autoSaveTimer=window.setTimeout(()=>{this.saveDraft(!0)},3e4)}async saveDraft(t=!1){const e=this.collectFormData();e.draft=!0;try{await this.saveContent(e),t||alert("草稿保存成功！")}catch(n){t||alert("保存失败："+n)}}publishContent(){const t=this.collectFormData();this.showSaveDialog(t,!1)}showSaveDialog(t,e){const n=document.getElementById("save-dialog"),s=document.getElementById("dialog-title"),i=document.getElementById("dialog-collection"),o=document.getElementById("dialog-status");!n||!s||!i||!o||(s.textContent=t.title||"未命名",i.textContent=this.getCollectionDisplayName(t.collection),o.textContent=e?"草稿":"发布",n.style.display="flex",n._pendingData={...t,draft:e})}closeDialog(){const t=document.getElementById("save-dialog");t&&(t.style.display="none")}async confirmSave(){const e=document.getElementById("save-dialog")?._pendingData;if(e)try{await this.saveContent(e),this.closeDialog(),alert(e.draft?"草稿保存成功！":"内容发布成功！"),e.draft||(window.location.href="/admin/content")}catch(n){alert("保存失败："+n)}}collectFormData(){const t=s=>document.getElementById(s)?.value||"",e=s=>document.getElementById(s)?.checked||!1,n=t("content-tags").split(",").map(s=>s.trim()).filter(s=>s.length>0);return{collection:t("content-collection"),slug:t("content-slug"),title:t("content-title"),description:t("content-description"),tags:n,author:t("content-author"),draft:e("content-draft"),featured:e("content-featured"),content:t("content-editor")}}async saveContent(t){const e=this.mode==="create"?"/api/content/create":`/api/content/${encodeURIComponent(this.contentId)}`,n=this.mode==="create"?"POST":"PUT",s=this.mode==="create"?t:{frontmatter:t,content:t.content},i=await fetch(e,{method:n,headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(!i.ok){const a=await i.json();throw new Error(a.error||"保存失败")}const o=await i.json();return this.mode==="create"&&(this.mode="edit",this.contentId=o.id,window.history.replaceState({},"",`/admin/content/edit/${encodeURIComponent(o.id)}`)),this.markClean(),o}getCollectionDisplayName(t){return{news:"动态资讯",logs:"研究日志",research:"研究报告",reflections:"反思记录",economics:"经济研究",philosophy:"哲学研究",internet:"互联网研究",ai:"AI研究",future:"未来研究",products:"产品发布"}[t]||t}}document.addEventListener("DOMContentLoaded",()=>{const r=window.location.pathname.split("/"),t=r.includes("edit"),e=t?decodeURIComponent(r[r.length-1]):void 0;new u(t?"edit":"create",e)});class m{currentPage=1;currentFilters={};totalPages=1;constructor(){this.bindEvents(),this.loadContent()}bindEvents(){const t=document.getElementById("apply-filters"),e=document.getElementById("clear-filters"),n=document.getElementById("refresh-list"),s=document.getElementById("create-content");t?.addEventListener("click",()=>this.applyFilters()),e?.addEventListener("click",()=>this.clearFilters()),n?.addEventListener("click",()=>this.loadContent()),s?.addEventListener("click",()=>this.createContent()),document.getElementById("search-input")?.addEventListener("keypress",l=>{l.key==="Enter"&&this.applyFilters()});const o=document.getElementById("prev-page"),a=document.getElementById("next-page");o?.addEventListener("click",()=>this.goToPage(this.currentPage-1)),a?.addEventListener("click",()=>this.goToPage(this.currentPage+1))}async loadContent(t=1){try{const e=new URLSearchParams({page:t.toString(),limit:"20",...this.currentFilters}),n=await fetch(`/api/content/list?${e}`),s=await n.json();n.ok?(this.renderContent(s.content),this.renderPagination(s.pagination),this.updateContentCount(s.pagination.total)):this.showError("加载内容失败: "+s.error)}catch(e){this.showError("网络错误: "+e)}}renderContent(t){const e=document.getElementById("content-table-body");if(e){if(t.length===0){e.innerHTML=`
          <tr>
            <td colspan="6" class="loading-row">
              <span>没有找到内容</span>
            </td>
          </tr>
        `;return}e.innerHTML=t.map(n=>`
        <tr data-id="${n.id}">
          <td class="col-title">
            <div>
              <div class="font-medium text-gray-900">${n.title}</div>
              ${n.description?`<div class="text-sm text-gray-500 mt-1">${n.description}</div>`:""}
            </div>
          </td>
          <td class="col-collection">
            <span class="collection-badge">
              ${this.getCollectionIcon(n.collection)}
              ${this.getCollectionDisplayName(n.collection)}
            </span>
          </td>
          <td class="col-status">
            ${this.renderStatusBadge(n)}
          </td>
          <td class="col-author">${n.author}</td>
          <td class="col-date">
            <div class="text-sm">
              ${this.formatDate(n.updateDate||n.publishDate)}
            </div>
          </td>
          <td class="col-actions">
            <div class="flex gap-1">
              <button onclick="contentList.editContent('${n.id}')" class="btn btn--secondary btn--sm" title="编辑">
                ✏️
              </button>
              <button onclick="contentList.deleteContent('${n.id}')" class="btn btn--danger btn--sm" title="删除">
                🗑️
              </button>
            </div>
          </td>
        </tr>
      `).join("")}}renderStatusBadge(t){return t.featured?'<span class="status-badge status-badge--featured">⭐ 特色</span>':t.draft?'<span class="status-badge status-badge--draft">📝 草稿</span>':'<span class="status-badge status-badge--published">✅ 已发布</span>'}renderPagination(t){const e=document.getElementById("pagination-container"),n=document.getElementById("pagination-info-text"),s=document.getElementById("page-numbers"),i=document.getElementById("prev-page"),o=document.getElementById("next-page");if(!e||!n||!s||!i||!o)return;this.currentPage=t.page,this.totalPages=t.totalPages;const a=(t.page-1)*t.limit+1,l=Math.min(t.page*t.limit,t.total);n.textContent=`显示 ${a}-${l} 条，共 ${t.total} 条`,i.disabled=!t.hasPrev,o.disabled=!t.hasNext;const d=this.generatePageNumbers(t.page,t.totalPages);s.innerHTML=d.map(c=>c==="..."?'<span class="px-2">...</span>':`
          <button 
            class="page-number ${c===t.page?"active":""}"
            onclick="contentList.goToPage(${c})"
          >
            ${c}
          </button>
        `).join(""),e.style.display=t.totalPages>1?"flex":"none"}generatePageNumbers(t,e){const n=[];if(e<=7)for(let s=1;s<=e;s++)n.push(s);else{n.push(1),t>4&&n.push("...");const s=Math.max(2,t-1),i=Math.min(e-1,t+1);for(let o=s;o<=i;o++)n.push(o);t<e-3&&n.push("..."),n.push(e)}return n}updateContentCount(t){const e=document.getElementById("content-count");e&&(e.textContent=`共 ${t} 条内容`)}applyFilters(){const t=document.getElementById("collection-filter"),e=document.getElementById("status-filter"),n=document.getElementById("author-filter"),s=document.getElementById("search-input");this.currentFilters={},t?.value&&(this.currentFilters.collection=t.value),e?.value&&(e.value==="published"?this.currentFilters.draft="false":e.value==="draft"?this.currentFilters.draft="true":e.value==="featured"&&(this.currentFilters.featured="true")),n?.value&&(this.currentFilters.author=n.value),s?.value.trim()&&(this.currentFilters.search=s.value.trim()),this.currentPage=1,this.loadContent(1)}clearFilters(){const t=document.getElementById("collection-filter"),e=document.getElementById("status-filter"),n=document.getElementById("author-filter"),s=document.getElementById("search-input");t&&(t.value=""),e&&(e.value=""),n&&(n.value=""),s&&(s.value=""),this.currentFilters={},this.currentPage=1,this.loadContent(1)}goToPage(t){t<1||t>this.totalPages||(this.currentPage=t,this.loadContent(t))}editContent(t){window.location.href=`/admin/content/edit/${encodeURIComponent(t)}`}async deleteContent(t){if(confirm("确定要删除这个内容吗？此操作不可恢复。"))try{const e=await fetch(`/api/content/${encodeURIComponent(t)}`,{method:"DELETE"});if(e.ok)this.loadContent(this.currentPage);else{const n=await e.json();this.showError("删除失败: "+n.error)}}catch(e){this.showError("网络错误: "+e)}}createContent(){window.location.href="/admin/content/create"}showError(t){alert(t)}formatDate(t){return new Date(t).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}getCollectionDisplayName(t){return{news:"动态资讯",logs:"研究日志",research:"研究报告",reflections:"反思记录",economics:"经济研究",philosophy:"哲学研究",internet:"互联网研究",ai:"AI研究",future:"未来研究",products:"产品发布"}[t]||t}getCollectionIcon(t){return{news:"📰",logs:"📔",research:"📊",reflections:"💭",economics:"💰",philosophy:"🤔",internet:"🌐",ai:"🤖",future:"🔮",products:"🛠️"}[t]||"📄"}}document.addEventListener("DOMContentLoaded",()=>{new m});
