---
export const prerender = false;

import { getCollection, getEntry } from 'astro:content';
import Layout from '../../layouts/Layout.astro';
import { formatDate } from '../../utils/dateUtils';

// 在服务器端渲染模式下，直接从 URL 参数获取内容
const { slug } = Astro.params;

if (!slug) {
  return Astro.redirect('/news');
}

// 获取新闻内容
const entry = await getEntry('news', slug);

if (!entry) {
  return Astro.redirect('/404');
}

const { Content } = await entry.render();

// 获取相关动态（同类型或相关研究所）
const allNews = await getCollection('news', ({ data }) => !data.draft);
const relatedNews = allNews
  .filter(
    news =>
      news.slug !== entry.slug &&
      (news.data.type === entry.data.type ||
        (entry.data.relatedInstitute &&
          news.data.relatedInstitute &&
          entry.data.relatedInstitute.some(institute =>
            news.data.relatedInstitute?.includes(institute)
          )))
  )
  .sort((a, b) => new Date(b.data.publishDate).getTime() - new Date(a.data.publishDate).getTime())
  .slice(0, 3);

// 面包屑导航
const breadcrumbs = [
  { label: '首页', href: '/' },
  { label: '动态资讯', href: '/news' },
  { label: entry.data.title.zh, href: `/news/${entry.slug}` },
];

// 类型映射
const typeConfig = {
  research: { icon: '🔬', label: '研究动态', color: 'blue' },
  announcement: { icon: '📢', label: '重要公告', color: 'purple' },
  reflection: { icon: '💭', label: '个人思考', color: 'orange' },
  milestone: { icon: '🎯', label: '里程碑', color: 'green' },
};

const currentType = typeConfig[entry.data.type] || typeConfig.research;
---

<Layout
  title={`${entry.data.title.zh} - 动态资讯 - Pennfly Private Academy`}
  description={entry.data.description.zh}
>
  <article class="min-h-screen bg-gray-50">
    <!-- 面包屑导航 -->
    <nav class="border-b border-gray-200 bg-white py-4">
      <div class="container mx-auto px-6">
        <ol class="flex items-center space-x-2 text-sm text-gray-600">
          {
            breadcrumbs.map((crumb, index) => (
              <li class="flex items-center">
                {index > 0 && <span class="mr-2 text-gray-400">/</span>}
                {index === breadcrumbs.length - 1 ? (
                  <span class="font-medium text-gray-800">{crumb.label}</span>
                ) : (
                  <a href={crumb.href} class="transition-colors hover:text-blue-600">
                    {crumb.label}
                  </a>
                )}
              </li>
            ))
          }
        </ol>
      </div>
    </nav>

    <!-- 文章头部 -->
    <header class="bg-white py-12">
      <div class="container mx-auto px-6">
        <div class="mx-auto max-w-4xl">
          <!-- 类型标签 -->
          <div class="mb-6 flex items-center space-x-4">
            <span
              class={`inline-flex items-center space-x-2 rounded-lg px-3 py-1 text-sm font-medium bg-${currentType.color}-100 text-${currentType.color}-800`}
            >
              <span>{currentType.icon}</span>
              <span>{currentType.label}</span>
            </span>

            {
              entry.data.featured && (
                <span class="inline-flex items-center space-x-1 rounded-lg bg-red-100 px-3 py-1 text-sm font-medium text-red-800">
                  <span>📌</span>
                  <span>置顶</span>
                </span>
              )
            }

            {
              entry.data.relatedInstitute && entry.data.relatedInstitute.length > 0 && (
                <div class="flex items-center space-x-2">
                  <span class="text-sm text-gray-600">相关研究所:</span>
                  {entry.data.relatedInstitute.map(institute => (
                    <a
                      href={`/${institute}`}
                      class="inline-flex items-center space-x-1 rounded bg-gray-100 px-2 py-1 text-xs text-gray-700 transition-colors hover:bg-gray-200"
                    >
                      <span>
                        {institute === 'economics'
                          ? '💰'
                          : institute === 'philosophy'
                            ? '🤔'
                            : institute === 'internet'
                              ? '🌐'
                              : institute === 'ai'
                                ? '🤖'
                                : '🔮'}
                      </span>
                      <span>
                        {institute === 'economics'
                          ? '经济研究所'
                          : institute === 'philosophy'
                            ? '哲学研究所'
                            : institute === 'internet'
                              ? '互联网研究所'
                              : institute === 'ai'
                                ? 'AI研究所'
                                : '未来研究所'}
                      </span>
                    </a>
                  ))}
                </div>
              )
            }
          </div>

          <!-- 标题 -->
          <h1 class="mb-6 text-4xl leading-tight font-bold text-gray-800">
            {entry.data.title.zh}
          </h1>

          <!-- 摘要 -->
          {
            entry.data.summary && (
              <p class="mb-6 text-xl leading-relaxed text-gray-600">{entry.data.summary}</p>
            )
          }

          <!-- 元信息 -->
          <div
            class="flex flex-wrap items-center gap-6 border-t border-gray-200 pt-6 text-sm text-gray-600"
          >
            <div class="flex items-center space-x-2">
              <span>📅</span>
              <span>发布时间: {formatDate(entry.data.publishDate)}</span>
            </div>

            {
              entry.data.updateDate && (
                <div class="flex items-center space-x-2">
                  <span>🔄</span>
                  <span>更新时间: {formatDate(entry.data.updateDate)}</span>
                </div>
              )
            }

            {
              entry.data.author && (
                <div class="flex items-center space-x-2">
                  <span>👤</span>
                  <span>作者: {entry.data.author}</span>
                </div>
              )
            }

            {
              entry.data.readingTime && (
                <div class="flex items-center space-x-2">
                  <span>⏱️</span>
                  <span>阅读时间: {entry.data.readingTime} 分钟</span>
                </div>
              )
            }
          </div>
        </div>
      </div>
    </header>

    <!-- 文章内容 -->
    <main class="py-12">
      <div class="container mx-auto px-6">
        <div class="mx-auto max-w-4xl">
          <div class="prose prose-lg prose-gray max-w-none">
            <Content />
          </div>
        </div>
      </div>
    </main>

    <!-- 标签 -->
    {
      entry.data.tags && entry.data.tags.length > 0 && (
        <section class="bg-white py-8">
          <div class="container mx-auto px-6">
            <div class="mx-auto max-w-4xl">
              <h3 class="mb-4 text-lg font-semibold text-gray-800">相关标签</h3>
              <div class="flex flex-wrap gap-2">
                {entry.data.tags.map(tag => (
                  <span class="cursor-pointer rounded-full bg-gray-100 px-4 py-2 text-sm text-gray-700 transition-colors hover:bg-gray-200">
                    #{tag}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </section>
      )
    }

    <!-- 相关动态 -->
    {
      relatedNews.length > 0 && (
        <section class="bg-gray-50 py-12">
          <div class="container mx-auto px-6">
            <div class="mx-auto max-w-4xl">
              <h3 class="mb-8 text-2xl font-bold text-gray-800">相关动态</h3>
              <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {relatedNews.map(news => (
                  <article class="rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition-shadow hover:shadow-md">
                    <div class="mb-3 flex items-center">
                      <span class="mr-2 text-lg">
                        {news.data.type === 'research'
                          ? '🔬'
                          : news.data.type === 'announcement'
                            ? '📢'
                            : news.data.type === 'milestone'
                              ? '🎯'
                              : '💭'}
                      </span>
                      <span class="text-xs text-gray-600">{formatDate(news.data.publishDate)}</span>
                    </div>

                    <h4 class="mb-2 line-clamp-2 font-semibold text-gray-800">
                      <a href={`/news/${news.slug}`} class="transition-colors hover:text-blue-600">
                        {news.data.title.zh}
                      </a>
                    </h4>

                    <p class="line-clamp-3 text-sm text-gray-600">{news.data.description.zh}</p>
                  </article>
                ))}
              </div>
            </div>
          </div>
        </section>
      )
    }

    <!-- 导航按钮 -->
    <section class="border-t border-gray-200 bg-white py-8">
      <div class="container mx-auto px-6">
        <div class="mx-auto flex max-w-4xl items-center justify-between">
          <a
            href="/news"
            class="inline-flex items-center space-x-2 rounded-lg bg-gray-600 px-6 py-3 text-white transition-colors hover:bg-gray-700"
          >
            <span>←</span>
            <span>返回动态列表</span>
          </a>

          <div class="flex items-center space-x-4">
            <button
              onclick="window.scrollTo({top: 0, behavior: 'smooth'})"
              class="rounded-lg bg-blue-600 px-6 py-3 text-white transition-colors hover:bg-blue-700"
            >
              回到顶部 ↑
            </button>
          </div>
        </div>
      </div>
    </section>
  </article>
</Layout>

<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 自定义 prose 样式 */
  .prose {
    color: #374151;
    line-height: 1.75;
  }

  .prose h1,
  .prose h2,
  .prose h3,
  .prose h4,
  .prose h5,
  .prose h6 {
    color: #111827;
    font-weight: 600;
    margin-top: 2rem;
    margin-bottom: 1rem;
  }

  .prose h1 {
    font-size: 2.25rem;
  }
  .prose h2 {
    font-size: 1.875rem;
  }
  .prose h3 {
    font-size: 1.5rem;
  }
  .prose h4 {
    font-size: 1.25rem;
  }

  .prose p {
    margin-bottom: 1.5rem;
  }

  .prose a {
    color: #2563eb;
    text-decoration: none;
  }

  .prose a:hover {
    color: #1d4ed8;
    text-decoration: underline;
  }

  .prose blockquote {
    border-left: 4px solid #e5e7eb;
    padding-left: 1rem;
    margin: 1.5rem 0;
    font-style: italic;
    color: #6b7280;
  }

  .prose ul,
  .prose ol {
    margin: 1.5rem 0;
    padding-left: 1.5rem;
  }

  .prose li {
    margin: 0.5rem 0;
  }

  .prose code {
    background-color: #f3f4f6;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
  }

  .prose pre {
    background-color: #1f2937;
    color: #f9fafb;
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 1.5rem 0;
  }

  .prose pre code {
    background-color: transparent;
    padding: 0;
  }
</style>
