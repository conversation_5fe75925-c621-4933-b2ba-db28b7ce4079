import { d as contentManager } from "../../../assets/utils.bIDOeBqD.js";
import { i } from "../../../assets/vendor-astro.Dc6apy9i.js";
const prerender = false;
const GET = async ({ params }) => {
  try {
    const { id } = params;
    if (!id) {
      return new Response(
        JSON.stringify({
          error: "Content ID is required",
          code: "MISSING_ID"
        }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
    const decodedId = decodeURIComponent(id);
    const content = await contentManager.getContentById(decodedId);
    if (!content) {
      return new Response(
        JSON.stringify({
          error: "Content not found",
          code: "NOT_FOUND"
        }),
        {
          status: 404,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
    const response = {
      id: content.id,
      collection: content.collection,
      slug: content.slug,
      title: content.title,
      description: content.description,
      publishDate: content.publishDate.toISOString(),
      updateDate: content.updateDate?.toISOString(),
      draft: content.draft,
      featured: content.featured,
      tags: content.tags,
      author: content.author,
      content: content.content
    };
    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        "Cache-Control": "public, max-age=300"
      }
    });
  } catch (error) {
    console.error("获取内容 API 错误:", error);
    return new Response(
      JSON.stringify({
        error: "Internal server error",
        code: "INTERNAL_ERROR"
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" }
      }
    );
  }
};
const PUT = async ({ params, request }) => {
  try {
    const { id } = params;
    if (!id) {
      return new Response(
        JSON.stringify({
          error: "Content ID is required",
          code: "MISSING_ID"
        }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
    const body = await request.json();
    const { frontmatter, content } = body;
    if (!frontmatter || content === void 0) {
      return new Response(
        JSON.stringify({
          error: "Frontmatter and content are required",
          code: "MISSING_DATA"
        }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
    const decodedId = decodeURIComponent(id);
    const updatedContent = await contentManager.updateContent(decodedId, frontmatter, content);
    if (!updatedContent) {
      return new Response(
        JSON.stringify({
          error: "Content not found",
          code: "NOT_FOUND"
        }),
        {
          status: 404,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
    const response = {
      id: updatedContent.id,
      collection: updatedContent.collection,
      slug: updatedContent.slug,
      title: updatedContent.title,
      description: updatedContent.description,
      publishDate: updatedContent.publishDate.toISOString(),
      updateDate: updatedContent.updateDate?.toISOString(),
      draft: updatedContent.draft,
      featured: updatedContent.featured,
      tags: updatedContent.tags,
      author: updatedContent.author,
      content: updatedContent.content
    };
    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { "Content-Type": "application/json" }
    });
  } catch (error) {
    console.error("更新内容 API 错误:", error);
    return new Response(
      JSON.stringify({
        error: "Internal server error",
        code: "INTERNAL_ERROR"
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" }
      }
    );
  }
};
const DELETE = async ({ params }) => {
  try {
    const { id } = params;
    if (!id) {
      return new Response(
        JSON.stringify({
          error: "Content ID is required",
          code: "MISSING_ID"
        }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
    const decodedId = decodeURIComponent(id);
    const success = await contentManager.deleteContent(decodedId);
    if (!success) {
      return new Response(
        JSON.stringify({
          error: "Content not found or could not be deleted",
          code: "DELETE_FAILED"
        }),
        {
          status: 404,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
    return new Response(
      JSON.stringify({
        success: true,
        message: "Content deleted successfully"
      }),
      {
        status: 200,
        headers: { "Content-Type": "application/json" }
      }
    );
  } catch (error) {
    console.error("删除内容 API 错误:", error);
    return new Response(
      JSON.stringify({
        error: "Internal server error",
        code: "INTERNAL_ERROR"
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" }
      }
    );
  }
};
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({ __proto__: null, DELETE, GET, PUT, prerender }, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  i as renderers
};
