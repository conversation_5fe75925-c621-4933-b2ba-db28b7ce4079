{"enabled": true, "name": "组件优化器", "description": "在保存 Astro 组件时检查性能和可访问性", "version": "1", "when": {"type": "userTriggered", "patterns": ["mad-magnitude/src/components/**/*.astro"]}, "then": {"type": "askAgent", "prompt": "请检查这个 Astro 组件的质量和性能：\n\n1. **TypeScript 类型检查**：\n   - Props 接口是否完整\n   - 类型注解是否准确\n   - 是否避免使用 any 类型\n\n2. **性能优化**：\n   - 是否使用了适当的图片组件\n   - 是否有不必要的重复渲染\n   - 是否正确使用了懒加载\n\n3. **可访问性检查**：\n   - 是否有适当的 ARIA 标签\n   - 图片是否有 alt 属性\n   - 是否支持键盘导航\n   - 颜色对比度是否足够\n\n4. **代码质量**：\n   - 组件结构是否清晰\n   - 是否遵循命名规范\n   - 是否有适当的注释\n\n5. **响应式设计**：\n   - 是否适配移动设备\n   - 是否使用了合适的 Tailwind 类\n\n请提供具体的改进建议，特别关注性能和可访问性问题。"}}