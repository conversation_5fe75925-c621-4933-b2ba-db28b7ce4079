/**
 * 研究日志管理工具
 */

import { getCollection } from 'astro:content';

// 心情类型配置
export const MOOD_CONFIG = {
  thoughtful: {
    label: '深思',
    icon: '🤔',
    color: 'blue',
    description: '深入思考和反思',
  },
  critical: {
    label: '批判',
    icon: '🧐',
    color: 'red',
    description: '批判性思维和分析',
  },
  optimistic: {
    label: '乐观',
    icon: '😊',
    color: 'green',
    description: '积极乐观的心态',
  },
  analytical: {
    label: '分析',
    icon: '🔍',
    color: 'purple',
    description: '理性分析和逻辑思考',
  },
} as const;

// 研究所配置
export const INSTITUTE_CONFIG = {
  economics: {
    label: '经济研究所',
    icon: '💰',
    color: 'yellow',
    path: '/economics',
  },
  philosophy: {
    label: '哲学研究所',
    icon: '🤔',
    color: 'indigo',
    path: '/philosophy',
  },
  internet: {
    label: '互联网研究所',
    icon: '🌐',
    color: 'cyan',
    path: '/internet',
  },
  ai: {
    label: 'AI研究所',
    icon: '🤖',
    color: 'purple',
    path: '/ai',
  },
  future: {
    label: '未来研究所',
    icon: '🔮',
    color: 'pink',
    path: '/future',
  },
} as const;

/**
 * 获取所有研究日志
 */
export async function getAllLogs() {
  const logsCollection = await getCollection('logs');
  return logsCollection
    .filter(log => !log.data.draft)
    .sort((a, b) => new Date(b.data.date).getTime() - new Date(a.data.date).getTime());
}

/**
 * 按心情获取日志
 */
export async function getLogsByMood(mood: keyof typeof MOOD_CONFIG) {
  const allLogs = await getAllLogs();
  return allLogs.filter(log => log.data.mood === mood);
}

/**
 * 按研究所获取日志
 */
export async function getLogsByInstitute(institute: keyof typeof INSTITUTE_CONFIG) {
  const allLogs = await getAllLogs();
  return allLogs.filter(
    log => log.data.relatedInstitute && log.data.relatedInstitute.includes(institute)
  );
}

/**
 * 获取最新日志
 */
export async function getRecentLogs(limit: number = 5) {
  const allLogs = await getAllLogs();
  return allLogs.slice(0, limit);
}

/**
 * 按标签获取日志
 */
export async function getLogsByTag(tag: string) {
  const allLogs = await getAllLogs();
  return allLogs.filter(log => log.data.tags && log.data.tags.includes(tag));
}

/**
 * 按日期范围获取日志
 */
export async function getLogsByDateRange(startDate: Date, endDate: Date) {
  const allLogs = await getAllLogs();
  return allLogs.filter(log => {
    const logDate = new Date(log.data.date);
    return logDate >= startDate && logDate <= endDate;
  });
}

/**
 * 获取本月日志
 */
export async function getThisMonthLogs() {
  const now = new Date();
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

  return getLogsByDateRange(startOfMonth, endOfMonth);
}

/**
 * 获取本年日志
 */
export async function getThisYearLogs() {
  const now = new Date();
  const startOfYear = new Date(now.getFullYear(), 0, 1);
  const endOfYear = new Date(now.getFullYear(), 11, 31);

  return getLogsByDateRange(startOfYear, endOfYear);
}

/**
 * 搜索日志
 */
export async function searchLogs(query: string) {
  const allLogs = await getAllLogs();
  const searchTerm = query.toLowerCase();

  return allLogs.filter(log => {
    const title = log.data.title.toLowerCase();
    const tags = log.data.tags?.join(' ').toLowerCase() || '';

    return title.includes(searchTerm) || tags.includes(searchTerm);
  });
}

/**
 * 获取相关日志
 */
export async function getRelatedLogs(currentSlug: string, limit: number = 3) {
  const allLogs = await getAllLogs();
  const currentLog = allLogs.find(log => log.slug === currentSlug);

  if (!currentLog) {
    return [];
  }

  // 计算相关性得分
  const relatedLogs = allLogs
    .filter(log => log.slug !== currentSlug)
    .map(log => {
      let score = 0;

      // 同心情加分
      if (log.data.mood === currentLog.data.mood) {
        score += 3;
      }

      // 相同研究所加分
      if (currentLog.data.relatedInstitute && log.data.relatedInstitute) {
        const commonInstitutes = currentLog.data.relatedInstitute.filter(inst =>
          log.data.relatedInstitute?.includes(inst)
        );
        score += commonInstitutes.length * 2;
      }

      // 相同标签加分
      if (currentLog.data.tags && log.data.tags) {
        const commonTags = currentLog.data.tags.filter(tag => log.data.tags?.includes(tag));
        score += commonTags.length;
      }

      // 时间接近加分
      const timeDiff = Math.abs(
        new Date(log.data.date).getTime() - new Date(currentLog.data.date).getTime()
      );
      const daysDiff = timeDiff / (1000 * 60 * 60 * 24);
      if (daysDiff < 7) {
        score += 2;
      } else if (daysDiff < 30) {
        score += 1;
      }

      return { log, score };
    })
    .filter(item => item.score > 0)
    .sort((a, b) => b.score - a.score)
    .slice(0, limit)
    .map(item => item.log);

  return relatedLogs;
}

/**
 * 获取日志统计信息
 */
export async function getLogStats() {
  const allLogs = await getAllLogs();

  const stats = {
    total: allLogs.length,
    byMood: {} as Record<string, number>,
    byInstitute: {} as Record<string, number>,
    thisMonth: 0,
    thisYear: 0,
    averagePerMonth: 0,
  };

  const now = new Date();
  const thisMonth = now.getMonth();
  const thisYear = now.getFullYear();

  // 计算月平均
  const oldestLog = allLogs[allLogs.length - 1];
  if (oldestLog) {
    const oldestDate = new Date(oldestLog.data.date);
    const monthsDiff =
      (now.getFullYear() - oldestDate.getFullYear()) * 12 +
      (now.getMonth() - oldestDate.getMonth()) +
      1;
    stats.averagePerMonth = Math.round((allLogs.length / monthsDiff) * 10) / 10;
  }

  allLogs.forEach(log => {
    // 按心情统计
    if (log.data.mood) {
      stats.byMood[log.data.mood] = (stats.byMood[log.data.mood] || 0) + 1;
    }

    // 按研究所统计
    if (log.data.relatedInstitute) {
      log.data.relatedInstitute.forEach(institute => {
        stats.byInstitute[institute] = (stats.byInstitute[institute] || 0) + 1;
      });
    }

    // 时间统计
    const logDate = new Date(log.data.date);
    if (logDate.getFullYear() === thisYear) {
      stats.thisYear++;
      if (logDate.getMonth() === thisMonth) {
        stats.thisMonth++;
      }
    }
  });

  return stats;
}

/**
 * 获取热门标签
 */
export async function getPopularTags(limit: number = 10) {
  const allLogs = await getAllLogs();
  const tagCounts: Record<string, number> = {};

  allLogs.forEach(log => {
    if (log.data.tags) {
      log.data.tags.forEach(tag => {
        tagCounts[tag] = (tagCounts[tag] || 0) + 1;
      });
    }
  });

  return Object.entries(tagCounts)
    .sort(([, a], [, b]) => b - a)
    .slice(0, limit)
    .map(([tag, count]) => ({ tag, count }));
}

/**
 * 按月份分组日志
 */
export async function getLogsByMonth() {
  const allLogs = await getAllLogs();
  const groupedLogs: Record<string, typeof allLogs> = {};

  allLogs.forEach(log => {
    const date = new Date(log.data.date);
    const monthKey = `${date.getFullYear()}年${date.getMonth() + 1}月`;

    if (!groupedLogs[monthKey]) {
      groupedLogs[monthKey] = [];
    }
    groupedLogs[monthKey].push(log);
  });

  return groupedLogs;
}

/**
 * 获取日志日历数据
 */
export async function getLogCalendarData() {
  const allLogs = await getAllLogs();
  const calendarData: Record<string, number> = {};

  allLogs.forEach(log => {
    const dateStr = new Date(log.data.date).toISOString().split('T')[0];
    calendarData[dateStr] = (calendarData[dateStr] || 0) + 1;
  });

  return calendarData;
}

/**
 * 获取连续记录天数
 */
export async function getStreakDays() {
  const allLogs = await getAllLogs();
  if (allLogs.length === 0) return 0;

  const dates = allLogs.map(log => new Date(log.data.date).toDateString());
  const uniqueDates = [...new Set(dates)].sort(
    (a, b) => new Date(b as string).getTime() - new Date(a as string).getTime()
  );

  let streak = 0;
  const today = new Date().toDateString();

  for (let i = 0; i < uniqueDates.length; i++) {
    const currentDate = new Date(uniqueDates[i] as string);
    const expectedDate = new Date();
    expectedDate.setDate(expectedDate.getDate() - i);

    if (currentDate.toDateString() === expectedDate.toDateString()) {
      streak++;
    } else {
      break;
    }
  }

  return streak;
}

/**
 * 验证日志数据
 */
export function validateLogData(data: any) {
  const errors: string[] = [];

  if (!data.title) {
    errors.push('缺少标题');
  }

  if (!data.date) {
    errors.push('缺少日期');
  }

  if (data.mood && !MOOD_CONFIG[data.mood as keyof typeof MOOD_CONFIG]) {
    errors.push('无效的心情类型');
  }

  if (data.relatedInstitute) {
    const invalidInstitutes = data.relatedInstitute.filter(
      inst => !INSTITUTE_CONFIG[inst as keyof typeof INSTITUTE_CONFIG]
    );
    if (invalidInstitutes.length > 0) {
      errors.push(`无效的研究所: ${invalidInstitutes.join(', ')}`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * 生成日志摘要
 */
export function generateLogSummary(logs: any[]) {
  const summary = {
    totalLogs: logs.length,
    dateRange: {
      start: logs.length > 0 ? new Date(logs[logs.length - 1].data.date) : null,
      end: logs.length > 0 ? new Date(logs[0].data.date) : null,
    },
    moodDistribution: {} as Record<string, number>,
    topTags: [] as Array<{ tag: string; count: number }>,
    instituteActivity: {} as Record<string, number>,
  };

  // 统计心情分布
  logs.forEach(log => {
    if (log.data.mood) {
      summary.moodDistribution[log.data.mood] = (summary.moodDistribution[log.data.mood] || 0) + 1;
    }
  });

  // 统计标签
  const tagCounts: Record<string, number> = {};
  logs.forEach(log => {
    if (log.data.tags) {
      log.data.tags.forEach((tag: string) => {
        tagCounts[tag] = (tagCounts[tag] || 0) + 1;
      });
    }
  });

  summary.topTags = Object.entries(tagCounts)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 5)
    .map(([tag, count]) => ({ tag, count }));

  // 统计研究所活动
  logs.forEach(log => {
    if (log.data.relatedInstitute) {
      log.data.relatedInstitute.forEach((institute: string) => {
        summary.instituteActivity[institute] = (summary.instituteActivity[institute] || 0) + 1;
      });
    }
  });

  return summary;
}
