---
// 数学公式示例组件
---

<div class="math-examples bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
  <h3 class="text-lg font-semibold mb-4 text-blue-900">数学公式示例</h3>
  
  <div class="space-y-4">
    <!-- 行内公式示例 -->
    <div>
      <h4 class="font-medium mb-2">行内公式：</h4>
      <p>
        爱因斯坦质能方程：<span class="math-inline">E = mc²</span>，其中 E 是能量，m 是质量，c 是光速。
      </p>
    </div>
    
    <!-- 块级公式示例 -->
    <div>
      <h4 class="font-medium mb-2">块级公式：</h4>
      <p>薛定谔方程：</p>
      <div class="math-display">
        iℏ ∂Ψ(r,t)/∂t = ĤΨ(r,t)
      </div>
      
      <p>贝叶斯定理：</p>
      <div class="math-display">
        P(A|B) = P(B|A)P(A) / P(B)
      </div>
    </div>
    
    <!-- 矩阵示例 -->
    <div>
      <h4 class="font-medium mb-2">矩阵：</h4>
      <div class="math-display">
        [a b] [x] = [ax + by]<br>
        [c d] [y]   [cx + dy]
      </div>
    </div>
    
    <!-- 求和符号示例 -->
    <div>
      <h4 class="font-medium mb-2">求和与积分：</h4>
      <div class="math-display">
        Σ(i=1 to n) i = n(n+1)/2
      </div>
      
      <div class="math-display">
        ∫(-∞ to ∞) e^(-x²) dx = √π
      </div>
    </div>
  </div>
</div>

<style>
  .math-inline {
    font-family: 'Times New Roman', serif;
    font-style: italic;
    background-color: #f0f9ff;
    padding: 2px 4px;
    border-radius: 3px;
  }

  .math-display {
    font-family: 'Times New Roman', serif;
    font-size: 1.1em;
    text-align: center;
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 12px;
    margin: 8px 0;
    font-style: italic;
  }
</style>