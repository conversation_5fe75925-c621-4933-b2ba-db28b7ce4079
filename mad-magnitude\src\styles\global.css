@import 'tailwindcss';
@import './tokens.css';

/* 基础样式重置和优化 */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-family:
    'PingFang SC',
    'Hiragino Sans GB',
    'Microsoft YaHei',
    'WenQuanYi Micro Hei',
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    'Helvetica Neue',
    Helvetica,
    Arial,
    sans-serif;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 确保图片响应式 */
img {
  max-width: 100%;
  height: auto;
}

/* 优化按钮和表单元素 */
button,
input,
textarea,
select {
  font: inherit;
}

/* 改善焦点指示器 */
:focus {
  outline: none;
}

:focus-visible {
  outline: 3px solid #3b82f6;
  outline-offset: 2px;
  border-radius: 3px;
}

/* 确保键盘用户能看到焦点 */
.js-focus-visible :focus:not(.focus-visible) {
  outline: none;
}

/* 改善跳转链接 */
.skip-link:focus {
  position: static;
  width: auto;
  height: auto;
  padding: 8px 16px;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
}
