{
  "extends": "astro/tsconfigs/strict",
  "include": [".astro/types.d.ts", "**/*", "src/**/*"],
  "exclude": ["dist", "node_modules", ".astro"],
  "compilerOptions": {
    // 严格类型检查 - 针对Astro项目调整
    "strict": true,
    "noImplicitAny": false, // Astro组件中允许隐式any
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "noImplicitReturns": false, // Astro组件中允许隐式返回
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": false, // 允许未检查的索引访问

    // 模块解析
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,

    // 路径别名
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@/components/*": ["src/components/*"],
      "@/layouts/*": ["src/layouts/*"],
      "@/utils/*": ["src/utils/*"],
      "@/styles/*": ["src/styles/*"],
      "@/content/*": ["src/content/*"]
    },

    // 其他选项
    "skipLibCheck": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true
  }
}
