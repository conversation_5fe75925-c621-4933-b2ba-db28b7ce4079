import { e as getCollection, l as buildSearchIndex } from "../assets/utils.CcA_tyNa.js";
import { i } from "../assets/vendor-astro.kctgsZae.js";
async function GET() {
  const research = await getCollection("research");
  const reflections = await getCollection("reflections");
  const allPosts = [...research, ...reflections];
  const searchIndex = buildSearchIndex(allPosts);
  return new Response(JSON.stringify(searchIndex), {
    headers: {
      "Content-Type": "application/json",
      "Cache-Control": "public, max-age=3600"
      // 缓存1小时
    }
  });
}
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({ __proto__: null, GET }, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  i as renderers
};
