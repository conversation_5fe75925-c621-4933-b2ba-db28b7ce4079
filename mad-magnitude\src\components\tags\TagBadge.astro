---
export interface Props {
  tag: string;
  count?: number;
  color?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'outline' | 'solid';
  clickable?: boolean;
  href?: string;
}

const {
  tag,
  count,
  color = 'gray',
  size = 'md',
  variant = 'default',
  clickable = false,
  href,
} = Astro.props;

// 颜色映射
const colorClasses = {
  blue: 'bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200',
  green: 'bg-green-100 text-green-800 border-green-200 hover:bg-green-200',
  purple: 'bg-purple-100 text-purple-800 border-purple-200 hover:bg-purple-200',
  orange: 'bg-orange-100 text-orange-800 border-orange-200 hover:bg-orange-200',
  indigo: 'bg-indigo-100 text-indigo-800 border-indigo-200 hover:bg-indigo-200',
  pink: 'bg-pink-100 text-pink-800 border-pink-200 hover:bg-pink-200',
  gray: 'bg-gray-100 text-gray-800 border-gray-200 hover:bg-gray-200',
};

const outlineColorClasses = {
  blue: 'border-blue-300 text-blue-700 hover:bg-blue-50',
  green: 'border-green-300 text-green-700 hover:bg-green-50',
  purple: 'border-purple-300 text-purple-700 hover:bg-purple-50',
  orange: 'border-orange-300 text-orange-700 hover:bg-orange-50',
  indigo: 'border-indigo-300 text-indigo-700 hover:bg-indigo-50',
  pink: 'border-pink-300 text-pink-700 hover:bg-pink-50',
  gray: 'border-gray-300 text-gray-700 hover:bg-gray-50',
};

const solidColorClasses = {
  blue: 'bg-blue-600 text-white border-blue-600 hover:bg-blue-700',
  green: 'bg-green-600 text-white border-green-600 hover:bg-green-700',
  purple: 'bg-purple-600 text-white border-purple-600 hover:bg-purple-700',
  orange: 'bg-orange-600 text-white border-orange-600 hover:bg-orange-700',
  indigo: 'bg-indigo-600 text-white border-indigo-600 hover:bg-indigo-700',
  pink: 'bg-pink-600 text-white border-pink-600 hover:bg-pink-700',
  gray: 'bg-gray-600 text-white border-gray-600 hover:bg-gray-700',
};

// 尺寸类
const sizeClasses = {
  sm: 'px-2 py-1 text-xs',
  md: 'px-3 py-1 text-sm',
  lg: 'px-4 py-2 text-base',
};

// 选择颜色类
let colorClass = colorClasses[color as keyof typeof colorClasses] || colorClasses.gray;
if (variant === 'outline') {
  colorClass =
    outlineColorClasses[color as keyof typeof outlineColorClasses] || outlineColorClasses.gray;
} else if (variant === 'solid') {
  colorClass = solidColorClasses[color as keyof typeof solidColorClasses] || solidColorClasses.gray;
}

const sizeClass = sizeClasses[size];
const baseClasses =
  'inline-flex items-center rounded-full border font-medium transition-colors duration-200';
const clickableClasses = clickable ? 'cursor-pointer' : '';

const classes = `${baseClasses} ${colorClass} ${sizeClass} ${clickableClasses}`;
---

{
  href ? (
    <a href={href} class={classes} title={`查看标签"${tag}"的所有内容`}>
      <span>{tag}</span>
      {count && count > 1 && <span class="ml-1 opacity-75">({count})</span>}
    </a>
  ) : (
    <span class={classes}>
      <span>{tag}</span>
      {count && count > 1 && <span class="ml-1 opacity-75">({count})</span>}
    </span>
  )
}

<style>
  /* 确保标签在不同状态下的视觉一致性 */
  a:focus-visible {
    outline: 2px solid currentColor;
    outline-offset: 2px;
  }
</style>
