---
// SEO 优化：启用静态预渲染以提高搜索引擎友好性
export const prerender = true;

/**
 * 单个标签页面 - SEO 优化版本
 * 展示特定标签的所有相关内容，包含完整的 SEO 元数据和结构化数据
 */
import { getCollection, type CollectionEntry } from 'astro:content';
import Layout from '../../layouts/Layout.astro';
import TagList from '../../components/tags/TagList.astro';
import { globalTagManager } from '../../utils/tagManager';
import { formatDate } from '../../utils/dateUtils';

// 定义内容项类型
interface ContentItem {
  slug: string;
  data: {
    title: string | { zh: string; en?: string };
    description?: string | { zh: string; en?: string };
    pubDate?: Date;
    date?: Date;
    tags?: string[];
    author?: string;
  };
  collection: string;
  url: string;
}

export async function getStaticPaths() {
  // 获取所有标签
  const tagStats = await globalTagManager.getTagStats();
  const allTags = tagStats.mostPopularTags.map(tag => tag.name);

  return allTags.map(tag => ({
    params: { tag: encodeURIComponent(tag) },
    props: { 
      tagName: tag,
      tagStats: tagStats // 传递标签统计信息用于SEO
    },
  }));
}

const { tag } = Astro.params;
const { tagName, tagStats } = Astro.props;

// 解码标签名称
const decodedTag = decodeURIComponent(tag || '');

// 获取标签信息
const tagInfo = await globalTagManager.getTagInfo(decodedTag);
const relatedTags = await globalTagManager.getRelatedTags(decodedTag);

// 获取所有包含此标签的内容
const collections = [
  'research',
  'news',
  'logs',
  'ai',
  'economics',
  'philosophy',
  'internet',
  'future',
  'products',
  'reflections',
] as const;

const taggedContent: ContentItem[] = [];

for (const collectionName of collections) {
  try {
    const collection = await getCollection(collectionName);
    const filtered = collection.filter(
      (entry: CollectionEntry<typeof collectionName>) =>
        entry.data.tags && entry.data.tags.includes(decodedTag)
    );

    // 添加集合信息
    const withCollection = filtered.map((entry: CollectionEntry<typeof collectionName>) => ({
      ...entry,
      collection: collectionName,
      url: `/${collectionName}/${entry.slug}`,
    }));

    taggedContent.push(...withCollection);
  } catch (error) {
    console.warn(`Failed to load collection ${collectionName}:`, error);
  }
}

// 按日期排序（最新的在前）
taggedContent.sort((a, b) => {
  const dateA = new Date(a.data.pubDate || a.data.date || '1970-01-01');
  const dateB = new Date(b.data.pubDate || b.data.date || '1970-01-01');
  return dateB.getTime() - dateA.getTime();
});

// SEO 优化的页面元数据
const pageTitle = `${decodedTag} 标签 - Pennfly Private Academy`;
const metaDescription = `探索 Pennfly Private Academy 中所有关于"${decodedTag}"的学术内容。包含 ${taggedContent.length} 篇研究文章，涵盖 AI、经济学、哲学等多个领域的深度分析。`;
const canonicalUrl = `${Astro.site}tags/${encodeURIComponent(decodedTag)}`;

// 生成更丰富的关键词
const keywords = [
  decodedTag,
  '学术研究',
  'Pennfly Private Academy',
  ...relatedTags.slice(0, 5).map(t => t.name),
  ...Array.from(new Set(taggedContent.map(item => getCollectionDisplayName(item.collection)))),
].join(', ');

// 获取标题文本（处理多语言）
function getTitle(title: string | { zh: string; en?: string }): string {
  return typeof title === 'string' ? title : title.zh;
}

// 获取描述文本（处理多语言）
function getDescription(description?: string | { zh: string; en?: string }): string {
  if (!description) return '';
  return typeof description === 'string' ? description : description.zh;
}

// 生成结构化数据
const structuredData = {
  '@context': 'https://schema.org',
  '@type': 'CollectionPage',
  name: pageTitle,
  description: metaDescription,
  url: canonicalUrl,
  mainEntity: {
    '@type': 'ItemList',
    numberOfItems: taggedContent.length,
    itemListElement: taggedContent.slice(0, 10).map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      item: {
        '@type': 'Article',
        headline: getTitle(item.data.title),
        description: getDescription(item.data.description),
        url: `${Astro.site}${item.url.startsWith('/') ? item.url.slice(1) : item.url}`,
        datePublished: (item.data.pubDate || item.data.date)?.toISOString(),
        author: {
          '@type': 'Person',
          name: item.data.author || 'Pennfly',
        },
        publisher: {
          '@type': 'Organization',
          name: 'Pennfly Private Academy',
        },
      },
    })),
  },
  breadcrumb: {
    '@type': 'BreadcrumbList',
    itemListElement: [
      {
        '@type': 'ListItem',
        position: 1,
        name: '首页',
        item: Astro.site,
      },
      {
        '@type': 'ListItem',
        position: 2,
        name: '标签',
        item: `${Astro.site}tags`,
      },
      {
        '@type': 'ListItem',
        position: 3,
        name: decodedTag,
        item: canonicalUrl,
      },
    ],
  },
};

// 获取集合显示名称
function getCollectionDisplayName(collection: string): string {
  const names: Record<string, string> = {
    research: '研究',
    news: '动态',
    logs: '日志',
    ai: 'AI',
    economics: '经济',
    philosophy: '哲学',
    internet: '互联网',
    future: '未来',
    products: '产品',
    reflections: '反思',
  };
  return names[collection] || collection;
}

// 获取集合图标
function getCollectionIcon(collection: string): string {
  const icons: Record<string, string> = {
    research: '📊',
    news: '📰',
    logs: '📝',
    ai: '🤖',
    economics: '💰',
    philosophy: '🤔',
    internet: '🌐',
    future: '🔮',
    products: '🛠️',
    reflections: '💭',
  };
  return icons[collection] || '📄';
}
---

<Layout
  title={pageTitle}
  description={metaDescription}
  canonicalUrl={canonicalUrl}
  ogType="website"
  keywords={keywords.split(', ')}
  author="Pennfly"
  structuredData={structuredData}
>
  <!-- 结构化数据 -->
  <script type="application/ld+json" is:inline set:html={JSON.stringify(structuredData)} />

  <!-- 额外的 SEO meta 标签 -->
  <meta name="keywords" content={keywords} slot="head" />
  <meta name="robots" content="index, follow, max-image-preview:large" slot="head" />
  <meta name="author" content="Pennfly Private Academy" slot="head" />
  
  <!-- Open Graph 标签 -->
  <meta property="og:title" content={pageTitle} slot="head" />
  <meta property="og:description" content={metaDescription} slot="head" />
  <meta property="og:url" content={canonicalUrl} slot="head" />
  <meta property="og:type" content="website" slot="head" />
  <meta property="og:site_name" content="Pennfly Private Academy" slot="head" />
  <meta property="og:locale" content="zh_CN" slot="head" />
  
  <!-- Twitter Card 标签 -->
  <meta name="twitter:card" content="summary_large_image" slot="head" />
  <meta name="twitter:title" content={pageTitle} slot="head" />
  <meta name="twitter:description" content={metaDescription} slot="head" />
  
  <!-- 移动端优化 -->
  <meta name="theme-color" content={tagInfo?.color || '#3b82f6'} slot="head" />
  <meta name="mobile-web-app-capable" content="yes" slot="head" />
  
  <!-- 搜索引擎优化 -->
  <meta name="googlebot" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" slot="head" />
  <meta name="bingbot" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" slot="head" />

  <!-- 预加载关键资源 -->
  <link
    rel="preload"
    href="/fonts/inter.woff2"
    as="font"
    type="font/woff2"
    crossorigin
    slot="head"
  />

  <!-- 相关标签的预连接 -->
  {
    relatedTags
      .slice(0, 3)
      .map(relatedTag => (
        <link rel="prefetch" href={`/tags/${encodeURIComponent(relatedTag.name)}`} slot="head" />
      ))
  }
  <main class="tag-detail-page">
    <!-- 页面头部 -->
    <header class="page-header">
      <div class="container">
        <div class="header-content">
          <nav class="breadcrumb">
            <a href="/" class="breadcrumb-link">首页</a>
            <span class="breadcrumb-separator">›</span>
            <a href="/tags" class="breadcrumb-link">标签</a>
            <span class="breadcrumb-separator">›</span>
            <span class="breadcrumb-current">{decodedTag}</span>
          </nav>

          <div class="tag-header">
            <h1 class="page-title">
              <span class="title-icon" aria-hidden="true">🏷️</span>
              {decodedTag}
            </h1>

            {
              tagInfo && (
                <div class="tag-meta">
                  <div class="tag-stats">
                    <span class="stat-item">
                      <span class="stat-icon">📚</span>
                      <span class="stat-text">{tagInfo.count} 篇内容</span>
                    </span>
                    <span class="stat-item">
                      <span class="stat-icon">📂</span>
                      <span class="stat-text">
                        {tagInfo.category === 'technology' && '技术'}
                        {tagInfo.category === 'economics' && '经济'}
                        {tagInfo.category === 'philosophy' && '哲学'}
                        {tagInfo.category === 'society' && '社会'}
                        {tagInfo.category === 'research' && '研究'}
                        {tagInfo.category === 'tools' && '工具'}
                        {tagInfo.category === 'general' && '通用'}
                      </span>
                    </span>
                  </div>
                </div>
              )
            }
          </div>
        </div>
      </div>
    </header>

    <div class="container">
      <div class="content-grid">
        <!-- 主要内容 -->
        <section class="main-content">
          {
            taggedContent.length > 0 ? (
              <div class="content-list">
                <div class="list-header">
                  <h2 class="list-title">相关内容 ({taggedContent.length})</h2>
                  <div class="list-filters">
                    <select class="filter-select" id="collectionFilter">
                      <option value="all">所有类型</option>
                      {Array.from(new Set(taggedContent.map(item => item.collection))).map(
                        collection => (
                          <option value={collection}>
                            {getCollectionIcon(collection)} {getCollectionDisplayName(collection)}
                          </option>
                        )
                      )}
                    </select>
                  </div>
                </div>

                <div class="content-items">
                  {taggedContent.map(item => (
                    <article class="content-item" data-collection={item.collection}>
                      <div class="item-header">
                        <div class="item-meta">
                          <span
                            class="collection-badge"
                            style={`--badge-color: ${tagInfo?.color || '#64748b'}`}
                          >
                            <span class="badge-icon">{getCollectionIcon(item.collection)}</span>
                            <span class="badge-text">
                              {getCollectionDisplayName(item.collection)}
                            </span>
                          </span>
                          <time
                            class="item-date"
                            datetime={(item.data.pubDate || item.data.date)?.toISOString()}
                          >
                            {formatDate(item.data.pubDate || item.data.date || new Date())}
                          </time>
                        </div>
                      </div>

                      <div class="item-content">
                        <h3 class="item-title">
                          <a
                            href={item.url}
                            class="title-link"
                            aria-describedby={`desc-${item.slug}`}
                          >
                            {getTitle(item.data.title)}
                          </a>
                        </h3>

                        {item.data.description && (
                          <p class="item-description" id={`desc-${item.slug}`}>
                            {getDescription(item.data.description)}
                          </p>
                        )}

                        {item.data.tags && item.data.tags.length > 1 && (
                          <div class="item-tags">
                            <TagList
                              tags={item.data.tags.filter((t: string) => t !== decodedTag)}
                              variant="compact"
                              maxTags={5}
                              size="small"
                            />
                          </div>
                        )}
                      </div>

                      <div class="item-footer">
                        <a href={item.url} class="read-more-link">
                          阅读全文 →
                        </a>
                      </div>
                    </article>
                  ))}
                </div>
              </div>
            ) : (
              <div class="empty-state" role="status" aria-live="polite">
                <div class="empty-icon" aria-hidden="true">📭</div>
                <h2 class="empty-title">暂无相关内容</h2>
                <p class="empty-description">目前还没有标记为 "{decodedTag}" 的内容。您可以：</p>
                <div class="empty-actions">
                  <a href="/tags" class="action-button" aria-label="浏览所有可用标签">
                    浏览其他标签
                  </a>
                  <a href="/search" class="action-button action-button--secondary" aria-label="搜索相关内容">
                    搜索内容
                  </a>
                </div>
                
                <!-- SEO 优化：添加相关内容推荐 -->
                {relatedTags.length > 0 && (
                  <div class="suggested-tags">
                    <h3>推荐标签：</h3>
                    <div class="tag-suggestions">
                      {relatedTags.slice(0, 5).map(relatedTag => (
                        <a 
                          href={`/tags/${encodeURIComponent(relatedTag.name)}`}
                          class="suggested-tag"
                          title={`查看 ${relatedTag.name} 相关内容 (${relatedTag.count} 篇)`}
                        >
                          {relatedTag.name}
                        </a>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )
          }
        </section>

        <!-- 侧边栏 -->
        <aside class="sidebar">
          {
            relatedTags.length > 0 && (
              <div class="sidebar-section">
                <h3 class="sidebar-title">
                  <span class="title-icon">🔗</span>
                  相关标签
                </h3>
                <div class="related-tags">
                  {relatedTags.map(relatedTag => (
                    <a
                      href={`/tags/${encodeURIComponent(relatedTag.name)}`}
                      class="related-tag"
                      style={`--tag-color: ${relatedTag.color}`}
                      title={`${relatedTag.name} (${relatedTag.count} 篇内容)`}
                    >
                      <span class="tag-name">{relatedTag.name}</span>
                      <span class="tag-count">({relatedTag.count})</span>
                    </a>
                  ))}
                </div>
              </div>
            )
          }

          <div class="sidebar-section">
            <h3 class="sidebar-title">
              <span class="title-icon">🧭</span>
              快速导航
            </h3>
            <nav class="quick-nav">
              <a href="/tags" class="nav-link">🏷️ 所有标签</a>
              <a href="/search" class="nav-link">🔍 搜索内容</a>
              <a href="/research" class="nav-link">📊 研究报告</a>
              <a href="/news" class="nav-link">📰 最新动态</a>
            </nav>
          </div>

          <div class="sidebar-section">
            <h3 class="sidebar-title">
              <span class="title-icon">💡</span>
              使用提示
            </h3>
            <ul class="tips-list">
              <li>点击相关标签探索更多内容</li>
              <li>使用筛选器按类型查看内容</li>
              <li>标签颜色表示不同的主题分类</li>
              <li>可以组合多个标签进行搜索</li>
            </ul>
          </div>
        </aside>
      </div>
    </div>
  </main>
</Layout>

<style>
  .tag-detail-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  /* 页面头部 */
  .page-header {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    color: white;
    padding: 2rem 0 3rem;
    margin-bottom: 2rem;
  }

  .breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
  }

  .breadcrumb-link {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.2s ease;
  }

  .breadcrumb-link:hover {
    color: white;
  }

  .breadcrumb-separator {
    color: rgba(255, 255, 255, 0.6);
  }

  .breadcrumb-current {
    color: white;
    font-weight: 500;
  }

  .tag-header {
    text-align: center;
  }

  .page-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
  }

  .title-icon {
    font-size: 2rem;
  }

  .tag-meta {
    display: flex;
    justify-content: center;
  }

  .tag-stats {
    display: flex;
    gap: 2rem;
  }

  .stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    opacity: 0.9;
  }

  .stat-icon {
    font-size: 1.25rem;
  }

  /* 内容网格 */
  .content-grid {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 2rem;
    margin-bottom: 3rem;
  }

  /* 主要内容 */
  .main-content {
    background: white;
    border-radius: 0.75rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    background: #f8fafc;
  }

  .list-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
  }

  .filter-select {
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    background: white;
    font-size: 0.875rem;
    cursor: pointer;
  }

  .content-items {
    display: flex;
    flex-direction: column;
  }

  .content-item {
    padding: 1.5rem;
    border-bottom: 1px solid #f1f5f9;
    transition: background-color 0.2s ease;
  }

  .content-item:hover {
    background: #f8fafc;
  }

  .content-item:last-child {
    border-bottom: none;
  }

  .item-header {
    margin-bottom: 1rem;
  }

  .item-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .collection-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.25rem 0.75rem;
    background: rgba(var(--badge-color-rgb, 100, 116, 139), 0.1);
    color: var(--badge-color, #64748b);
    border: 1px solid rgba(var(--badge-color-rgb, 100, 116, 139), 0.2);
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .badge-icon {
    font-size: 0.875rem;
  }

  .item-date {
    font-size: 0.875rem;
    color: #64748b;
  }

  .item-content {
    margin-bottom: 1rem;
  }

  .item-title {
    margin-bottom: 0.75rem;
  }

  .title-link {
    color: #1e293b;
    text-decoration: none;
    font-size: 1.25rem;
    font-weight: 600;
    line-height: 1.4;
    transition: color 0.2s ease;
  }

  .title-link:hover {
    color: #3b82f6;
  }

  .item-description {
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 1rem;
  }

  .item-tags {
    margin-bottom: 1rem;
  }

  .item-footer {
    display: flex;
    justify-content: flex-end;
  }

  .read-more-link {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
    transition: color 0.2s ease;
  }

  .read-more-link:hover {
    color: #2563eb;
  }

  /* 空状态 */
  .empty-state {
    text-align: center;
    padding: 4rem 2rem;
  }

  .empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.6;
  }

  .empty-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.75rem;
  }

  .empty-description {
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 2rem;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
  }

  .empty-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 2rem;
  }

  /* 推荐标签样式 */
  .suggested-tags {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e2e8f0;
  }

  .suggested-tags h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
    text-align: center;
  }

  .tag-suggestions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    justify-content: center;
  }

  .suggested-tag {
    display: inline-block;
    padding: 0.5rem 1rem;
    background: #f1f5f9;
    color: #475569;
    text-decoration: none;
    border-radius: 1.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
  }

  .suggested-tag:hover {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
    transform: translateY(-1px);
  }

  .action-button {
    padding: 0.75rem 1.5rem;
    background: #3b82f6;
    color: white;
    text-decoration: none;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: background-color 0.2s ease;
  }

  .action-button:hover {
    background: #2563eb;
  }

  .action-button--secondary {
    background: #f1f5f9;
    color: #64748b;
    border: 1px solid #e2e8f0;
  }

  .action-button--secondary:hover {
    background: #e2e8f0;
    color: #475569;
  }

  /* 侧边栏 */
  .sidebar {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .sidebar-section {
    background: white;
    border-radius: 0.75rem;
    border: 1px solid #e2e8f0;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .sidebar-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
  }

  .related-tags {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .related-tag {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0.75rem;
    background: rgba(var(--tag-color-rgb, 59, 130, 246), 0.1);
    color: var(--tag-color, #3b82f6);
    border: 1px solid rgba(var(--tag-color-rgb, 59, 130, 246), 0.2);
    border-radius: 0.375rem;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .related-tag:hover {
    background: rgba(var(--tag-color-rgb, 59, 130, 246), 0.15);
    transform: translateX(2px);
  }

  .tag-count {
    font-size: 0.75rem;
    opacity: 0.7;
  }

  .quick-nav {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    color: #64748b;
    text-decoration: none;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
    font-size: 0.875rem;
  }

  .nav-link:hover {
    background: #f1f5f9;
    color: #334155;
  }

  .tips-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .tips-list li {
    font-size: 0.875rem;
    color: #64748b;
    line-height: 1.5;
    margin-bottom: 0.5rem;
    padding-left: 1rem;
    position: relative;
  }

  .tips-list li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: #3b82f6;
  }

  /* 响应式设计 */
  @media (max-width: 1024px) {
    .content-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .sidebar {
      order: -1;
    }

    .sidebar-section {
      padding: 1rem;
    }
  }

  @media (max-width: 768px) {
    .page-header {
      padding: 1.5rem 0 2rem;
    }

    .page-title {
      font-size: 2rem;
    }

    .tag-stats {
      flex-direction: column;
      gap: 1rem;
    }

    .container {
      padding: 0 0.75rem;
    }

    .list-header {
      flex-direction: column;
      gap: 1rem;
      align-items: stretch;
    }

    .content-item {
      padding: 1rem;
    }

    .item-meta {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }

    .empty-actions {
      flex-direction: column;
      align-items: center;
    }
  }
</style>

<script>
  // 标签颜色处理
  document.addEventListener('DOMContentLoaded', () => {
    const colorElements = document.querySelectorAll(
      '[style*="--tag-color"], [style*="--badge-color"]'
    );
    colorElements.forEach(element => {
      const htmlElement = element as HTMLElement;
      const style = htmlElement.getAttribute('style') || '';
      const colorMatch = style.match(/--(?:tag|badge)-color:\s*([^;]+)/);
      if (colorMatch) {
        const colorValue = colorMatch[1].trim();
        if (colorValue.startsWith('#')) {
          const hex = colorValue.replace('#', '');
          const r = parseInt(hex.substring(0, 2), 16);
          const g = parseInt(hex.substring(2, 4), 16);
          const b = parseInt(hex.substring(4, 6), 16);
          htmlElement.style.setProperty('--tag-color-rgb', `${r}, ${g}, ${b}`);
          htmlElement.style.setProperty('--badge-color-rgb', `${r}, ${g}, ${b}`);
        }
      }
    });

    // 集合筛选功能
    const collectionFilter = document.getElementById('collectionFilter') as HTMLSelectElement;
    const contentItems = document.querySelectorAll('.content-item');

    if (collectionFilter) {
      collectionFilter.addEventListener('change', () => {
        const selectedCollection = collectionFilter.value;

        contentItems.forEach(item => {
          const itemCollection = item.getAttribute('data-collection');
          if (selectedCollection === 'all' || itemCollection === selectedCollection) {
            (item as HTMLElement).style.display = 'block';
          } else {
            (item as HTMLElement).style.display = 'none';
          }
        });
      });
    }
  });
</script>
