/**
 * Theme utility functions for Pennfly Private Academy
 * Provides helper functions for theme management and CSS variable access
 */

import type { ThemeMode, ThemeVariant, Theme, ThemeCSSVariables } from '../types/theme';
import { themeConfig } from '../styles/tokens/themes';

/**
 * Get the current theme from the theme manager
 */
export function getCurrentTheme(): ThemeMode {
  if (typeof window === 'undefined') return 'light';
  const themeManager = (window as any).themeManager;
  return themeManager?.getTheme() || 'light';
}

/**
 * Get the current theme variant
 */
export function getCurrentVariant(): ThemeVariant {
  if (typeof window === 'undefined') return 'default';
  const themeManager = (window as any).themeManager;
  return themeManager?.getVariant() || 'default';
}

/**
 * Get the resolved theme (accounting for auto mode)
 */
export function getResolvedTheme(): ThemeMode {
  if (typeof window === 'undefined') return 'light';
  const themeManager = (window as any).themeManager;
  return themeManager?.getResolvedThemePublic() || 'light';
}

/**
 * Get the system theme preference
 */
export function getSystemTheme(): ThemeMode {
  if (typeof window === 'undefined') return 'light';
  const themeManager = (window as any).themeManager;
  return themeManager?.getSystemThemePublic() || 'light';
}

/**
 * Set the theme programmatically
 */
export function setTheme(mode: ThemeMode): void {
  if (typeof window === 'undefined') return;
  const themeManager = (window as any).themeManager;
  themeManager?.setTheme(mode);
}

/**
 * Set the theme variant programmatically
 */
export function setVariant(variant: ThemeVariant): void {
  if (typeof window === 'undefined') return;
  const themeManager = (window as any).themeManager;
  themeManager?.setVariant(variant);
}

/**
 * Toggle between light and dark themes
 */
export function toggleTheme(): void {
  if (typeof window === 'undefined') return;
  const themeManager = (window as any).themeManager;
  themeManager?.toggleTheme();
}

/**
 * Get theme configuration by name
 */
export function getThemeConfig(themeName: keyof typeof themeConfig.themes): Theme {
  return themeConfig.themes[themeName];
}

/**
 * Get all available themes
 */
export function getAllThemes(): Record<string, Theme> {
  return themeConfig.themes;
}

/**
 * Check if a theme is dark
 */
export function isDarkTheme(theme?: ThemeMode): boolean {
  const currentTheme = theme || getResolvedTheme();
  return currentTheme === 'dark';
}

/**
 * Check if high contrast mode is enabled
 */
export function isHighContrastMode(): boolean {
  return getCurrentVariant() === 'high-contrast';
}

/**
 * Get CSS custom property value
 */
export function getCSSVariable(property: keyof ThemeCSSVariables): string {
  if (typeof window === 'undefined') return '';

  const value = getComputedStyle(document.documentElement).getPropertyValue(property).trim();

  return value;
}

/**
 * Set CSS custom property value
 */
export function setCSSVariable(property: keyof ThemeCSSVariables, value: string): void {
  if (typeof window === 'undefined') return;
  document.documentElement.style.setProperty(property, value);
}

/**
 * Get theme-aware color value
 * Returns appropriate color based on current theme
 */
export function getThemeColor(lightColor: string, darkColor: string): string {
  return isDarkTheme() ? darkColor : lightColor;
}

/**
 * Generate theme-aware CSS classes
 */
export function getThemeClasses(baseClasses: string, darkClasses?: string): string {
  if (!darkClasses) return baseClasses;
  return `${baseClasses} dark:${darkClasses}`;
}

/**
 * Create a theme-aware style object
 */
export function createThemeStyles(styles: {
  light: Record<string, string>;
  dark: Record<string, string>;
}): Record<string, string> {
  const currentTheme = getResolvedTheme();
  return currentTheme === 'dark' ? styles.dark : styles.light;
}

/**
 * Listen for theme changes
 */
export function onThemeChange(callback: (event: CustomEvent) => void): () => void {
  if (typeof window === 'undefined') return () => {};

  window.addEventListener('themechange', callback as EventListener);

  // Return cleanup function
  return () => {
    window.removeEventListener('themechange', callback as EventListener);
  };
}

/**
 * Get theme metadata for SEO and social sharing
 */
export function getThemeMetadata(): {
  themeColor: string;
  colorScheme: string;
  preferredColorScheme: string;
} {
  const resolvedTheme = getResolvedTheme();
  const variant = getCurrentVariant();

  let themeColor = '#ffffff'; // Default light
  if (resolvedTheme === 'dark') {
    themeColor = variant === 'high-contrast' ? '#000000' : '#111827';
  }

  return {
    themeColor,
    colorScheme: resolvedTheme === 'dark' ? 'dark' : 'light',
    preferredColorScheme: getCurrentTheme() === 'auto' ? 'light dark' : resolvedTheme,
  };
}

/**
 * Validate theme mode
 */
export function isValidThemeMode(mode: string): mode is ThemeMode {
  return ['light', 'dark', 'auto'].includes(mode);
}

/**
 * Validate theme variant
 */
export function isValidThemeVariant(variant: string): variant is ThemeVariant {
  return ['default', 'high-contrast'].includes(variant);
}

/**
 * Get theme-appropriate focus ring classes
 */
export function getFocusRingClasses(): string {
  const variant = getCurrentVariant();

  if (variant === 'high-contrast') {
    return 'focus:ring-4 focus:ring-blue-300 dark:focus:ring-blue-800';
  }

  return 'focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900';
}

/**
 * Get theme-appropriate shadow classes
 */
export function getShadowClasses(size: 'sm' | 'md' | 'lg' | 'xl' = 'md'): string {
  const isDark = isDarkTheme();
  const variant = getCurrentVariant();

  if (variant === 'high-contrast') {
    return `shadow-${size} shadow-white/20`;
  }

  if (isDark) {
    return `shadow-${size} shadow-black/50`;
  }

  return `shadow-${size}`;
}

/**
 * Create responsive theme classes
 */
export function createResponsiveThemeClasses(
  baseClasses: string,
  responsiveClasses: Record<string, string> = {}
): string {
  const classes = [baseClasses];

  Object.entries(responsiveClasses).forEach(([breakpoint, className]) => {
    classes.push(`${breakpoint}:${className}`);
  });

  return classes.join(' ');
}

/**
 * Get theme transition classes
 */
export function getTransitionClasses(properties: string[] = ['colors']): string {
  const baseTransition = 'transition-all duration-200 ease-in-out';

  if (
    typeof window !== 'undefined' &&
    window.matchMedia('(prefers-reduced-motion: reduce)').matches
  ) {
    return 'transition-none';
  }

  const propertyClasses = properties.map(prop => `transition-${prop}`).join(' ');
  return `${propertyClasses} duration-200 ease-in-out`;
}
