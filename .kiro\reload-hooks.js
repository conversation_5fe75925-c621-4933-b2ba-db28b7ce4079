#!/usr/bin/env node

/**
 * 重新加载 Kiro Hooks 的脚本
 * 用于调试和刷新 hooks 配置
 */

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log("🔄 重新加载 Kiro Hooks...\n");

// 检查 hooks 目录
const hooksDir = path.join(__dirname, "hooks");
const steeringDir = path.join(__dirname, "steering");

console.log("📁 检查目录结构:");
console.log(
  `  Hooks 目录: ${fs.existsSync(hooksDir) ? "✅" : "❌"} ${hooksDir}`
);
console.log(
  `  Steering 目录: ${fs.existsSync(steeringDir) ? "✅" : "❌"} ${steeringDir}`
);

// 列出所有 hooks
if (fs.existsSync(hooksDir)) {
  const hookFiles = fs
    .readdirSync(hooksDir)
    .filter((f) => f.endsWith(".kiro.hook"));
  console.log(`\n🎣 发现 ${hookFiles.length} 个 Hook 文件:`);

  hookFiles.forEach((file, index) => {
    try {
      const hookPath = path.join(hooksDir, file);
      const hookData = JSON.parse(fs.readFileSync(hookPath, "utf8"));
      const triggerType =
        hookData.when?.type === "manual" ? "🔧 手动" : "🔄 自动";
      console.log(`  ${index + 1}. ${triggerType} ${hookData.name || file}`);
    } catch (error) {
      console.log(`  ${index + 1}. ❌ ${file} (格式错误: ${error.message})`);
    }
  });
}

// 列出所有 steering 文件
if (fs.existsSync(steeringDir)) {
  const steeringFiles = fs
    .readdirSync(steeringDir)
    .filter((f) => f.endsWith(".md"));
  console.log(`\n📋 发现 ${steeringFiles.length} 个 Steering 文件:`);

  steeringFiles.forEach((file, index) => {
    console.log(`  ${index + 1}. ${file}`);
  });
}

// 验证配置文件
const configFiles = ["config.json", "settings.json"];
console.log("\n⚙️ 配置文件状态:");
configFiles.forEach((file) => {
  const filePath = path.join(__dirname, file);
  const exists = fs.existsSync(filePath);
  console.log(`  ${file}: ${exists ? "✅" : "❌"}`);

  if (exists) {
    try {
      JSON.parse(fs.readFileSync(filePath, "utf8"));
      console.log(`    格式: ✅ 有效的 JSON`);
    } catch (error) {
      console.log(`    格式: ❌ JSON 错误 - ${error.message}`);
    }
  }
});

console.log("\n💡 如果 Hooks 仍未显示，请尝试:");
console.log("  1. 重启 Kiro IDE");
console.log("  2. 检查 Kiro 的 Agent Hooks 面板");
console.log("  3. 查看 Kiro 的设置中是否启用了 Hooks");
console.log("  4. 检查工作区是否正确加载");

console.log("\n✨ 重新加载完成！");
