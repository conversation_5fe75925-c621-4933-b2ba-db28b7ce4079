# 设计令牌系统

本目录包含 Pennfly Private
Academy 项目的完整设计令牌系统，用于确保整个应用的视觉一致性。

## 文件结构

```
tokens/
├── index.ts          # 统一导出文件
├── colors.ts         # 颜色系统
├── typography.ts     # 字体系统
├── spacing.ts        # 间距系统
├── radius.ts         # 圆角系统
├── shadows.ts        # 阴影系统
└── README.md         # 文档说明
```

## 使用方法

### 在 TypeScript/JavaScript 中使用

```typescript
import { colors, typography, spacing } from '@/styles/tokens';

// 使用颜色令牌
const primaryColor = colors.primary[600];

// 使用字体令牌
const headingFont = typography.fontFamily.sans;

// 使用间距令牌
const componentPadding = spacing[4];
```

### 在 Tailwind CSS 中使用

设计令牌已集成到 Tailwind 配置中，可直接使用：

```html
<!-- 使用主色调 -->
<button class="bg-primary-600 text-white">按钮</button>

<!-- 使用语义颜色 -->
<div class="text-success bg-success/10">成功消息</div>

<!-- 使用字体系统 -->
<h1 class="font-sans text-2xl font-bold">标题</h1>
<p class="font-serif text-lg leading-relaxed">正文内容</p>

<!-- 使用间距系统 -->
<div class="m-6 space-y-2 p-4">内容</div>
```

### 在 CSS 中使用自定义属性

```css
.custom-component {
  background-color: rgb(var(--color-bg-primary));
  color: rgb(var(--color-text-primary));
  font-family: var(--font-sans);
  padding: var(--spacing-md);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
}
```

## 设计令牌说明

### 颜色系统

- **主色调 (Primary)**: 学术蓝，用于主要操作和品牌元素
- **辅助色 (Secondary)**: 学术金，用于强调和装饰
- **语义色**: 成功、警告、错误、信息状态的标准颜色
- **中性色**: 文本、背景、边框的灰度色阶
- **主题色**: 支持亮色、暗色和高对比度主题

### 字体系统

- **Sans**: 无衬线字体，用于界面和标题
- **Serif**: 衬线字体，用于正文阅读
- **Mono**: 等宽字体，用于代码显示
- **Math**: 数学字体，用于公式渲染

### 间距系统

基于 4px 网格系统：

- 基础单位：4px (0.25rem)
- 语义化间距：组件、布局、内容、表单等不同场景的间距

### 圆角系统

- 从 2px 到 24px 的渐进式圆角
- 语义化定义：按钮、卡片、输入框、图片、弹窗等

### 阴影系统

- 5 个层级的阴影深度
- 支持彩色阴影用于强调
- 暗色主题的阴影适配
- 语义化定义：卡片、按钮、弹窗、下拉菜单等

## 主题切换

系统支持三种主题模式：

1. **亮色主题** (默认)
2. **暗色主题** (`[data-theme="dark"]`)
3. **高对比度主题** (`[data-theme="high-contrast"]`)

主题切换通过 CSS 自定义属性实现，确保平滑过渡。

## 可访问性

- 所有颜色组合都符合 WCAG 2.1 AA 对比度标准
- 支持 `prefers-reduced-motion` 媒体查询
- 高对比度主题提供更好的可访问性支持

## 扩展指南

### 添加新颜色

1. 在 `colors.ts` 中定义新的颜色令牌
2. 在 `tokens.css` 中添加对应的 CSS 自定义属性
3. 更新 `tailwind.config.js` 中的颜色配置

### 添加新的语义化令牌

1. 在对应的令牌文件中添加语义化定义
2. 在文档中说明使用场景
3. 创建相应的工具类（如需要）

## 最佳实践

1. **优先使用语义化令牌**：使用 `semanticSpacing.component.md` 而不是
   `spacing[4]`
2. **保持一致性**：在相似的场景中使用相同的令牌
3. **避免硬编码**：不要在组件中直接使用具体的数值
4. **考虑主题适配**：确保新组件在所有主题下都能正常显示
5. **测试可访问性**：验证颜色对比度和键盘导航

## 版本历史

- v1.0.0: 初始设计令牌系统
  - 完整的颜色、字体、间距、圆角、阴影系统
  - 多主题支持
  - Tailwind CSS 集成
  - CSS 自定义属性支持
