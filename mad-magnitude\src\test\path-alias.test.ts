/**
 * 路径别名测试
 *
 * 验证 TypeScript 和 Vitest 的路径别名配置是否正确工作
 */

import { describe, expect, it } from 'vitest';

// 测试路径别名导入
import { formatTestData, getTestMessage, TEST_CONSTANTS } from '@/utils/test-helper';

describe('路径别名功能测试', () => {
  it('应该能够使用 @/utils 别名导入工具函数', () => {
    const message = getTestMessage();
    expect(message).toBe('Hello from utils!');
  });

  it('应该能够使用导入的格式化函数', () => {
    const testData = { name: 'Test', value: 123 };
    const formatted = formatTestData(testData);

    expect(formatted).toContain('"name": "Test"');
    expect(formatted).toContain('"value": 123');
  });

  it('应该能够导入常量', () => {
    expect(TEST_CONSTANTS.APP_NAME).toBe('Pennfly Private Academy');
    expect(TEST_CONSTANTS.VERSION).toBe('1.0.0');
    expect(TEST_CONSTANTS.ENVIRONMENT).toBe('test');
  });

  it('应该支持 TypeScript 类型推断', () => {
    // 验证 TypeScript 类型推断正确工作
    const message: string = getTestMessage();
    const constants: typeof TEST_CONSTANTS = TEST_CONSTANTS;

    expect(typeof message).toBe('string');
    expect(typeof constants.APP_NAME).toBe('string');
    expect(typeof constants.VERSION).toBe('string');
  });
});
