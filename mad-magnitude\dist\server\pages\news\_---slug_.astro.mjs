import { c as createAstro, a as createComponent, h as renderComponent, d as renderTemplate, m as maybeRenderHead, b as addAttribute } from "../../assets/vendor-astro.kctgsZae.js";
import { i } from "../../assets/vendor-astro.kctgsZae.js";
import "kleur/colors";
import { h as getEntry, e as getCollection, j as formatDate } from "../../assets/utils.CcA_tyNa.js";
import { $ as $$Layout } from "../../assets/Layout.BKd1ZXhO.js";
/* empty css                                    */
const $$Astro = createAstro("https://pennfly.com");
const prerender = false;
const $$ = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$;
  const { slug } = Astro2.params;
  if (!slug) {
    return Astro2.redirect("/news");
  }
  const entry = await getEntry("news", slug);
  if (!entry) {
    return Astro2.redirect("/404");
  }
  const { Content } = await entry.render();
  const allNews = await getCollection("news", ({ data }) => !data.draft);
  const relatedNews = allNews.filter(
    (news) => news.slug !== entry.slug && (news.data.type === entry.data.type || entry.data.relatedInstitute && news.data.relatedInstitute && entry.data.relatedInstitute.some(
      (institute) => news.data.relatedInstitute?.includes(institute)
    ))
  ).sort((a, b) => new Date(b.data.publishDate).getTime() - new Date(a.data.publishDate).getTime()).slice(0, 3);
  const breadcrumbs = [
    { label: "首页", href: "/" },
    { label: "动态资讯", href: "/news" },
    { label: entry.data.title.zh, href: `/news/${entry.slug}` }
  ];
  const typeConfig = {
    research: { icon: "🔬", label: "研究动态", color: "blue" },
    announcement: { icon: "📢", label: "重要公告", color: "purple" },
    reflection: { icon: "💭", label: "个人思考", color: "orange" },
    milestone: { icon: "🎯", label: "里程碑", color: "green" }
  };
  const currentType = typeConfig[entry.data.type] || typeConfig.research;
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": `${entry.data.title.zh} - 动态资讯 - Pennfly Private Academy`, "description": entry.data.description.zh, "data-astro-cid-cubnwgbf": true }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<article class="min-h-screen bg-gray-50" data-astro-cid-cubnwgbf> <!-- 面包屑导航 --> <nav class="border-b border-gray-200 bg-white py-4" data-astro-cid-cubnwgbf> <div class="container mx-auto px-6" data-astro-cid-cubnwgbf> <ol class="flex items-center space-x-2 text-sm text-gray-600" data-astro-cid-cubnwgbf> ${breadcrumbs.map((crumb, index) => renderTemplate`<li class="flex items-center" data-astro-cid-cubnwgbf> ${index > 0 && renderTemplate`<span class="mr-2 text-gray-400" data-astro-cid-cubnwgbf>/</span>`} ${index === breadcrumbs.length - 1 ? renderTemplate`<span class="font-medium text-gray-800" data-astro-cid-cubnwgbf>${crumb.label}</span>` : renderTemplate`<a${addAttribute(crumb.href, "href")} class="transition-colors hover:text-blue-600" data-astro-cid-cubnwgbf> ${crumb.label} </a>`} </li>`)} </ol> </div> </nav> <!-- 文章头部 --> <header class="bg-white py-12" data-astro-cid-cubnwgbf> <div class="container mx-auto px-6" data-astro-cid-cubnwgbf> <div class="mx-auto max-w-4xl" data-astro-cid-cubnwgbf> <!-- 类型标签 --> <div class="mb-6 flex items-center space-x-4" data-astro-cid-cubnwgbf> <span${addAttribute(`inline-flex items-center space-x-2 rounded-lg px-3 py-1 text-sm font-medium bg-${currentType.color}-100 text-${currentType.color}-800`, "class")} data-astro-cid-cubnwgbf> <span data-astro-cid-cubnwgbf>${currentType.icon}</span> <span data-astro-cid-cubnwgbf>${currentType.label}</span> </span> ${entry.data.featured && renderTemplate`<span class="inline-flex items-center space-x-1 rounded-lg bg-red-100 px-3 py-1 text-sm font-medium text-red-800" data-astro-cid-cubnwgbf> <span data-astro-cid-cubnwgbf>📌</span> <span data-astro-cid-cubnwgbf>置顶</span> </span>`} ${entry.data.relatedInstitute && entry.data.relatedInstitute.length > 0 && renderTemplate`<div class="flex items-center space-x-2" data-astro-cid-cubnwgbf> <span class="text-sm text-gray-600" data-astro-cid-cubnwgbf>相关研究所:</span> ${entry.data.relatedInstitute.map((institute) => renderTemplate`<a${addAttribute(`/${institute}`, "href")} class="inline-flex items-center space-x-1 rounded bg-gray-100 px-2 py-1 text-xs text-gray-700 transition-colors hover:bg-gray-200" data-astro-cid-cubnwgbf> <span data-astro-cid-cubnwgbf> ${institute === "economics" ? "💰" : institute === "philosophy" ? "🤔" : institute === "internet" ? "🌐" : institute === "ai" ? "🤖" : "🔮"} </span> <span data-astro-cid-cubnwgbf> ${institute === "economics" ? "经济研究所" : institute === "philosophy" ? "哲学研究所" : institute === "internet" ? "互联网研究所" : institute === "ai" ? "AI研究所" : "未来研究所"} </span> </a>`)} </div>`} </div> <!-- 标题 --> <h1 class="mb-6 text-4xl leading-tight font-bold text-gray-800" data-astro-cid-cubnwgbf> ${entry.data.title.zh} </h1> <!-- 摘要 --> ${entry.data.summary && renderTemplate`<p class="mb-6 text-xl leading-relaxed text-gray-600" data-astro-cid-cubnwgbf>${entry.data.summary}</p>`} <!-- 元信息 --> <div class="flex flex-wrap items-center gap-6 border-t border-gray-200 pt-6 text-sm text-gray-600" data-astro-cid-cubnwgbf> <div class="flex items-center space-x-2" data-astro-cid-cubnwgbf> <span data-astro-cid-cubnwgbf>📅</span> <span data-astro-cid-cubnwgbf>发布时间: ${formatDate(entry.data.publishDate)}</span> </div> ${entry.data.updateDate && renderTemplate`<div class="flex items-center space-x-2" data-astro-cid-cubnwgbf> <span data-astro-cid-cubnwgbf>🔄</span> <span data-astro-cid-cubnwgbf>更新时间: ${formatDate(entry.data.updateDate)}</span> </div>`} ${entry.data.author && renderTemplate`<div class="flex items-center space-x-2" data-astro-cid-cubnwgbf> <span data-astro-cid-cubnwgbf>👤</span> <span data-astro-cid-cubnwgbf>作者: ${entry.data.author}</span> </div>`} ${entry.data.readingTime && renderTemplate`<div class="flex items-center space-x-2" data-astro-cid-cubnwgbf> <span data-astro-cid-cubnwgbf>⏱️</span> <span data-astro-cid-cubnwgbf>阅读时间: ${entry.data.readingTime} 分钟</span> </div>`} </div> </div> </div> </header> <!-- 文章内容 --> <main class="py-12" data-astro-cid-cubnwgbf> <div class="container mx-auto px-6" data-astro-cid-cubnwgbf> <div class="mx-auto max-w-4xl" data-astro-cid-cubnwgbf> <div class="prose prose-lg prose-gray max-w-none" data-astro-cid-cubnwgbf> ${renderComponent($$result2, "Content", Content, { "data-astro-cid-cubnwgbf": true })} </div> </div> </div> </main> <!-- 标签 --> ${entry.data.tags && entry.data.tags.length > 0 && renderTemplate`<section class="bg-white py-8" data-astro-cid-cubnwgbf> <div class="container mx-auto px-6" data-astro-cid-cubnwgbf> <div class="mx-auto max-w-4xl" data-astro-cid-cubnwgbf> <h3 class="mb-4 text-lg font-semibold text-gray-800" data-astro-cid-cubnwgbf>相关标签</h3> <div class="flex flex-wrap gap-2" data-astro-cid-cubnwgbf> ${entry.data.tags.map((tag) => renderTemplate`<span class="cursor-pointer rounded-full bg-gray-100 px-4 py-2 text-sm text-gray-700 transition-colors hover:bg-gray-200" data-astro-cid-cubnwgbf>
#${tag} </span>`)} </div> </div> </div> </section>`} <!-- 相关动态 --> ${relatedNews.length > 0 && renderTemplate`<section class="bg-gray-50 py-12" data-astro-cid-cubnwgbf> <div class="container mx-auto px-6" data-astro-cid-cubnwgbf> <div class="mx-auto max-w-4xl" data-astro-cid-cubnwgbf> <h3 class="mb-8 text-2xl font-bold text-gray-800" data-astro-cid-cubnwgbf>相关动态</h3> <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3" data-astro-cid-cubnwgbf> ${relatedNews.map((news) => renderTemplate`<article class="rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition-shadow hover:shadow-md" data-astro-cid-cubnwgbf> <div class="mb-3 flex items-center" data-astro-cid-cubnwgbf> <span class="mr-2 text-lg" data-astro-cid-cubnwgbf> ${news.data.type === "research" ? "🔬" : news.data.type === "announcement" ? "📢" : news.data.type === "milestone" ? "🎯" : "💭"} </span> <span class="text-xs text-gray-600" data-astro-cid-cubnwgbf>${formatDate(news.data.publishDate)}</span> </div> <h4 class="mb-2 line-clamp-2 font-semibold text-gray-800" data-astro-cid-cubnwgbf> <a${addAttribute(`/news/${news.slug}`, "href")} class="transition-colors hover:text-blue-600" data-astro-cid-cubnwgbf> ${news.data.title.zh} </a> </h4> <p class="line-clamp-3 text-sm text-gray-600" data-astro-cid-cubnwgbf>${news.data.description.zh}</p> </article>`)} </div> </div> </div> </section>`} <!-- 导航按钮 --> <section class="border-t border-gray-200 bg-white py-8" data-astro-cid-cubnwgbf> <div class="container mx-auto px-6" data-astro-cid-cubnwgbf> <div class="mx-auto flex max-w-4xl items-center justify-between" data-astro-cid-cubnwgbf> <a href="/news" class="inline-flex items-center space-x-2 rounded-lg bg-gray-600 px-6 py-3 text-white transition-colors hover:bg-gray-700" data-astro-cid-cubnwgbf> <span data-astro-cid-cubnwgbf>←</span> <span data-astro-cid-cubnwgbf>返回动态列表</span> </a> <div class="flex items-center space-x-4" data-astro-cid-cubnwgbf> <button onclick="window.scrollTo({top: 0, behavior: 'smooth'})" class="rounded-lg bg-blue-600 px-6 py-3 text-white transition-colors hover:bg-blue-700" data-astro-cid-cubnwgbf>
回到顶部 ↑
</button> </div> </div> </div> </section> </article> ` })} `;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/news/[...slug].astro", void 0);
const $$file = "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/news/[...slug].astro";
const $$url = "/news/[...slug]";
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({ __proto__: null, default: $$, file: $$file, prerender, url: $$url }, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  i as renderers
};
