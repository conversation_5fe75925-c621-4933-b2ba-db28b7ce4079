# UI Components Library

Enhanced base components for Pennfly Private Academy with design system
integration.

## Components

### Button

Enhanced button component with multiple variants and states.

```astro
---
import But<PERSON> from '../components/ui/Button.astro';
---

<Button variant="primary" size="md">Click me</Button>
<Button variant="outline" loading={true}>Loading</Button>
<Button href="/link" target="_blank"><PERSON></Button>
```

**Props:**

- `variant`: 'primary' | 'secondary' | 'outline' | 'ghost' | 'link' |
  'destructive'
- `size`: 'sm' | 'md' | 'lg' | 'xl'
- `disabled`: boolean
- `loading`: boolean
- `fullWidth`: boolean
- `href`: string (makes it a link)
- `target`: '\_blank' | '\_self' | '\_parent' | '\_top'

### Card

Flexible container component with consistent styling.

```astro
---
import Card from '../components/ui/Card.astro';
---

<Card variant="elevated" padding="lg">
  <h3>Card Title</h3>
  <p>Card content goes here</p>
</Card>
```

**Props:**

- `variant`: 'default' | 'elevated' | 'outlined' | 'filled'
- `padding`: 'none' | 'sm' | 'md' | 'lg' | 'xl'
- `interactive`: boolean
- `href`: string (makes it clickable)

### Typography

Semantic typography component with consistent styling.

```astro
---
import Typography from '../components/ui/Typography.astro';
---

<Typography as="h1" variant="display">Display Heading</Typography>
<Typography as="p" variant="body" color="secondary">Body text</Typography>
```

**Props:**

- `as`: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span' | 'div' |
  'blockquote' | 'code' | 'pre'
- `variant`: 'display' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'body' |
  'lead' | 'small' | 'caption' | 'code' | 'quote'
- `weight`: 'thin' | 'light' | 'normal' | 'medium' | 'semibold' | 'bold' |
  'extrabold'
- `color`: 'primary' | 'secondary' | 'tertiary' | 'inverse' | 'brand' |
  'success' | 'warning' | 'error' | 'info'
- `family`: 'sans' | 'serif' | 'mono' | 'math'

### Badge

Labeling component for status indicators and tags.

```astro
---
import Badge from '../components/ui/Badge.astro';
---

<Badge variant="primary">Primary</Badge>
<Badge variant="success" removable={true}>Removable</Badge>
```

**Props:**

- `variant`: 'default' | 'primary' | 'secondary' | 'success' | 'warning' |
  'error' | 'info' | 'outline'
- `size`: 'sm' | 'md' | 'lg'
- `rounded`: boolean
- `removable`: boolean
- `interactive`: boolean

### Avatar

User avatar component with fallbacks and status indicators.

```astro
---
import Avatar from '../components/ui/Avatar.astro';
---

<Avatar name="John Doe" size="lg" status="online" />
<Avatar src="/avatar.jpg" alt="User avatar" />
```

**Props:**

- `src`: string (image URL)
- `alt`: string
- `name`: string (for initials fallback)
- `size`: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'
- `shape`: 'circle' | 'square' | 'rounded'
- `status`: 'online' | 'offline' | 'away' | 'busy'

### Icon

Consistent icon component with accessibility features.

```astro
---
import Icon from '../components/ui/Icon.astro';
---

<Icon name="search" size="md" color="brand" />
<Icon name="heart" interactive={true} aria-label="Like" />
```

**Props:**

- `name`: string (icon name from the icon set)
- `size`: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'
- `color`: 'current' | 'primary' | 'secondary' | 'tertiary' | 'brand' |
  'success' | 'warning' | 'error' | 'info'
- `interactive`: boolean
- `aria-label`: string (required for interactive icons)

## Available Icons

### Navigation

- chevron-left, chevron-right, chevron-up, chevron-down
- arrow-left, arrow-right, arrow-up, arrow-down

### Actions

- plus, minus, x, check, search, edit, trash, copy

### Interface

- menu, dots-vertical, dots-horizontal, cog

### Content

- document, folder, book, tag

### Social

- heart, star, share

### Status

- info, warning, error, success

### Theme

- sun, moon, computer

## Usage Guidelines

### Accessibility

- Always provide `aria-label` for interactive icons
- Use semantic HTML elements in Typography component
- Ensure proper color contrast with theme-aware colors
- Support keyboard navigation for interactive components

### Design System Integration

- All components use theme-aware CSS custom properties
- Consistent spacing and sizing scales
- Responsive design considerations
- Support for reduced motion preferences

### Performance

- Components are optimized for Astro's static generation
- Minimal JavaScript for interactive features
- Efficient CSS with design tokens
- Proper image optimization for avatars

## Testing

Visit `/ui-components-test` to see all components in action with various
configurations and combinations.
