---
/**
 * Enhanced responsive container component for Pennfly Private Academy
 * Provides consistent content containers with comprehensive responsive options
 */

export interface Props {
  /** Container max width */
  maxWidth?:
    | 'xs'
    | 'sm'
    | 'md'
    | 'lg'
    | 'xl'
    | '2xl'
    | '3xl'
    | '4xl'
    | '5xl'
    | '6xl'
    | '7xl'
    | 'full'
    | 'none'
    | 'screen-sm'
    | 'screen-md'
    | 'screen-lg'
    | 'screen-xl'
    | 'screen-2xl';

  /** Responsive max width for different breakpoints */
  smMaxWidth?:
    | 'xs'
    | 'sm'
    | 'md'
    | 'lg'
    | 'xl'
    | '2xl'
    | '3xl'
    | '4xl'
    | '5xl'
    | '6xl'
    | '7xl'
    | 'full'
    | 'none';
  mdMaxWidth?:
    | 'xs'
    | 'sm'
    | 'md'
    | 'lg'
    | 'xl'
    | '2xl'
    | '3xl'
    | '4xl'
    | '5xl'
    | '6xl'
    | '7xl'
    | 'full'
    | 'none';
  lgMaxWidth?:
    | 'xs'
    | 'sm'
    | 'md'
    | 'lg'
    | 'xl'
    | '2xl'
    | '3xl'
    | '4xl'
    | '5xl'
    | '6xl'
    | '7xl'
    | 'full'
    | 'none';
  xlMaxWidth?:
    | 'xs'
    | 'sm'
    | 'md'
    | 'lg'
    | 'xl'
    | '2xl'
    | '3xl'
    | '4xl'
    | '5xl'
    | '6xl'
    | '7xl'
    | 'full'
    | 'none';
  '2xlMaxWidth'?:
    | 'xs'
    | 'sm'
    | 'md'
    | 'lg'
    | 'xl'
    | '2xl'
    | '3xl'
    | '4xl'
    | '5xl'
    | '6xl'
    | '7xl'
    | 'full'
    | 'none';

  /** Horizontal padding */
  paddingX?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  /** Vertical padding */
  paddingY?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';

  /** Responsive horizontal padding */
  smPaddingX?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  mdPaddingX?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  lgPaddingX?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  xlPaddingX?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  '2xlPaddingX'?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';

  /** Responsive vertical padding */
  smPaddingY?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  mdPaddingY?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  lgPaddingY?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  xlPaddingY?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  '2xlPaddingY'?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';

  /** Center alignment */
  centered?: boolean;
  /** Fluid container (no max-width) */
  fluid?: boolean;
  /** Full height */
  fullHeight?: boolean;

  /** Custom CSS class */
  class?: string;
  /** HTML tag type */
  as?: 'div' | 'section' | 'article' | 'main' | 'aside' | 'header' | 'footer' | 'nav';
}

const {
  maxWidth = '7xl',
  smMaxWidth,
  mdMaxWidth,
  lgMaxWidth,
  xlMaxWidth,
  '2xlMaxWidth': xxlMaxWidth,

  paddingX = 'md',
  paddingY = 'none',
  smPaddingX,
  mdPaddingX,
  lgPaddingX,
  xlPaddingX,
  '2xlPaddingX': xxlPaddingX,
  smPaddingY,
  mdPaddingY,
  lgPaddingY,
  xlPaddingY,
  '2xlPaddingY': xxlPaddingY,

  centered = true,
  fluid = false,
  fullHeight = false,

  class: className = '',
  as: Tag = 'div',
} = Astro.props;

// Max width class mappings
const maxWidthClasses: Record<string, string> = {
  xs: 'max-w-xs', // 320px
  sm: 'max-w-sm', // 384px
  md: 'max-w-md', // 448px
  lg: 'max-w-lg', // 512px
  xl: 'max-w-xl', // 576px
  '2xl': 'max-w-2xl', // 672px
  '3xl': 'max-w-3xl', // 768px
  '4xl': 'max-w-4xl', // 896px
  '5xl': 'max-w-5xl', // 1024px
  '6xl': 'max-w-6xl', // 1152px
  '7xl': 'max-w-7xl', // 1280px
  full: 'max-w-full', // 100%
  none: 'max-w-none', // none
  'screen-sm': 'max-w-screen-sm', // 640px
  'screen-md': 'max-w-screen-md', // 768px
  'screen-lg': 'max-w-screen-lg', // 1024px
  'screen-xl': 'max-w-screen-xl', // 1280px
  'screen-2xl': 'max-w-screen-2xl', // 1536px
};

// Padding class mappings
const paddingXClasses: Record<string, string> = {
  none: '',
  xs: 'px-2',
  sm: 'px-3',
  md: 'px-4',
  lg: 'px-6',
  xl: 'px-8',
  '2xl': 'px-12',
};

const paddingYClasses: Record<string, string> = {
  none: '',
  xs: 'py-2',
  sm: 'py-3',
  md: 'py-4',
  lg: 'py-6',
  xl: 'py-8',
  '2xl': 'py-12',
};

// Build responsive max width classes
const responsiveMaxWidthClasses: string[] = [];
if (!fluid) {
  responsiveMaxWidthClasses.push(maxWidthClasses[maxWidth]);
  if (smMaxWidth) responsiveMaxWidthClasses.push(`sm:${maxWidthClasses[smMaxWidth]}`);
  if (mdMaxWidth) responsiveMaxWidthClasses.push(`md:${maxWidthClasses[mdMaxWidth]}`);
  if (lgMaxWidth) responsiveMaxWidthClasses.push(`lg:${maxWidthClasses[lgMaxWidth]}`);
  if (xlMaxWidth) responsiveMaxWidthClasses.push(`xl:${maxWidthClasses[xlMaxWidth]}`);
  if (xxlMaxWidth) responsiveMaxWidthClasses.push(`2xl:${maxWidthClasses[xxlMaxWidth]}`);
}

// Build responsive padding X classes
const responsivePaddingXClasses = [
  paddingXClasses[paddingX],
  smPaddingX ? `sm:${paddingXClasses[smPaddingX]}` : '',
  mdPaddingX ? `md:${paddingXClasses[mdPaddingX]}` : '',
  lgPaddingX ? `lg:${paddingXClasses[lgPaddingX]}` : '',
  xlPaddingX ? `xl:${paddingXClasses[xlPaddingX]}` : '',
  xxlPaddingX ? `2xl:${paddingXClasses[xxlPaddingX]}` : '',
].filter(Boolean);

// Build responsive padding Y classes
const responsivePaddingYClasses = [
  paddingYClasses[paddingY],
  smPaddingY ? `sm:${paddingYClasses[smPaddingY]}` : '',
  mdPaddingY ? `md:${paddingYClasses[mdPaddingY]}` : '',
  lgPaddingY ? `lg:${paddingYClasses[lgPaddingY]}` : '',
  xlPaddingY ? `xl:${paddingYClasses[xlPaddingY]}` : '',
  xxlPaddingY ? `2xl:${paddingYClasses[xxlPaddingY]}` : '',
].filter(Boolean);

// Combine all classes
const containerClasses = [
  'w-full',
  ...responsiveMaxWidthClasses,
  ...responsivePaddingXClasses,
  ...responsivePaddingYClasses,
  centered ? 'mx-auto' : '',
  fullHeight ? 'min-h-screen' : '',
  className,
]
  .filter(Boolean)
  .join(' ');
---

<Tag class={containerClasses}>
  <slot />
</Tag>

<style>
  /* Ensure container handles content overflow properly */
  [class*='max-w-'] {
    box-sizing: border-box;
  }

  /* Smooth transitions for responsive changes */
  [class*='max-w-'] {
    transition:
      max-width 0.3s ease-in-out,
      padding 0.3s ease-in-out;
  }

  /* Respect user's motion preferences */
  @media (prefers-reduced-motion: reduce) {
    [class*='max-w-'] {
      transition: none !important;
    }
  }

  /* Print styles */
  @media print {
    [class*='max-w-'] {
      max-width: none !important;
      padding-left: 0 !important;
      padding-right: 0 !important;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    [class*='max-w-'] {
      outline: 1px solid transparent;
    }
  }

  /* Ensure proper spacing on very small screens */
  @media (max-width: 374px) {
    [class*='px-'] {
      padding-left: 1rem !important;
      padding-right: 1rem !important;
    }
  }
</style>
