/**
 * 标签管理工具
 * 用于处理内容标签的统计、分类和管理
 */
import { getCollection } from 'astro:content';

export interface TagInfo {
  name: string;
  count: number;
  category: string;
  relatedTags: string[];
  color?: string;
}

export interface TagStats {
  totalTags: number;
  totalUniqueContent: number;
  mostPopularTags: TagInfo[];
  tagsByCategory: Record<string, TagInfo[]>;
  recentTags: string[];
}

export class TagManager {
  private tagCache: Map<string, TagInfo> = new Map();
  private lastUpdate: number = 0;
  private cacheTimeout = 5 * 60 * 1000; // 5分钟缓存

  /**
   * 获取所有标签统计信息
   */
  async getTagStats(): Promise<TagStats> {
    const now = Date.now();
    if (now - this.lastUpdate < this.cacheTimeout && this.tagCache.size > 0) {
      return this.buildStatsFromCache();
    }

    await this.refreshTagCache();
    return this.buildStatsFromCache();
  }

  /**
   * 刷新标签缓存
   */
  private async refreshTagCache(): Promise<void> {
    this.tagCache.clear();

    // 获取所有内容集合
    const collections = [
      'research',
      'news',
      'logs',
      'ai',
      'economics',
      'philosophy',
      'internet',
      'future',
      'products',
      'reflections',
    ];

    const tagCounts = new Map<string, number>();
    const tagCategories = new Map<string, string>();
    const tagRelations = new Map<string, Set<string>>();

    for (const collectionName of collections) {
      try {
        const collection = await getCollection(collectionName as any);
        for (const entry of collection) {
          const tags = entry.data.tags || [];

          // 统计标签使用次数
          for (const tag of tags) {
            tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);

            // 设置标签分类
            if (!tagCategories.has(tag)) {
              tagCategories.set(tag, this.categorizeTag(tag, collectionName));
            }

            // 建立标签关联关系
            if (!tagRelations.has(tag)) {
              tagRelations.set(tag, new Set());
            }

            // 添加同文章中的其他标签作为相关标签
            for (const relatedTag of tags) {
              if (relatedTag !== tag) {
                tagRelations.get(tag)!.add(relatedTag);
              }
            }
          }
        }
      } catch (error) {
        console.warn(`Failed to load collection ${collectionName}:`, error);
      }
    }

    // 构建标签信息
    for (const [tagName, count] of tagCounts) {
      const category = tagCategories.get(tagName) || 'other';
      const relatedTags = Array.from(tagRelations.get(tagName) || [])
        .sort((a, b) => (tagCounts.get(b) || 0) - (tagCounts.get(a) || 0))
        .slice(0, 5); // 取前5个相关标签

      this.tagCache.set(tagName, {
        name: tagName,
        count,
        category,
        relatedTags,
        color: this.getTagColor(category),
      });
    }

    this.lastUpdate = Date.now();
  }

  /**
   * 从缓存构建统计信息
   */
  private buildStatsFromCache(): TagStats {
    const allTags = Array.from(this.tagCache.values());
    const tagsByCategory: Record<string, TagInfo[]> = {};

    // 按分类组织标签
    for (const tag of allTags) {
      if (!tagsByCategory[tag.category]) {
        tagsByCategory[tag.category] = [];
      }
      tagsByCategory[tag.category].push(tag);
    }

    // 对每个分类的标签按使用次数排序
    for (const category in tagsByCategory) {
      tagsByCategory[category].sort((a, b) => b.count - a.count);
    }

    // 获取最受欢迎的标签
    const mostPopularTags = allTags.sort((a, b) => b.count - a.count).slice(0, 20);

    // 获取最近的标签（这里简化为最受欢迎的前10个）
    const recentTags = mostPopularTags.slice(0, 10).map(tag => tag.name);

    return {
      totalTags: allTags.length,
      totalUniqueContent: this.calculateUniqueContent(allTags),
      mostPopularTags,
      tagsByCategory,
      recentTags,
    };
  }

  /**
   * 根据标签名称和来源集合推断标签分类
   */
  private categorizeTag(tagName: string, collectionName: string): string {
    const tag = tagName.toLowerCase();

    // 技术相关
    if (
      tag.includes('ai') ||
      tag.includes('人工智能') ||
      tag.includes('机器学习') ||
      tag.includes('算法') ||
      tag.includes('技术') ||
      tag.includes('数字化')
    ) {
      return 'technology';
    }

    // 经济相关
    if (
      tag.includes('经济') ||
      tag.includes('市场') ||
      tag.includes('商业') ||
      tag.includes('金融') ||
      tag.includes('投资')
    ) {
      return 'economics';
    }

    // 哲学相关
    if (
      tag.includes('哲学') ||
      tag.includes('伦理') ||
      tag.includes('思考') ||
      tag.includes('价值观') ||
      tag.includes('道德')
    ) {
      return 'philosophy';
    }

    // 社会相关
    if (
      tag.includes('社会') ||
      tag.includes('文化') ||
      tag.includes('教育') ||
      tag.includes('政策') ||
      tag.includes('治理')
    ) {
      return 'society';
    }

    // 研究相关
    if (
      tag.includes('研究') ||
      tag.includes('分析') ||
      tag.includes('报告') ||
      tag.includes('框架') ||
      tag.includes('方法')
    ) {
      return 'research';
    }

    // 工具相关
    if (
      tag.includes('工具') ||
      tag.includes('平台') ||
      tag.includes('系统') ||
      tag.includes('助手') ||
      tag.includes('管理')
    ) {
      return 'tools';
    }

    // 根据集合名称分类
    switch (collectionName) {
      case 'ai':
        return 'technology';
      case 'economics':
        return 'economics';
      case 'philosophy':
        return 'philosophy';
      case 'products':
        return 'tools';
      case 'research':
        return 'research';
      default:
        return 'general';
    }
  }

  /**
   * 获取标签颜色
   */
  private getTagColor(category: string): string {
    const colors: Record<string, string> = {
      technology: '#3b82f6', // 蓝色
      economics: '#10b981', // 绿色
      philosophy: '#8b5cf6', // 紫色
      society: '#f59e0b', // 橙色
      research: '#ef4444', // 红色
      tools: '#6b7280', // 灰色
      general: '#64748b', // 石板色
    };
    return colors[category] || colors.general;
  }

  /**
   * 计算唯一内容数量
   */
  private calculateUniqueContent(tags: TagInfo[]): number {
    // 这里简化计算，实际应该统计有标签的内容总数
    return tags.reduce((sum, tag) => sum + tag.count, 0);
  }

  /**
   * 获取特定标签的详细信息
   */
  async getTagInfo(tagName: string): Promise<TagInfo | null> {
    if (this.tagCache.size === 0) {
      await this.refreshTagCache();
    }
    return this.tagCache.get(tagName) || null;
  }

  /**
   * 搜索标签
   */
  async searchTags(query: string): Promise<TagInfo[]> {
    if (this.tagCache.size === 0) {
      await this.refreshTagCache();
    }

    const searchTerm = query.toLowerCase();
    return Array.from(this.tagCache.values())
      .filter(tag => tag.name.toLowerCase().includes(searchTerm))
      .sort((a, b) => b.count - a.count);
  }

  /**
   * 获取标签建议（基于现有标签）
   */
  async getTagSuggestions(partialTag: string): Promise<string[]> {
    const searchResults = await this.searchTags(partialTag);
    return searchResults.slice(0, 10).map(tag => tag.name);
  }

  /**
   * 获取相关标签
   */
  async getRelatedTags(tagName: string): Promise<TagInfo[]> {
    const tagInfo = await this.getTagInfo(tagName);
    if (!tagInfo) return [];

    const relatedTagInfos: TagInfo[] = [];
    for (const relatedTagName of tagInfo.relatedTags) {
      const relatedInfo = await this.getTagInfo(relatedTagName);
      if (relatedInfo) {
        relatedTagInfos.push(relatedInfo);
      }
    }
    return relatedTagInfos;
  }
}

// 全局标签管理器实例
export const globalTagManager = new TagManager();

// 标签相关的工具函数
export function formatTagForUrl(tag: string): string {
  return encodeURIComponent(tag.toLowerCase().replace(/\s+/g, '-'));
}

export function parseTagFromUrl(urlTag: string): string {
  return decodeURIComponent(urlTag).replace(/-/g, ' ');
}

export function getTagDisplayName(tag: string): string {
  return tag.charAt(0).toUpperCase() + tag.slice(1);
}
