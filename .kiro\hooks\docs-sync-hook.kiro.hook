{"enabled": true, "name": "Documentation Sync", "description": "Listens to source files and configuration changes to automatically update project documentation in README and docs folder", "version": "1", "when": {"type": "userTriggered", "patterns": ["mad-magnitude/src/**/*.ts", "mad-magnitude/src/**/*.astro", "mad-magnitude/src/**/*.js", "mad-magnitude/src/**/*.jsx", "mad-magnitude/src/**/*.tsx", "mad-magnitude/*.config.js", "mad-magnitude/*.config.mjs", "mad-magnitude/*.config.ts", "mad-magnitude/package.json", "mad-magnitude/tsconfig.json", "*.md"]}, "then": {"type": "askAgent", "prompt": "Source files or configuration have been modified. Please review the changes and update the project documentation accordingly. Update the README.md file in the mad-magnitude folder and any relevant documentation in the docs folder to reflect the current state of the codebase, including any new features, API changes, configuration updates, or architectural modifications."}}