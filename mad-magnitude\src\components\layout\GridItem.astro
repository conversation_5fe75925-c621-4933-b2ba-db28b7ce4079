---
/**
 * 网格项目组件
 * 提供对单个网格项目的精细控制
 */

export interface Props {
  /** 跨越的列数 */
  colSpan?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 'full' | 'auto';
  /** 小屏幕跨越的列数 (sm: 640px+) */
  smColSpan?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 'full' | 'auto';
  /** 中等屏幕跨越的列数 (md: 768px+) */
  mdColSpan?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 'full' | 'auto';
  /** 大屏幕跨越的列数 (lg: 1024px+) */
  lgColSpan?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 'full' | 'auto';
  /** 超大屏幕跨越的列数 (xl: 1280px+) */
  xlColSpan?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 'full' | 'auto';
  /** 2XL屏幕跨越的列数 (2xl: 1536px+) */
  '2xlColSpan'?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 'full' | 'auto';
  /** 跨越的行数 */
  rowSpan?: 1 | 2 | 3 | 4 | 5 | 6 | 'full' | 'auto';
  /** 列起始位置 */
  colStart?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 13 | 'auto';
  /** 列结束位置 */
  colEnd?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 13 | 'auto';
  /** 行起始位置 */
  rowStart?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 'auto';
  /** 行结束位置 */
  rowEnd?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 'auto';
  /** 项目对齐方式 */
  justifySelf?: 'auto' | 'start' | 'end' | 'center' | 'stretch';
  /** 项目垂直对齐方式 */
  alignSelf?: 'auto' | 'start' | 'end' | 'center' | 'stretch';
  /** 自定义 CSS 类 */
  class?: string;
  /** HTML 标签类型 */
  as?: 'div' | 'section' | 'article' | 'li';
}

const {
  colSpan,
  smColSpan,
  mdColSpan,
  lgColSpan,
  xlColSpan,
  '2xlColSpan': xxlColSpan,
  rowSpan,
  colStart,
  colEnd,
  rowStart,
  rowEnd,
  justifySelf,
  alignSelf,
  class: className = '',
  as: Tag = 'div',
} = Astro.props;

// 列跨度类名映射
const colSpanClasses = {
  1: 'col-span-1',
  2: 'col-span-2',
  3: 'col-span-3',
  4: 'col-span-4',
  5: 'col-span-5',
  6: 'col-span-6',
  7: 'col-span-7',
  8: 'col-span-8',
  9: 'col-span-9',
  10: 'col-span-10',
  11: 'col-span-11',
  12: 'col-span-12',
  full: 'col-span-full',
  auto: 'col-auto',
};

// 行跨度类名映射
const rowSpanClasses = {
  1: 'row-span-1',
  2: 'row-span-2',
  3: 'row-span-3',
  4: 'row-span-4',
  5: 'row-span-5',
  6: 'row-span-6',
  full: 'row-span-full',
  auto: 'row-auto',
};

// 列起始位置类名映射
const colStartClasses = {
  1: 'col-start-1',
  2: 'col-start-2',
  3: 'col-start-3',
  4: 'col-start-4',
  5: 'col-start-5',
  6: 'col-start-6',
  7: 'col-start-7',
  8: 'col-start-8',
  9: 'col-start-9',
  10: 'col-start-10',
  11: 'col-start-11',
  12: 'col-start-12',
  13: 'col-start-13',
  auto: 'col-start-auto',
};

// 列结束位置类名映射
const colEndClasses = {
  1: 'col-end-1',
  2: 'col-end-2',
  3: 'col-end-3',
  4: 'col-end-4',
  5: 'col-end-5',
  6: 'col-end-6',
  7: 'col-end-7',
  8: 'col-end-8',
  9: 'col-end-9',
  10: 'col-end-10',
  11: 'col-end-11',
  12: 'col-end-12',
  13: 'col-end-13',
  auto: 'col-end-auto',
};

// 行起始位置类名映射
const rowStartClasses = {
  1: 'row-start-1',
  2: 'row-start-2',
  3: 'row-start-3',
  4: 'row-start-4',
  5: 'row-start-5',
  6: 'row-start-6',
  7: 'row-start-7',
  auto: 'row-start-auto',
};

// 行结束位置类名映射
const rowEndClasses = {
  1: 'row-end-1',
  2: 'row-end-2',
  3: 'row-end-3',
  4: 'row-end-4',
  5: 'row-end-5',
  6: 'row-end-6',
  7: 'row-end-7',
  auto: 'row-end-auto',
};

// 对齐类名映射
const justifySelfClasses = {
  auto: 'justify-self-auto',
  start: 'justify-self-start',
  end: 'justify-self-end',
  center: 'justify-self-center',
  stretch: 'justify-self-stretch',
};

const alignSelfClasses = {
  auto: 'self-auto',
  start: 'self-start',
  end: 'self-end',
  center: 'self-center',
  stretch: 'self-stretch',
};

// 构建响应式列跨度类名
const responsiveColSpanClasses = [
  colSpan ? colSpanClasses[colSpan] : '',
  smColSpan ? `sm:${colSpanClasses[smColSpan]}` : '',
  mdColSpan ? `md:${colSpanClasses[mdColSpan]}` : '',
  lgColSpan ? `lg:${colSpanClasses[lgColSpan]}` : '',
  xlColSpan ? `xl:${colSpanClasses[xlColSpan]}` : '',
  xxlColSpan ? `2xl:${colSpanClasses[xxlColSpan]}` : '',
].filter(Boolean);

// 组合所有类名
const itemClasses = [
  ...responsiveColSpanClasses,
  rowSpan ? rowSpanClasses[rowSpan] : '',
  colStart ? colStartClasses[colStart] : '',
  colEnd ? colEndClasses[colEnd] : '',
  rowStart ? rowStartClasses[rowStart] : '',
  rowEnd ? rowEndClasses[rowEnd] : '',
  justifySelf ? justifySelfClasses[justifySelf] : '',
  alignSelf ? alignSelfClasses[alignSelf] : '',
  className,
]
  .filter(Boolean)
  .join(' ');
---

<Tag class={itemClasses}>
  <slot />
</Tag>
