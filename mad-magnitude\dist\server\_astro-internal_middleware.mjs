import "es-module-lexer";
import { s as sequence } from "./assets/vendor-astro.kctgsZae.js";
import "kleur/colors";
import "clsx";
import "cookie";
import { a as getEnvConfig, c as createSecurityMiddleware, b as getSecurityHeaders } from "./assets/utils.CcA_tyNa.js";
function applySecurityHeaders(response) {
  const headers = getSecurityHeaders();
  Object.entries(headers).forEach(([key, value]) => {
    if (value && typeof value === "string") {
      response.headers.set(key, value);
    }
  });
  return response;
}
function validateRequestSecurity(request) {
  const errors = [];
  const config = getEnvConfig();
  const middleware = createSecurityMiddleware();
  if (!middleware.checkRateLimit(request)) {
    errors.push("Rate limit exceeded");
  }
  if (request.method === "POST" && !middleware.validateOrigin(request)) {
    errors.push("Invalid request origin");
  }
  if (request.method === "POST") {
    const contentType = request.headers.get("content-type");
    if (!contentType || !contentType.includes("application/json") && !contentType.includes("application/x-www-form-urlencoded") && !contentType.includes("multipart/form-data")) {
      errors.push("Invalid content type");
    }
  }
  const userAgent = request.headers.get("user-agent");
  if (!userAgent || userAgent.length < 10) {
    errors.push("Suspicious or missing user agent");
  }
  if (config.NODE_ENV === "production") {
    const requiredHeaders = ["accept", "accept-language"];
    requiredHeaders.forEach((header) => {
      if (!request.headers.get(header)) {
        errors.push(`Missing required header: ${header}`);
      }
    });
  }
  return {
    isValid: errors.length === 0,
    errors
  };
}
const securityMiddleware = async (context, next) => {
  const { request } = context;
  if (request.url.includes("/api/")) {
    const validation = validateRequestSecurity(request);
    if (!validation.isValid) {
      return new Response(
        JSON.stringify({
          error: "Security validation failed",
          details: validation.errors
        }),
        {
          status: 403,
          headers: {
            "Content-Type": "application/json"
          }
        }
      );
    }
  }
  const response = await next();
  return applySecurityHeaders(response);
};
const loggingMiddleware = async (context, next) => {
  const { request } = context;
  return next();
};
const onRequest$1 = sequence(
  securityMiddleware,
  loggingMiddleware
  // Add other middleware here as needed
);
const onRequest = sequence(
  onRequest$1
);
export {
  onRequest
};
