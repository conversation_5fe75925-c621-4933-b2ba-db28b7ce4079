# Pennfly Private Academy 安全配置文档

## 安全概述

Pennfly Private Academy 项目采用了多层次的安全策略，包括输入验证、输出编码、安全头设置、依赖检查和代码分析等。本文档详细说明了项目的安全配置和最佳实践。

## 已实施的安全措施

### 1. 输入验证和清理

项目实现了全面的输入验证和清理机制，防止XSS、注入和其他攻击：

- **HTML清理**: `sanitizeHtml()` 函数移除或转义危险HTML元素和属性
- **文本清理**: `sanitizeText()` 函数清理用户输入中的危险字符
- **表单验证**: `validateFormInput()` 提供全面的表单验证功能
- **URL和邮箱验证**: 专门的函数验证URL和邮箱格式

### 2. 安全HTTP头

项目配置了全面的安全HTTP头，增强浏览器安全性：

- **内容安全策略(CSP)**: 限制资源加载来源，防止XSS攻击
- **X-Content-Type-Options**: 防止MIME类型嗅探攻击
- **X-Frame-Options**: 防止点击劫持攻击
- **X-XSS-Protection**: 启用浏览器内置XSS过滤
- **Referrer-Policy**: 控制Referer头信息
- **Strict-Transport-Security**: 强制HTTPS连接
- **Permissions-Policy**: 限制浏览器API权限

### 3. 依赖安全

项目实现了多层次的依赖安全检查：

- **npm审计**: 定期运行`npm audit`检查已知漏洞
- **安全检查脚本**: 自定义脚本检测潜在安全问题
- **过时包检查**: 定期更新依赖以修复安全漏洞
- **ESLint安全规则**: 使用eslint-plugin-security检测不安全代码模式

### 4. 环境变量管理

项目采用安全的环境变量管理策略：

- **敏感信息隔离**: 敏感环境变量仅在服务端可用
- **环境验证**: 启动时验证必需的环境变量
- **类型安全**: TypeScript接口确保环境变量类型安全
- **默认值**: 为环境变量提供安全的默认值

### 5. 代码安全

项目采用多种代码安全措施：

- **TypeScript严格模式**: 启用所有严格类型检查选项
- **ESLint安全规则**: 检测不安全代码模式
- **代码审查流程**: Git hooks确保代码质量
- **安全测试**: 全面的单元测试覆盖安全功能

## 安全配置详情

### 安全工具函数

`src/utils/security.ts` 提供了核心安全功能：

```typescript
// HTML清理
export function sanitizeHtml(html: string): string

// 文本清理
export function sanitizeText(text: string): string

// 表单验证
export function validateFormInput(
  value: string,
  options: {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    type?: 'text' | 'email' | 'url';
  } = {}
): FormValidationResult

// 生成安全令牌
export function generateSecureToken(length: number = 32): string

// 获取安全头
export function getSecurityHeaders(): SecurityHeaders

// 创建安全中间件
export function createSecurityMiddleware()
```

### 安全测试

`src/test/security.test.ts` 包含全面的安全测试：

```typescript
describe('Security Utils', () => {
  describe('sanitizeHtml', () => {
    // 测试HTML清理功能
  });

  describe('sanitizeText', () => {
    // 测试文本清理功能
  });

  describe('Advanced XSS Protection', () => {
    // 测试复杂XSS攻击防护
  });

  // 更多安全测试...
});
```

### 安全检查脚本

`scripts/security-check.js` 提供自动化安全检查：

```javascript
// 检查易受攻击的包
function checkVulnerablePackages()

// 检查过时的包
function checkOutdatedPackages()

// 检查环境文件安全
function checkEnvironmentSecurity()

// 检查硬编码的秘密
function checkHardcodedSecrets()

// 检查不安全的URL
function checkInsecureUrls()

// 检查TypeScript安全配置
function checkTypeScriptSecurity()
```

## 安全最佳实践

### 开发阶段

1. **输入验证**: 始终验证和清理所有用户输入
2. **使用参数化查询**: 防止SQL注入
3. **避免eval()**: 不要使用eval()和类似的危险函数
4. **使用HTTPS**: 确保所有通信都通过加密通道
5. **最小权限原则**: 应用和用户只拥有必要的权限

### 部署阶段

1. **定期更新依赖**: 保持依赖项为最新版本
2. **安全头配置**: 确保所有安全头正确配置
3. **环境变量管理**: 不要将敏感信息提交到版本控制
4. **错误处理**: 不要向用户暴露敏感错误信息
5. **日志记录**: 记录安全相关事件，但不要记录敏感数据

## 安全检查清单

- [ ] 所有用户输入都经过验证和清理
- [ ] 使用了适当的安全HTTP头
- [ ] 没有硬编码的敏感信息
- [ ] 依赖项定期更新和审计
- [ ] 实现了适当的访问控制
- [ ] 有适当的错误处理机制
- [ ] 所有安全测试都通过
- [ ] 安全检查已集成到CI/CD流程

## 安全资源

- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [Mozilla Web Security](https://developer.mozilla.org/en-US/docs/Web/Security)
- [Node.js Security Checklist](https://github.com/nodejs/node/security/policy)
- [Astro Security Best Practices](https://docs.astro.build/en/guides/security/)
