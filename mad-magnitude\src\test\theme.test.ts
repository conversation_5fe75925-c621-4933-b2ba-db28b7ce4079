/**
 * Theme system tests for Pennfly Private Academy
 * Tests theme utilities and configuration
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  isValidThemeMode,
  isValidThemeVariant,
  getThemeConfig,
  getAllThemes,
  isDarkTheme,
} from '../utils/theme';
import { themeConfig, lightTheme, darkTheme, highContrastTheme } from '../styles/tokens/themes';

// Mock window and localStorage for testing
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

const mockMatchMedia = vi.fn();

beforeEach(() => {
  vi.clearAllMocks();

  // Mock window object
  Object.defineProperty(window, 'localStorage', {
    value: mockLocalStorage,
    writable: true,
  });

  Object.defineProperty(window, 'matchMedia', {
    value: mockMatchMedia,
    writable: true,
  });

  mockMatchMedia.mockReturnValue({
    matches: false,
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
  });
});

describe('Theme Configuration', () => {
  it('should have valid theme configuration', () => {
    expect(themeConfig).toBeDefined();
    expect(themeConfig.defaultTheme).toBe('light');
    expect(themeConfig.enableSystemTheme).toBe(true);
    expect(themeConfig.storageKey).toBe('pennfly-theme');
  });

  it('should have all required themes', () => {
    expect(themeConfig.themes.light).toBeDefined();
    expect(themeConfig.themes.dark).toBeDefined();
    expect(themeConfig.themes.highContrast).toBeDefined();
  });

  it('should have consistent theme structure', () => {
    const themes = [lightTheme, darkTheme, highContrastTheme];

    themes.forEach(theme => {
      expect(theme).toHaveProperty('name');
      expect(theme).toHaveProperty('mode');
      expect(theme).toHaveProperty('variant');
      expect(theme).toHaveProperty('colors');
      expect(theme).toHaveProperty('typography');
      expect(theme).toHaveProperty('spacing');
      expect(theme).toHaveProperty('borderRadius');
      expect(theme).toHaveProperty('shadows');
      expect(theme).toHaveProperty('animations');
    });
  });

  it('should have all required color properties', () => {
    const themes = [lightTheme, darkTheme, highContrastTheme];

    themes.forEach(theme => {
      expect(theme.colors).toHaveProperty('background');
      expect(theme.colors).toHaveProperty('foreground');
      expect(theme.colors).toHaveProperty('brand');
      expect(theme.colors).toHaveProperty('semantic');
      expect(theme.colors).toHaveProperty('interactive');
      expect(theme.colors).toHaveProperty('border');

      // Check nested properties
      expect(theme.colors.background).toHaveProperty('primary');
      expect(theme.colors.background).toHaveProperty('secondary');
      expect(theme.colors.background).toHaveProperty('tertiary');
      expect(theme.colors.background).toHaveProperty('elevated');
    });
  });
});

describe('Theme Utilities', () => {
  it('should validate theme modes correctly', () => {
    expect(isValidThemeMode('light')).toBe(true);
    expect(isValidThemeMode('dark')).toBe(true);
    expect(isValidThemeMode('auto')).toBe(true);
    expect(isValidThemeMode('invalid')).toBe(false);
    expect(isValidThemeMode('')).toBe(false);
  });

  it('should validate theme variants correctly', () => {
    expect(isValidThemeVariant('default')).toBe(true);
    expect(isValidThemeVariant('high-contrast')).toBe(true);
    expect(isValidThemeVariant('invalid')).toBe(false);
    expect(isValidThemeVariant('')).toBe(false);
  });

  it('should get theme config by name', () => {
    expect(getThemeConfig('light')).toEqual(lightTheme);
    expect(getThemeConfig('dark')).toEqual(darkTheme);
    expect(getThemeConfig('highContrast')).toEqual(highContrastTheme);
  });

  it('should get all themes', () => {
    const allThemes = getAllThemes();
    expect(allThemes).toHaveProperty('light');
    expect(allThemes).toHaveProperty('dark');
    expect(allThemes).toHaveProperty('highContrast');
  });

  it('should detect dark theme correctly', () => {
    expect(isDarkTheme('dark')).toBe(true);
    expect(isDarkTheme('light')).toBe(false);
  });
});

describe('Theme Colors', () => {
  it('should have valid color values', () => {
    const themes = [lightTheme, darkTheme, highContrastTheme];

    themes.forEach(theme => {
      // Check that all color values are valid CSS colors (hex format)
      const colorValues = [
        theme.colors.background.primary,
        theme.colors.background.secondary,
        theme.colors.foreground.primary,
        theme.colors.brand.primary,
        theme.colors.semantic.success,
        theme.colors.interactive.default,
        theme.colors.border.default,
      ];

      colorValues.forEach(color => {
        expect(color).toMatch(/^#[0-9a-fA-F]{6}$/);
      });
    });
  });

  it('should have different colors for light and dark themes', () => {
    expect(lightTheme.colors.background.primary).not.toBe(darkTheme.colors.background.primary);
    expect(lightTheme.colors.foreground.primary).not.toBe(darkTheme.colors.foreground.primary);
  });

  it('should have high contrast colors for accessibility', () => {
    // High contrast theme should have more extreme color differences
    expect(highContrastTheme.colors.background.primary).toBe('#000000');
    expect(highContrastTheme.colors.foreground.primary).toBe('#ffffff');
  });
});

describe('Theme Typography', () => {
  it('should have consistent typography across themes', () => {
    const themes = [lightTheme, darkTheme, highContrastTheme];

    themes.forEach(theme => {
      expect(theme.typography.fontFamily).toHaveProperty('sans');
      expect(theme.typography.fontFamily).toHaveProperty('serif');
      expect(theme.typography.fontFamily).toHaveProperty('mono');
      expect(theme.typography.fontFamily).toHaveProperty('math');

      expect(theme.typography.fontSize).toHaveProperty('base');
      expect(theme.typography.fontSize).toHaveProperty('lg');
      expect(theme.typography.fontSize).toHaveProperty('xl');

      expect(theme.typography.fontWeight).toHaveProperty('normal');
      expect(theme.typography.fontWeight).toHaveProperty('medium');
      expect(theme.typography.fontWeight).toHaveProperty('bold');
    });
  });

  it('should have valid font size values', () => {
    const fontSize = lightTheme.typography.fontSize;

    Object.values(fontSize).forEach(([size, config]) => {
      expect(size).toMatch(/^\d+(\.\d+)?(rem|px)$/);
      expect(config).toHaveProperty('lineHeight');
    });
  });
});

describe('Theme Animations', () => {
  it('should have consistent animation settings', () => {
    const themes = [lightTheme, darkTheme, highContrastTheme];

    themes.forEach(theme => {
      expect(theme.animations.duration).toHaveProperty('fast');
      expect(theme.animations.duration).toHaveProperty('normal');
      expect(theme.animations.duration).toHaveProperty('slow');

      expect(theme.animations.easing).toHaveProperty('linear');
      expect(theme.animations.easing).toHaveProperty('ease');
      expect(theme.animations.easing).toHaveProperty('easeIn');
      expect(theme.animations.easing).toHaveProperty('easeOut');
      expect(theme.animations.easing).toHaveProperty('easeInOut');
    });
  });

  it('should have valid duration values', () => {
    const durations = lightTheme.animations.duration;

    Object.values(durations).forEach(duration => {
      expect(duration).toMatch(/^\d+ms$/);
    });
  });
});
