import { e as createCollectionToGlobResultMap, f as createGetCollection, g as createGetEntry } from "./vendor-astro.kctgsZae.js";
const __vite_import_meta_env__ = { "ASSETS_PREFIX": void 0, "BASE_URL": "/", "DEV": false, "MODE": "production", "PROD": true, "SITE": "https://pennfly.com", "SSR": true };
const defaultConfig = {
  SITE_URL: "https://pennfly.com",
  SITE_TITLE: "Pennfly Private Academy",
  SITE_DESCRIPTION: "个人化私人研究院 · 跨领域学术探索 · 思想与创意的交流平台",
  NODE_ENV: "development",
  DEBUG: false,
  BUILD_ANALYZE: false,
  BUILD_SOURCEMAP: true,
  DEV_PORT: 4321,
  DEV_HOST: "localhost",
  DEV_OPEN_BROWSER: true,
  CSP_ENABLED: true,
  CSP_REPORT_ONLY: false,
  CORS_ORIGIN: "*",
  CORS_CREDENTIALS: false,
  FEATURE_COMMENTS: false,
  FEATURE_SEARCH: false,
  FEATURE_ANALYTICS: false,
  FEATURE_NEWSLETTER: false
};
function parseBoolean(value, fallback) {
  if (!value) return fallback;
  return value.toLowerCase() === "true";
}
function parseNumber(value, fallback) {
  if (!value) return fallback;
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? fallback : parsed;
}
function getEnvConfig() {
  const env = Object.assign(__vite_import_meta_env__, { NODE: process.env.NODE, NODE_ENV: process.env.NODE_ENV, OS: process.env.OS });
  return {
    SITE_URL: env.SITE_URL || defaultConfig.SITE_URL,
    SITE_TITLE: env.SITE_TITLE || defaultConfig.SITE_TITLE,
    SITE_DESCRIPTION: env.SITE_DESCRIPTION || defaultConfig.SITE_DESCRIPTION,
    NODE_ENV: env.NODE_ENV || defaultConfig.NODE_ENV,
    DEBUG: parseBoolean(env.DEBUG, defaultConfig.DEBUG),
    BUILD_ANALYZE: parseBoolean(env.BUILD_ANALYZE, defaultConfig.BUILD_ANALYZE),
    BUILD_SOURCEMAP: parseBoolean(env.BUILD_SOURCEMAP, defaultConfig.BUILD_SOURCEMAP),
    DEV_PORT: parseNumber(env.DEV_PORT, defaultConfig.DEV_PORT),
    DEV_HOST: env.DEV_HOST || defaultConfig.DEV_HOST,
    DEV_OPEN_BROWSER: parseBoolean(env.DEV_OPEN_BROWSER, defaultConfig.DEV_OPEN_BROWSER),
    CSP_ENABLED: parseBoolean(env.CSP_ENABLED, defaultConfig.CSP_ENABLED),
    CSP_REPORT_ONLY: parseBoolean(env.CSP_REPORT_ONLY, defaultConfig.CSP_REPORT_ONLY),
    CORS_ORIGIN: env.CORS_ORIGIN || defaultConfig.CORS_ORIGIN,
    CORS_CREDENTIALS: parseBoolean(env.CORS_CREDENTIALS, defaultConfig.CORS_CREDENTIALS),
    FEATURE_COMMENTS: parseBoolean(env.FEATURE_COMMENTS, defaultConfig.FEATURE_COMMENTS),
    FEATURE_SEARCH: parseBoolean(env.FEATURE_SEARCH, defaultConfig.FEATURE_SEARCH),
    FEATURE_ANALYTICS: parseBoolean(env.FEATURE_ANALYTICS, defaultConfig.FEATURE_ANALYTICS),
    FEATURE_NEWSLETTER: parseBoolean(env.FEATURE_NEWSLETTER, defaultConfig.FEATURE_NEWSLETTER)
  };
}
function getSecurityHeaders() {
  const config = getEnvConfig();
  const isDev = config.NODE_ENV === "development";
  const headers = {
    "X-Content-Type-Options": "nosniff",
    "X-Frame-Options": "DENY",
    "X-XSS-Protection": "1; mode=block",
    "Referrer-Policy": "strict-origin-when-cross-origin"
  };
  if (config.CSP_ENABLED) {
    const cspDirectives = [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline'",
      // Allow inline scripts for Astro
      "style-src 'self' 'unsafe-inline'",
      // Allow inline styles for Tailwind
      "img-src 'self' data: https:",
      "font-src 'self' data:",
      "connect-src 'self'",
      "media-src 'self'",
      "object-src 'none'",
      "child-src 'none'",
      "worker-src 'self'",
      "frame-ancestors 'none'",
      "form-action 'self'",
      "base-uri 'self'",
      "manifest-src 'self'"
    ];
    if (isDev) {
      cspDirectives.push("connect-src 'self' ws: wss:");
    }
    const cspValue = cspDirectives.join("; ");
    if (config.CSP_REPORT_ONLY) {
      headers["Content-Security-Policy-Report-Only"] = cspValue;
    } else {
      headers["Content-Security-Policy"] = cspValue;
    }
  }
  if (!isDev) {
    headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains; preload";
  }
  headers["Permissions-Policy"] = [
    "camera=()",
    "microphone=()",
    "geolocation=()",
    "interest-cohort=()",
    "payment=()",
    "usb=()"
  ].join(", ");
  return headers;
}
class RateLimiter {
  constructor(maxRequests = 100, windowMs = 15 * 60 * 1e3) {
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
  }
  requests = /* @__PURE__ */ new Map();
  isAllowed(identifier) {
    const now = Date.now();
    const windowStart = now - this.windowMs;
    const requests = this.requests.get(identifier) || [];
    const recentRequests = requests.filter((time) => time > windowStart);
    if (recentRequests.length >= this.maxRequests) {
      return false;
    }
    recentRequests.push(now);
    this.requests.set(identifier, recentRequests);
    return true;
  }
  reset(identifier) {
    this.requests.delete(identifier);
  }
  cleanup() {
    const now = Date.now();
    const windowStart = now - this.windowMs;
    for (const [identifier, requests] of this.requests.entries()) {
      const recentRequests = requests.filter((time) => time > windowStart);
      if (recentRequests.length === 0) {
        this.requests.delete(identifier);
      } else {
        this.requests.set(identifier, recentRequests);
      }
    }
  }
}
const rateLimiter = new RateLimiter();
if (typeof setInterval !== "undefined") {
  setInterval(() => {
    rateLimiter.cleanup();
  }, 5 * 60 * 1e3);
}
function createSecurityMiddleware() {
  return {
    /**
     * Apply security headers to response
     */
    applyHeaders: (response) => {
      const headers = getSecurityHeaders();
      Object.entries(headers).forEach(([key, value]) => {
        if (value && typeof value === "string") {
          response.headers.set(key, value);
        }
      });
      return response;
    },
    /**
     * Validate request rate limiting
     */
    checkRateLimit: (request) => {
      const clientIP = request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip") || "unknown";
      return rateLimiter.isAllowed(clientIP);
    },
    /**
     * Validate request origin
     */
    validateOrigin: (request) => {
      const origin = request.headers.get("origin");
      const referer = request.headers.get("referer");
      const config = getEnvConfig();
      if (!origin && !referer) {
        return false;
      }
      const allowedOrigins = [config.SITE_URL];
      if (config.NODE_ENV === "development") {
        allowedOrigins.push(`http://${config.DEV_HOST}:${config.DEV_PORT}`);
        allowedOrigins.push("http://localhost:4321");
      }
      if (origin) {
        return allowedOrigins.some((allowed) => origin.startsWith(allowed));
      }
      if (referer) {
        return allowedOrigins.some((allowed) => referer.startsWith(allowed));
      }
      return false;
    }
  };
}
const liveCollections = {};
const contentDir = "/src/content/";
const contentEntryGlob = "";
const contentCollectionToEntryMap = createCollectionToGlobResultMap({
  globResult: contentEntryGlob,
  contentDir
});
const dataEntryGlob = "";
const dataCollectionToEntryMap = createCollectionToGlobResultMap({
  globResult: dataEntryGlob,
  contentDir
});
const collectionToEntryMap = createCollectionToGlobResultMap({
  globResult: { ...contentEntryGlob, ...dataEntryGlob },
  contentDir
});
let lookupMap = {};
lookupMap = {};
const collectionNames = new Set(Object.keys(lookupMap));
function createGlobLookup(glob) {
  return async (collection, lookupId) => {
    const filePath = lookupMap[collection]?.entries[lookupId];
    if (!filePath) return void 0;
    return glob[collection][filePath];
  };
}
const renderEntryGlob = "";
const collectionToRenderEntryMap = createCollectionToGlobResultMap({
  globResult: renderEntryGlob,
  contentDir
});
const cacheEntriesByCollection = /* @__PURE__ */ new Map();
const getCollection = createGetCollection({
  contentCollectionToEntryMap,
  dataCollectionToEntryMap,
  getRenderEntryImport: createGlobLookup(collectionToRenderEntryMap),
  cacheEntriesByCollection,
  liveCollections
});
const getEntry = createGetEntry({
  getEntryImport: createGlobLookup(collectionToEntryMap),
  getRenderEntryImport: createGlobLookup(collectionToRenderEntryMap),
  collectionNames,
  liveCollections
});
class ContentManager {
  static instance;
  contentCache = /* @__PURE__ */ new Map();
  lastCacheUpdate = 0;
  cacheTimeout = 5 * 60 * 1e3;
  // 5分钟缓存
  static getInstance() {
    if (!ContentManager.instance) {
      ContentManager.instance = new ContentManager();
    }
    return ContentManager.instance;
  }
  /**
   * 获取所有内容列表
   */
  async getAllContent(filter) {
    try {
      await this.refreshCacheIfNeeded();
      let items = Array.from(this.contentCache.values());
      if (filter) {
        items = this.applyFilter(items, filter);
      }
      return items.sort((a, b) => {
        const dateA = a.updateDate || a.publishDate;
        const dateB = b.updateDate || b.publishDate;
        return dateB.getTime() - dateA.getTime();
      });
    } catch (error) {
      console.error("Error getting all content:", error);
      return [];
    }
  }
  /**
   * 根据ID获取单个内容
   */
  async getContentById(id) {
    await this.refreshCacheIfNeeded();
    return this.contentCache.get(id) || null;
  }
  /**
   * 创建新内容（简化版本，实际文件操作需要通过API）
   */
  async createContent(collection, slug, frontmatter, content) {
    const now = /* @__PURE__ */ new Date();
    const fullFrontmatter = {
      title: frontmatter.title || "Untitled",
      description: frontmatter.description || "",
      publishDate: frontmatter.publishDate || now,
      updateDate: now,
      draft: frontmatter.draft !== void 0 ? frontmatter.draft : true,
      featured: frontmatter.featured || false,
      tags: frontmatter.tags || [],
      author: frontmatter.author || "Pennfly",
      ...frontmatter
    };
    const contentItem = {
      id: `${collection}/${slug}`,
      collection,
      slug,
      title: fullFrontmatter.title,
      description: fullFrontmatter.description,
      publishDate: new Date(fullFrontmatter.publishDate),
      updateDate: new Date(fullFrontmatter.updateDate),
      draft: fullFrontmatter.draft,
      featured: fullFrontmatter.featured,
      tags: fullFrontmatter.tags,
      author: fullFrontmatter.author,
      content,
      filePath: `src/content/${collection}/${slug}.md`
    };
    this.contentCache.set(contentItem.id, contentItem);
    return contentItem;
  }
  /**
   * 更新内容（简化版本，实际文件操作需要通过API）
   */
  async updateContent(id, frontmatter, content) {
    const existingItem = await this.getContentById(id);
    if (!existingItem) {
      return null;
    }
    const updatedFrontmatter = {
      ...frontmatter,
      updateDate: /* @__PURE__ */ new Date()
    };
    const updatedItem = {
      ...existingItem,
      title: updatedFrontmatter.title || existingItem.title,
      description: updatedFrontmatter.description || existingItem.description,
      updateDate: updatedFrontmatter.updateDate,
      draft: updatedFrontmatter.draft !== void 0 ? updatedFrontmatter.draft : existingItem.draft,
      featured: updatedFrontmatter.featured !== void 0 ? updatedFrontmatter.featured : existingItem.featured,
      tags: updatedFrontmatter.tags || existingItem.tags,
      author: updatedFrontmatter.author || existingItem.author,
      content
    };
    this.contentCache.set(id, updatedItem);
    return updatedItem;
  }
  /**
   * 删除内容（简化版本，实际文件操作需要通过API）
   */
  async deleteContent(id) {
    const item = await this.getContentById(id);
    if (!item) {
      return false;
    }
    try {
      this.contentCache.delete(id);
      return true;
    } catch (error) {
      console.error("删除内容失败:", error);
      return false;
    }
  }
  /**
   * 获取内容统计信息
   */
  async getContentStats() {
    await this.refreshCacheIfNeeded();
    const items = Array.from(this.contentCache.values());
    const published = items.filter((item) => !item.draft);
    const drafts = items.filter((item) => item.draft);
    const featured = items.filter((item) => item.featured);
    const byCollection = {};
    items.forEach((item) => {
      byCollection[item.collection] = (byCollection[item.collection] || 0) + 1;
    });
    const byAuthor = {};
    items.forEach((item) => {
      byAuthor[item.author] = (byAuthor[item.author] || 0) + 1;
    });
    const recentActivity = items.sort((a, b) => {
      const dateA = a.updateDate || a.publishDate;
      const dateB = b.updateDate || b.publishDate;
      return dateB.getTime() - dateA.getTime();
    }).slice(0, 10).map((item) => ({
      action: item.updateDate ? "更新" : "创建",
      content: item.title,
      date: item.updateDate || item.publishDate
    }));
    return {
      total: items.length,
      published: published.length,
      drafts: drafts.length,
      featured: featured.length,
      byCollection,
      byAuthor,
      recentActivity
    };
  }
  /**
   * 搜索内容
   */
  async searchContent(query) {
    await this.refreshCacheIfNeeded();
    const searchTerm = query.toLowerCase();
    const items = Array.from(this.contentCache.values());
    return items.filter((item) => {
      return item.title.toLowerCase().includes(searchTerm) || item.description?.toLowerCase().includes(searchTerm) || item.content.toLowerCase().includes(searchTerm) || item.tags.some((tag) => tag.toLowerCase().includes(searchTerm)) || item.author.toLowerCase().includes(searchTerm);
    });
  }
  /**
   * 刷新缓存（如果需要）
   */
  async refreshCacheIfNeeded() {
    const now = Date.now();
    if (now - this.lastCacheUpdate < this.cacheTimeout && this.contentCache.size > 0) {
      return;
    }
    await this.refreshCache();
  }
  /**
   * 刷新缓存
   */
  async refreshCache() {
    this.contentCache.clear();
    const collections = [
      "news",
      "logs",
      "research",
      "reflections",
      "economics",
      "philosophy",
      "internet",
      "ai",
      "future",
      "products"
    ];
    for (const collectionName of collections) {
      try {
        const collection = await getCollection(collectionName);
        for (const entry of collection) {
          const contentItem = {
            id: `${collectionName}/${entry.slug}`,
            collection: collectionName,
            slug: entry.slug,
            title: this.extractTitle(entry.data),
            description: this.extractDescription(entry.data),
            publishDate: new Date(entry.data.publishDate || entry.data.date || Date.now()),
            updateDate: entry.data.updateDate ? new Date(entry.data.updateDate) : void 0,
            draft: entry.data.draft || false,
            featured: entry.data.featured || false,
            tags: entry.data.tags || [],
            author: entry.data.author || "Pennfly",
            content: "",
            // Content body would need to be rendered separately
            filePath: `src/content/${collectionName}/${entry.slug}.md`
          };
          this.contentCache.set(contentItem.id, contentItem);
        }
      } catch (error) {
        console.warn(`Failed to load collection ${collectionName}:`, error);
      }
    }
    this.lastCacheUpdate = Date.now();
  }
  /**
   * 提取标题（处理多语言格式）
   */
  extractTitle(data) {
    if (typeof data.title === "string") {
      return data.title;
    }
    if (typeof data.title === "object" && data.title.zh) {
      return data.title.zh;
    }
    return "Untitled";
  }
  /**
   * 提取描述（处理多语言格式）
   */
  extractDescription(data) {
    if (typeof data.description === "string") {
      return data.description;
    }
    if (typeof data.description === "object" && data.description.zh) {
      return data.description.zh;
    }
    return void 0;
  }
  /**
   * 应用筛选器
   */
  applyFilter(items, filter) {
    return items.filter((item) => {
      if (filter.collection && item.collection !== filter.collection) {
        return false;
      }
      if (filter.draft !== void 0 && item.draft !== filter.draft) {
        return false;
      }
      if (filter.featured !== void 0 && item.featured !== filter.featured) {
        return false;
      }
      if (filter.tags && filter.tags.length > 0) {
        const hasMatchingTag = filter.tags.some((tag) => item.tags.includes(tag));
        if (!hasMatchingTag) {
          return false;
        }
      }
      if (filter.author && item.author !== filter.author) {
        return false;
      }
      if (filter.dateRange) {
        const itemDate = item.updateDate || item.publishDate;
        if (itemDate < filter.dateRange.start || itemDate > filter.dateRange.end) {
          return false;
        }
      }
      if (filter.search) {
        const searchTerm = filter.search.toLowerCase();
        const matchesSearch = item.title.toLowerCase().includes(searchTerm) || item.description?.toLowerCase().includes(searchTerm) || item.content.toLowerCase().includes(searchTerm) || item.tags.some((tag) => tag.toLowerCase().includes(searchTerm));
        if (!matchesSearch) {
          return false;
        }
      }
      return true;
    });
  }
  /**
   * 生成 Markdown 文件内容
   */
  generateMarkdownFile(frontmatter, content) {
    const yamlFrontmatter = Object.entries(frontmatter).map(([key, value]) => {
      if (value instanceof Date) {
        return `${key}: ${value.toISOString()}`;
      } else if (Array.isArray(value)) {
        return `${key}: [${value.map((v) => `'${v}'`).join(", ")}]`;
      } else if (typeof value === "string") {
        return `${key}: '${value.replace(/'/g, "''")}'`;
      } else {
        return `${key}: ${value}`;
      }
    }).join("\n");
    return `---
${yamlFrontmatter}
---

${content}`;
  }
  /**
   * 解析 Markdown 文件
   */
  parseMarkdownFile(fileContent) {
    const frontmatterRegex = /^---\n([\s\S]*?)\n---\n([\s\S]*)$/;
    const match = fileContent.match(frontmatterRegex);
    if (!match) {
      return { frontmatter: {}, content: fileContent };
    }
    const [, frontmatterStr, content] = match;
    const frontmatter = {};
    const lines = frontmatterStr.split("\n");
    for (const line of lines) {
      const colonIndex = line.indexOf(":");
      if (colonIndex === -1) continue;
      const key = line.substring(0, colonIndex).trim();
      const value = line.substring(colonIndex + 1).trim();
      if (value.startsWith("[") && value.endsWith("]")) {
        const arrayContent = value.slice(1, -1);
        frontmatter[key] = arrayContent.split(",").map((item) => item.trim().replace(/^'|'$/g, ""));
      } else if (value === "true" || value === "false") {
        frontmatter[key] = value === "true";
      } else if (!isNaN(Number(value))) {
        frontmatter[key] = Number(value);
      } else if (value.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)) {
        frontmatter[key] = new Date(value);
      } else {
        frontmatter[key] = value.replace(/^'|'$/g, "");
      }
    }
    return { frontmatter, content: content.trim() };
  }
}
const contentManager = ContentManager.getInstance();
const instituteConfigs = {
  economics: {
    id: "economics",
    name: "经济研究所",
    nameEn: "Economics Research Institute",
    icon: "💰",
    description: "专注于经济分析、市场洞察和政策解读。从个人视角探讨经济现象，分析市场趋势，解读政策影响，为理解复杂的经济世界提供独特的观点和思考。",
    color: "text-green-600",
    gradientFrom: "from-green-500",
    gradientTo: "to-emerald-600",
    fields: [
      { name: "市场分析", description: "股市、债市、商品市场的趋势分析" },
      { name: "政策解读", description: "货币政策、财政政策的影响分析" },
      { name: "经济理论", description: "经济学理论的现实应用和思考" },
      { name: "数据分析", description: "经济数据的深度挖掘和解读" }
    ]
  },
  philosophy: {
    id: "philosophy",
    name: "哲学研究所",
    nameEn: "Philosophy Research Institute",
    icon: "🤔",
    description: "探索人类思维的深层问题，从古典哲学到现代思辨，从形而上学到伦理学，致力于通过哲学思考来理解世界、人生和价值的本质。",
    color: "text-amber-600",
    gradientFrom: "from-amber-500",
    gradientTo: "to-orange-600",
    fields: [
      { name: "伦理学", description: "道德哲学和价值判断的研究" },
      { name: "形而上学", description: "存在、现实和本质的哲学探讨" },
      { name: "认识论", description: "知识、真理和信念的哲学分析" },
      { name: "逻辑学", description: "推理和论证的形式化研究" },
      { name: "美学", description: "艺术、美和审美体验的哲学思考" }
    ]
  },
  internet: {
    id: "internet",
    name: "互联网研究所",
    nameEn: "Internet Research Institute",
    icon: "🌐",
    description: "深入研究互联网行业的发展趋势、商业模式和技术创新。关注数字化转型、平台经济、网络文化等现象，探索互联网对社会的深远影响。",
    color: "text-blue-600",
    gradientFrom: "from-blue-500",
    gradientTo: "to-cyan-600",
    fields: [
      { name: "平台经济", description: "互联网平台的商业模式和生态研究" },
      { name: "数字化转型", description: "传统行业的数字化升级路径" },
      { name: "网络文化", description: "互联网时代的文化现象和社会影响" },
      { name: "技术创新", description: "互联网技术的发展趋势和应用" },
      { name: "用户行为", description: "网络用户的行为模式和心理分析" }
    ]
  },
  ai: {
    id: "ai",
    name: "人工智能研究所",
    nameEn: "Artificial Intelligence Research Institute",
    icon: "🤖",
    description: "专注于人工智能技术研究、应用思考和伦理探讨。从技术原理到实际应用，从算法创新到社会影响，全方位探索AI技术的发展轨迹和未来可能。",
    color: "text-purple-600",
    gradientFrom: "from-purple-500",
    gradientTo: "to-indigo-600",
    fields: [
      { name: "机器学习", description: "深度学习、强化学习等算法研究" },
      { name: "自然语言处理", description: "大语言模型、对话系统等技术" },
      { name: "计算机视觉", description: "图像识别、生成模型等应用" },
      { name: "AI伦理", description: "人工智能的伦理问题和治理" },
      { name: "应用场景", description: "AI在各行业的实际应用案例" },
      { name: "通用人工智能", description: "AGI的发展路径和可能性" }
    ]
  },
  future: {
    id: "future",
    name: "未来研究所",
    nameEn: "Future Studies Institute",
    icon: "🔮",
    description: "致力于未来趋势预测和情景分析，探索科技、社会、环境等各领域的发展可能性。通过系统性思考和跨学科研究，为理解和塑造未来提供洞察。",
    color: "text-pink-600",
    gradientFrom: "from-pink-500",
    gradientTo: "to-rose-600",
    fields: [
      { name: "科技趋势", description: "新兴技术的发展预测和影响分析" },
      { name: "社会变迁", description: "社会结构和文化的未来演变" },
      { name: "环境未来", description: "气候变化和可持续发展的前景" },
      { name: "工作未来", description: "就业形态和工作方式的变化趋势" },
      { name: "生活方式", description: "未来人类生活方式的可能形态" },
      { name: "风险评估", description: "未来可能面临的挑战和风险" }
    ]
  }
};
function getInstituteConfig(instituteId) {
  return instituteConfigs[instituteId];
}
class TagManager {
  tagCache = /* @__PURE__ */ new Map();
  lastUpdate = 0;
  cacheTimeout = 5 * 60 * 1e3;
  // 5分钟缓存
  /**
   * 获取所有标签统计信息
   */
  async getTagStats() {
    const now = Date.now();
    if (now - this.lastUpdate < this.cacheTimeout && this.tagCache.size > 0) {
      return this.buildStatsFromCache();
    }
    await this.refreshTagCache();
    return this.buildStatsFromCache();
  }
  /**
   * 刷新标签缓存
   */
  async refreshTagCache() {
    this.tagCache.clear();
    const collections = [
      "research",
      "news",
      "logs",
      "ai",
      "economics",
      "philosophy",
      "internet",
      "future",
      "products",
      "reflections"
    ];
    const tagCounts = /* @__PURE__ */ new Map();
    const tagCategories = /* @__PURE__ */ new Map();
    const tagRelations = /* @__PURE__ */ new Map();
    for (const collectionName of collections) {
      try {
        const collection = await getCollection(collectionName);
        for (const entry of collection) {
          const tags = entry.data.tags || [];
          for (const tag of tags) {
            tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);
            if (!tagCategories.has(tag)) {
              tagCategories.set(tag, this.categorizeTag(tag, collectionName));
            }
            if (!tagRelations.has(tag)) {
              tagRelations.set(tag, /* @__PURE__ */ new Set());
            }
            for (const relatedTag of tags) {
              if (relatedTag !== tag) {
                tagRelations.get(tag).add(relatedTag);
              }
            }
          }
        }
      } catch (error) {
        console.warn(`Failed to load collection ${collectionName}:`, error);
      }
    }
    for (const [tagName, count] of tagCounts) {
      const category = tagCategories.get(tagName) || "other";
      const relatedTags = Array.from(tagRelations.get(tagName) || []).sort((a, b) => (tagCounts.get(b) || 0) - (tagCounts.get(a) || 0)).slice(0, 5);
      this.tagCache.set(tagName, {
        name: tagName,
        count,
        category,
        relatedTags,
        color: this.getTagColor(category)
      });
    }
    this.lastUpdate = Date.now();
  }
  /**
   * 从缓存构建统计信息
   */
  buildStatsFromCache() {
    const allTags = Array.from(this.tagCache.values());
    const tagsByCategory = {};
    for (const tag of allTags) {
      if (!tagsByCategory[tag.category]) {
        tagsByCategory[tag.category] = [];
      }
      tagsByCategory[tag.category].push(tag);
    }
    for (const category in tagsByCategory) {
      tagsByCategory[category].sort((a, b) => b.count - a.count);
    }
    const mostPopularTags = allTags.sort((a, b) => b.count - a.count).slice(0, 20);
    const recentTags = mostPopularTags.slice(0, 10).map((tag) => tag.name);
    return {
      totalTags: allTags.length,
      totalUniqueContent: this.calculateUniqueContent(allTags),
      mostPopularTags,
      tagsByCategory,
      recentTags
    };
  }
  /**
   * 根据标签名称和来源集合推断标签分类
   */
  categorizeTag(tagName, collectionName) {
    const tag = tagName.toLowerCase();
    if (tag.includes("ai") || tag.includes("人工智能") || tag.includes("机器学习") || tag.includes("算法") || tag.includes("技术") || tag.includes("数字化")) {
      return "technology";
    }
    if (tag.includes("经济") || tag.includes("市场") || tag.includes("商业") || tag.includes("金融") || tag.includes("投资")) {
      return "economics";
    }
    if (tag.includes("哲学") || tag.includes("伦理") || tag.includes("思考") || tag.includes("价值观") || tag.includes("道德")) {
      return "philosophy";
    }
    if (tag.includes("社会") || tag.includes("文化") || tag.includes("教育") || tag.includes("政策") || tag.includes("治理")) {
      return "society";
    }
    if (tag.includes("研究") || tag.includes("分析") || tag.includes("报告") || tag.includes("框架") || tag.includes("方法")) {
      return "research";
    }
    if (tag.includes("工具") || tag.includes("平台") || tag.includes("系统") || tag.includes("助手") || tag.includes("管理")) {
      return "tools";
    }
    switch (collectionName) {
      case "ai":
        return "technology";
      case "economics":
        return "economics";
      case "philosophy":
        return "philosophy";
      case "products":
        return "tools";
      case "research":
        return "research";
      default:
        return "general";
    }
  }
  /**
   * 获取标签颜色
   */
  getTagColor(category) {
    const colors = {
      technology: "#3b82f6",
      // 蓝色
      economics: "#10b981",
      // 绿色
      philosophy: "#8b5cf6",
      // 紫色
      society: "#f59e0b",
      // 橙色
      research: "#ef4444",
      // 红色
      tools: "#6b7280",
      // 灰色
      general: "#64748b"
      // 石板色
    };
    return colors[category] || colors.general;
  }
  /**
   * 计算唯一内容数量
   */
  calculateUniqueContent(tags) {
    return tags.reduce((sum, tag) => sum + tag.count, 0);
  }
  /**
   * 获取特定标签的详细信息
   */
  async getTagInfo(tagName) {
    if (this.tagCache.size === 0) {
      await this.refreshTagCache();
    }
    return this.tagCache.get(tagName) || null;
  }
  /**
   * 搜索标签
   */
  async searchTags(query) {
    if (this.tagCache.size === 0) {
      await this.refreshTagCache();
    }
    const searchTerm = query.toLowerCase();
    return Array.from(this.tagCache.values()).filter((tag) => tag.name.toLowerCase().includes(searchTerm)).sort((a, b) => b.count - a.count);
  }
  /**
   * 获取标签建议（基于现有标签）
   */
  async getTagSuggestions(partialTag) {
    const searchResults = await this.searchTags(partialTag);
    return searchResults.slice(0, 10).map((tag) => tag.name);
  }
  /**
   * 获取相关标签
   */
  async getRelatedTags(tagName) {
    const tagInfo = await this.getTagInfo(tagName);
    if (!tagInfo) return [];
    const relatedTagInfos = [];
    for (const relatedTagName of tagInfo.relatedTags) {
      const relatedInfo = await this.getTagInfo(relatedTagName);
      if (relatedInfo) {
        relatedTagInfos.push(relatedInfo);
      }
    }
    return relatedTagInfos;
  }
}
const globalTagManager = new TagManager();
function formatDate(date) {
  const d = typeof date === "string" ? new Date(date) : date;
  if (isNaN(d.getTime())) {
    return "无效日期";
  }
  const year = d.getFullYear();
  const month = d.getMonth() + 1;
  const day = d.getDate();
  return `${year}年${month}月${day}日`;
}
function groupByMonth(items) {
  const grouped = {};
  items.forEach((item) => {
    const date = new Date(item.data.date || item.data.pubDate || /* @__PURE__ */ new Date());
    const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}`;
    if (!grouped[monthKey]) {
      grouped[monthKey] = [];
    }
    grouped[monthKey].push(item);
  });
  return grouped;
}
function calculateReadingTime(content) {
  const wordsPerMinute = 200;
  const wordCount = content.length;
  return Math.ceil(wordCount / wordsPerMinute);
}
function buildSearchIndex(posts) {
  return posts.filter((post) => !post.data.draft).map((post) => ({
    id: post.id,
    title: post.data.title.zh,
    description: post.data.description.zh,
    content: post.body,
    tags: post.data.tags,
    category: post.collection,
    url: `/${post.collection}/${post.slug}`,
    publishDate: post.data.publishDate.toISOString()
  }));
}
export {
  getEnvConfig as a,
  getSecurityHeaders as b,
  createSecurityMiddleware as c,
  contentManager as d,
  getCollection as e,
  getInstituteConfig as f,
  globalTagManager as g,
  getEntry as h,
  groupByMonth as i,
  formatDate as j,
  calculateReadingTime as k,
  buildSearchIndex as l
};
