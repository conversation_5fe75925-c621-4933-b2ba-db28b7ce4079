#!/usr/bin/env node

import { existsSync, mkdirSync, writeFileSync } from 'fs';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * 构建错误处理和报告工具
 */
class BuildErrorHandler {
  constructor() {
    this.projectRoot = join(__dirname, '..');
    this.reportsDir = join(this.projectRoot, 'reports');
    this.errorLogFile = join(this.reportsDir, 'build-errors.log');
    this.errorTypes = {
      TYPESCRIPT_ERROR: 'TypeScript 类型错误',
      IMPORT_ERROR: '导入错误',
      SYNTAX_ERROR: '语法错误',
      DEPENDENCY_ERROR: '依赖错误',
      CONFIG_ERROR: '配置错误',
      ASSET_ERROR: '资源错误',
      UNKNOWN_ERROR: '未知错误'
    };
  }

  /**
   * 确保报告目录存在
   */
  ensureReportsDir() {
    if (!existsSync(this.reportsDir)) {
      mkdirSync(this.reportsDir, { recursive: true });
    }
  }

  /**
   * 分析错误类型
   */
  analyzeErrorType(errorMessage) {
    const message = errorMessage.toLowerCase();
    
    if (message.includes('typescript') || message.includes('type error') || message.includes('ts(')) {
      return 'TYPESCRIPT_ERROR';
    }
    
    if (message.includes('cannot resolve') || message.includes('module not found') || message.includes('import')) {
      return 'IMPORT_ERROR';
    }
    
    if (message.includes('syntax error') || message.includes('unexpected token')) {
      return 'SYNTAX_ERROR';
    }
    
    if (message.includes('dependency') || message.includes('package') || message.includes('node_modules')) {
      return 'DEPENDENCY_ERROR';
    }
    
    if (message.includes('config') || message.includes('astro.config') || message.includes('vite.config')) {
      return 'CONFIG_ERROR';
    }
    
    if (message.includes('asset') || message.includes('image') || message.includes('font')) {
      return 'ASSET_ERROR';
    }
    
    return 'UNKNOWN_ERROR';
  }

  /**
   * 生成错误解决方案
   */
  generateSolution(errorType, errorMessage) {
    const solutions = {
      TYPESCRIPT_ERROR: [
        '检查 TypeScript 类型定义',
        '确保所有变量都有正确的类型注解',
        '检查 tsconfig.json 配置',
        '更新 @types/* 包到最新版本',
        '使用 // @ts-ignore 临时忽略类型错误（不推荐）'
      ],
      
      IMPORT_ERROR: [
        '检查文件路径是否正确',
        '确保文件扩展名正确',
        '验证路径别名配置',
        '检查文件是否存在',
        '确保导出语法正确'
      ],
      
      SYNTAX_ERROR: [
        '检查 JavaScript/TypeScript 语法',
        '确保括号、引号匹配',
        '检查 ES6+ 语法兼容性',
        '运行 ESLint 检查语法错误',
        '使用代码格式化工具'
      ],
      
      DEPENDENCY_ERROR: [
        '运行 npm install 重新安装依赖',
        '清除 node_modules 和 package-lock.json 后重新安装',
        '检查 package.json 中的依赖版本',
        '更新过时的依赖包',
        '检查依赖包的兼容性'
      ],
      
      CONFIG_ERROR: [
        '检查 astro.config.mjs 语法',
        '验证配置选项的正确性',
        '检查插件配置',
        '确保配置文件导出正确',
        '参考官方文档更新配置'
      ],
      
      ASSET_ERROR: [
        '检查资源文件路径',
        '确保图片和字体文件存在',
        '检查文件权限',
        '验证资源文件格式',
        '检查 public 目录结构'
      ],
      
      UNKNOWN_ERROR: [
        '查看完整的错误堆栈',
        '搜索错误信息寻找解决方案',
        '检查 Node.js 版本兼容性',
        '尝试清除缓存后重新构建',
        '查看项目文档或社区支持'
      ]
    };
    
    return solutions[errorType] || solutions.UNKNOWN_ERROR;
  }

  /**
   * 格式化错误报告
   */
  formatErrorReport(error) {
    const timestamp = new Date().toISOString();
    const errorType = this.analyzeErrorType(error.message);
    const solutions = this.generateSolution(errorType, error.message);
    
    return {
      timestamp,
      type: errorType,
      typeName: this.errorTypes[errorType],
      message: error.message,
      stack: error.stack,
      solutions,
      context: {
        nodeVersion: process.version,
        platform: process.platform,
        cwd: process.cwd()
      }
    };
  }

  /**
   * 记录错误到日志文件
   */
  logError(errorReport) {
    this.ensureReportsDir();
    
    const logEntry = `
${'='.repeat(80)}
时间: ${errorReport.timestamp}
错误类型: ${errorReport.typeName}
错误信息: ${errorReport.message}

解决方案:
${errorReport.solutions.map((solution, index) => `${index + 1}. ${solution}`).join('\n')}

技术详情:
- Node.js 版本: ${errorReport.context.nodeVersion}
- 平台: ${errorReport.context.platform}
- 工作目录: ${errorReport.context.cwd}

堆栈跟踪:
${errorReport.stack || '无堆栈信息'}
${'='.repeat(80)}
`;
    
    try {
      writeFileSync(this.errorLogFile, logEntry, { flag: 'a' });
      console.log(`📝 错误已记录到: ${this.errorLogFile}`);
    } catch (err) {
      console.warn('无法写入错误日志:', err.message);
    }
  }

  /**
   * 打印友好的错误信息
   */
  printFriendlyError(errorReport) {
    console.log('\n❌ 构建失败');
    console.log('='.repeat(50));
    
    console.log(`\n🔍 错误类型: ${errorReport.typeName}`);
    console.log(`📝 错误信息: ${errorReport.message}`);
    
    console.log(`\n💡 建议的解决方案:`);
    errorReport.solutions.forEach((solution, index) => {
      console.log(`   ${index + 1}. ${solution}`);
    });
    
    console.log(`\n🕐 发生时间: ${errorReport.timestamp}`);
    console.log(`📍 工作目录: ${errorReport.context.cwd}`);
    
    console.log('\n📚 更多帮助:');
    console.log('   • 查看完整错误日志: ' + this.errorLogFile);
    console.log('   • Astro 文档: https://docs.astro.build/');
    console.log('   • 社区支持: https://astro.build/chat/');
    
    console.log('\n='.repeat(50));
  }

  /**
   * 处理构建错误
   */
  handleError(error) {
    const errorReport = this.formatErrorReport(error);
    
    // 记录到日志文件
    this.logError(errorReport);
    
    // 打印友好的错误信息
    this.printFriendlyError(errorReport);
    
    return errorReport;
  }

  /**
   * 设置全局错误处理器
   */
  setupGlobalErrorHandlers() {
    // 处理未捕获的异常
    process.on('uncaughtException', (error) => {
      console.error('未捕获的异常:');
      this.handleError(error);
      process.exit(1);
    });
    
    // 处理未处理的 Promise 拒绝
    process.on('unhandledRejection', (reason, promise) => {
      console.error('未处理的 Promise 拒绝:');
      const error = reason instanceof Error ? reason : new Error(String(reason));
      this.handleError(error);
      process.exit(1);
    });
  }

  /**
   * 包装构建命令以捕获错误
   */
  wrapBuildCommand(buildFunction) {
    return async (...args) => {
      try {
        return await buildFunction(...args);
      } catch (error) {
        this.handleError(error);
        throw error; // 重新抛出错误以保持原有行为
      }
    };
  }

  /**
   * 生成错误统计报告
   */
  generateErrorStats() {
    // 这里可以分析错误日志文件，生成统计信息
    // 由于是示例，这里只返回基本信息
    return {
      message: '错误统计功能需要分析历史日志文件',
      suggestion: '可以扩展此功能来分析常见错误模式'
    };
  }
}

// 导出错误处理器
export default BuildErrorHandler;

// 如果直接运行此脚本，设置全局错误处理
if (import.meta.url === `file://${process.argv[1]}`) {
  const errorHandler = new BuildErrorHandler();
  errorHandler.setupGlobalErrorHandlers();
  console.log('✅ 全局错误处理器已设置');
}