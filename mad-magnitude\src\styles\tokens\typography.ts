/**
 * 设计令牌 - 字体系统
 * 针对学术内容阅读优化的字体配置
 */

export const typography = {
  // 字体族定义
  fontFamily: {
    // 无衬线字体 - 用于界面和标题
    sans: [
      'Inter',
      'PingFang SC',
      'Hiragino Sans GB',
      'Microsoft YaHei',
      'WenQuanYi Micro Hei',
      'system-ui',
      '-apple-system',
      'BlinkMacSystemFont',
      'Segoe UI',
      'Helvetica Neue',
      'Helvetica',
      'Arial',
      'sans-serif',
    ],

    // 衬线字体 - 用于正文阅读
    serif: [
      'Crimson Text',
      'Source Serif Pro',
      'Noto Serif SC',
      'Georgia',
      'Times New Roman',
      'serif',
    ],

    // 等宽字体 - 用于代码
    mono: [
      'JetBrains Mono',
      'Fira Code',
      'Source Code Pro',
      'Consolas',
      'Monaco',
      'Courier New',
      'monospace',
    ],

    // 数学字体已移除
  },

  // 字体大小系统 (包含行高)
  fontSize: {
    xs: ['0.75rem', { lineHeight: '1rem', letterSpacing: '0.025em' }], // 12px
    sm: ['0.875rem', { lineHeight: '1.25rem', letterSpacing: '0.025em' }], // 14px
    base: ['1rem', { lineHeight: '1.5rem', letterSpacing: '0' }], // 16px
    lg: ['1.125rem', { lineHeight: '1.75rem', letterSpacing: '-0.025em' }], // 18px
    xl: ['1.25rem', { lineHeight: '1.75rem', letterSpacing: '-0.025em' }], // 20px
    '2xl': ['1.5rem', { lineHeight: '2rem', letterSpacing: '-0.025em' }], // 24px
    '3xl': ['1.875rem', { lineHeight: '2.25rem', letterSpacing: '-0.05em' }], // 30px
    '4xl': ['2.25rem', { lineHeight: '2.5rem', letterSpacing: '-0.05em' }], // 36px
    '5xl': ['3rem', { lineHeight: '1', letterSpacing: '-0.05em' }], // 48px
    '6xl': ['3.75rem', { lineHeight: '1', letterSpacing: '-0.05em' }], // 60px
    '7xl': ['4.5rem', { lineHeight: '1', letterSpacing: '-0.05em' }], // 72px
    '8xl': ['6rem', { lineHeight: '1', letterSpacing: '-0.05em' }], // 96px
    '9xl': ['8rem', { lineHeight: '1', letterSpacing: '-0.05em' }], // 128px
  },

  // 字体粗细
  fontWeight: {
    thin: '100',
    extralight: '200',
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
    black: '900',
  },

  // 行高系统
  lineHeight: {
    none: '1',
    tight: '1.25',
    snug: '1.375',
    normal: '1.5',
    relaxed: '1.625',
    loose: '2',
    3: '.75rem',
    4: '1rem',
    5: '1.25rem',
    6: '1.5rem',
    7: '1.75rem',
    8: '2rem',
    9: '2.25rem',
    10: '2.5rem',
  },

  // 字母间距
  letterSpacing: {
    tighter: '-0.05em',
    tight: '-0.025em',
    normal: '0em',
    wide: '0.025em',
    wider: '0.05em',
    widest: '0.1em',
  },

  // 学术内容专用字体配置
  academic: {
    // 文章标题
    heading: {
      fontFamily: 'sans',
      fontSize: '2xl',
      fontWeight: 'bold',
      lineHeight: 'tight',
      letterSpacing: 'tight',
    },

    // 文章正文
    body: {
      fontFamily: 'serif',
      fontSize: 'lg',
      fontWeight: 'normal',
      lineHeight: 'relaxed',
      letterSpacing: 'normal',
    },

    // 代码块
    code: {
      fontFamily: 'mono',
      fontSize: 'sm',
      fontWeight: 'normal',
      lineHeight: 'normal',
      letterSpacing: 'normal',
    },

    // 数学公式
    math: {
      fontFamily: 'math',
      fontSize: 'base',
      fontWeight: 'normal',
      lineHeight: 'normal',
      letterSpacing: 'normal',
    },
  },
} as const;

// 导出类型定义
export type TypographyToken = typeof typography;
export type FontSize = keyof typeof typography.fontSize;
export type FontWeight = keyof typeof typography.fontWeight;
