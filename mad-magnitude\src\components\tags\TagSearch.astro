---
/**
 * 标签搜索组件
 * 提供标签搜索和自动完成功能
 */
export interface Props {
  placeholder?: string;
  showCategories?: boolean;
  maxSuggestions?: number;
  size?: 'small' | 'medium' | 'large';
  autoFocus?: boolean;
}

const {
  placeholder = '搜索标签...',
  showCategories = true,
  maxSuggestions = 8,
  size = 'medium',
  autoFocus = false,
} = Astro.props;
---

<div class={`tag-search tag-search--${size}`}>
  <div class="search-container">
    <div class="search-input-wrapper">
      <input
        type="text"
        class="search-input"
        placeholder={placeholder}
        autocomplete="off"
        spellcheck="false"
        autofocus={autoFocus}
        data-max-suggestions={maxSuggestions}
      />
      <div class="search-icon">
        <svg
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
        >
          <circle cx="11" cy="11" r="8"></circle>
          <path d="m21 21-4.35-4.35"></path>
        </svg>
      </div>
      <button class="clear-button" style="display: none;" title="清除搜索">
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
        >
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
      </button>
    </div>

    {
      showCategories && (
        <div class="category-filter">
          <select class="category-select">
            <option value="all">所有分类</option>
            <option value="technology">🔬 技术</option>
            <option value="economics">💰 经济</option>
            <option value="philosophy">🤔 哲学</option>
            <option value="society">🏛️ 社会</option>
            <option value="research">📊 研究</option>
            <option value="tools">🛠️ 工具</option>
            <option value="general">📝 通用</option>
          </select>
        </div>
      )
    }
  </div>

  <!-- 搜索建议下拉框 -->
  <div class="suggestions-dropdown" style="display: none;">
    <div class="suggestions-header">
      <span class="suggestions-title">搜索建议</span>
      <span class="suggestions-count"></span>
    </div>
    <div class="suggestions-list"></div>
    <div class="suggestions-footer">
      <button class="view-all-button">查看所有结果</button>
    </div>
  </div>

  <!-- 加载状态 -->
  <div class="loading-indicator" style="display: none;">
    <div class="loading-spinner"></div>
    <span class="loading-text">搜索中...</span>
  </div>

  <!-- 无结果状态 -->
  <div class="no-results" style="display: none;">
    <div class="no-results-icon">🔍</div>
    <div class="no-results-text">未找到相关标签</div>
    <div class="no-results-suggestion">尝试使用其他关键词</div>
  </div>
</div>

<style>
  .tag-search {
    position: relative;
    width: 100%;
    max-width: 500px;
  }

  .search-container {
    display: flex;
    gap: 0.75rem;
    align-items: center;
  }

  .search-input-wrapper {
    position: relative;
    flex: 1;
  }

  .search-input {
    width: 100%;
    padding: 0.75rem 3rem 0.75rem 2.5rem;
    border: 2px solid #e2e8f0;
    border-radius: 0.5rem;
    font-size: 1rem;
    background: white;
    transition: all 0.2s ease;
    outline: none;
  }

  .search-input:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .search-input::placeholder {
    color: #9ca3af;
  }

  .search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
    pointer-events: none;
  }

  .clear-button {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
  }

  .clear-button:hover {
    color: #374151;
    background: #f3f4f6;
  }

  .category-filter {
    flex-shrink: 0;
  }

  .category-select {
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 0.5rem;
    background: white;
    font-size: 0.875rem;
    cursor: pointer;
    outline: none;
    transition: border-color 0.2s ease;
  }

  .category-select:focus {
    border-color: #3b82f6;
  }

  /* 建议下拉框 */
  .suggestions-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    z-index: 50;
    margin-top: 0.5rem;
    max-height: 400px;
    overflow: hidden;
  }

  .suggestions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f1f5f9;
    background: #f8fafc;
  }

  .suggestions-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
  }

  .suggestions-count {
    font-size: 0.75rem;
    color: #6b7280;
  }

  .suggestions-list {
    max-height: 300px;
    overflow-y: auto;
  }

  .suggestion-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid #f9fafb;
  }

  .suggestion-item:hover,
  .suggestion-item.highlighted {
    background: #f1f5f9;
  }

  .suggestion-item:last-child {
    border-bottom: none;
  }

  .suggestion-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex: 1;
  }

  .suggestion-name {
    font-weight: 500;
    color: #1f2937;
  }

  .suggestion-category {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.125rem 0.5rem;
    background: #f3f4f6;
    color: #6b7280;
    border-radius: 1rem;
    font-size: 0.75rem;
  }

  .suggestion-count {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
  }

  .suggestions-footer {
    padding: 0.75rem 1rem;
    border-top: 1px solid #f1f5f9;
    background: #f8fafc;
  }

  .view-all-button {
    width: 100%;
    padding: 0.5rem;
    background: #3b82f6;
    color: white;
    border: none;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .view-all-button:hover {
    background: #2563eb;
  }

  /* 加载状态 */
  .loading-indicator {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    z-index: 50;
  }

  .loading-spinner {
    width: 1rem;
    height: 1rem;
    border: 2px solid #e2e8f0;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loading-text {
    font-size: 0.875rem;
    color: #6b7280;
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  /* 无结果状态 */
  .no-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    padding: 2rem 1rem;
    margin-top: 0.5rem;
    text-align: center;
    z-index: 50;
  }

  .no-results-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    opacity: 0.6;
  }

  .no-results-text {
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.25rem;
  }

  .no-results-suggestion {
    font-size: 0.875rem;
    color: #6b7280;
  }

  /* 尺寸变体 */
  .tag-search--small .search-input {
    padding: 0.5rem 2.5rem 0.5rem 2rem;
    font-size: 0.875rem;
  }

  .tag-search--small .category-select {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
  }

  .tag-search--large .search-input {
    padding: 1rem 3.5rem 1rem 3rem;
    font-size: 1.125rem;
  }

  .tag-search--large .category-select {
    padding: 1rem 1.25rem;
    font-size: 1rem;
  }

  /* 响应式设计 */
  @media (max-width: 640px) {
    .search-container {
      flex-direction: column;
      gap: 0.5rem;
    }

    .category-filter {
      width: 100%;
    }

    .category-select {
      width: 100%;
    }
  }
</style>

<script>
  class TagSearch {
    private input: HTMLInputElement;
    private clearButton: HTMLButtonElement;
    private categorySelect: HTMLSelectElement | null;
    private suggestionsDropdown: HTMLElement;
    private suggestionsList: HTMLElement;
    private suggestionsCount: HTMLElement;
    private loadingIndicator: HTMLElement;
    private noResults: HTMLElement;
    private viewAllButton: HTMLButtonElement;
    private maxSuggestions: number;
    private searchTimeout: number | null = null;
    private currentQuery = '';
    private highlightedIndex = -1;
    private suggestions: any[] = [];

    constructor(container: HTMLElement) {
      this.input = container.querySelector('.search-input')!;
      this.clearButton = container.querySelector('.clear-button')!;
      this.categorySelect = container.querySelector('.category-select');
      this.suggestionsDropdown = container.querySelector('.suggestions-dropdown')!;
      this.suggestionsList = container.querySelector('.suggestions-list')!;
      this.suggestionsCount = container.querySelector('.suggestions-count')!;
      this.loadingIndicator = container.querySelector('.loading-indicator')!;
      this.noResults = container.querySelector('.no-results')!;
      this.viewAllButton = container.querySelector('.view-all-button')!;
      this.maxSuggestions = parseInt(this.input.dataset.maxSuggestions || '8');

      this.bindEvents();
    }

    private bindEvents() {
      // 输入事件
      this.input.addEventListener('input', e => {
        const query = (e.target as HTMLInputElement).value.trim();
        this.handleInput(query);
      });

      // 键盘导航
      this.input.addEventListener('keydown', e => {
        this.handleKeydown(e);
      });

      // 清除按钮
      this.clearButton.addEventListener('click', () => {
        this.clearSearch();
      });

      // 分类筛选
      if (this.categorySelect) {
        this.categorySelect.addEventListener('change', () => {
          if (this.currentQuery) {
            this.performSearch(this.currentQuery);
          }
        });
      }

      // 查看所有结果
      this.viewAllButton.addEventListener('click', () => {
        this.navigateToTagsPage();
      });

      // 点击外部关闭下拉框
      document.addEventListener('click', e => {
        if (!this.input.closest('.tag-search')?.contains(e.target as Node)) {
          this.hideSuggestions();
        }
      });
    }

    private handleInput(query: string) {
      this.currentQuery = query;
      this.updateClearButton();

      if (this.searchTimeout) {
        clearTimeout(this.searchTimeout);
      }

      if (query.length === 0) {
        this.hideSuggestions();
        return;
      }

      if (query.length < 2) {
        return;
      }

      // 防抖搜索
      this.searchTimeout = window.setTimeout(() => {
        this.performSearch(query);
      }, 300);
    }

    private async performSearch(query: string) {
      this.showLoading();

      try {
        const category = this.categorySelect?.value || 'all';
        const response = await fetch(
          `/api/tags/search?q=${encodeURIComponent(query)}&category=${category}&limit=${this.maxSuggestions}`
        );

        if (!response.ok) {
          throw new Error('搜索失败');
        }

        const data = await response.json();
        this.suggestions = data.results;
        this.displaySuggestions(data);
      } catch (error) {
        console.error('标签搜索错误:', error);
        this.showNoResults();
      }
    }

    private displaySuggestions(data: any) {
      this.hideLoading();

      if (data.results.length === 0) {
        this.showNoResults();
        return;
      }

      this.suggestionsList.innerHTML = '';
      this.suggestionsCount.textContent = `${data.results.length} 个结果`;

      data.results.forEach((tag: any, index: number) => {
        const item = document.createElement('div');
        item.className = 'suggestion-item';
        item.dataset.index = index.toString();

        item.innerHTML = `
          <div class="suggestion-content">
            <span class="suggestion-name">${this.highlightQuery(tag.name, data.query)}</span>
            <span class="suggestion-category">${tag.categoryDisplayName}</span>
          </div>
          <span class="suggestion-count">${tag.count}</span>
        `;

        item.addEventListener('click', () => {
          this.selectTag(tag);
        });

        this.suggestionsList.appendChild(item);
      });

      this.showSuggestions();
      this.highlightedIndex = -1;
    }

    private highlightQuery(text: string, query: string): string {
      const regex = new RegExp(`(${query})`, 'gi');
      return text.replace(regex, '<mark>$1</mark>');
    }

    private handleKeydown(e: KeyboardEvent) {
      if (!this.isSuggestionsVisible()) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          this.highlightNext();
          break;
        case 'ArrowUp':
          e.preventDefault();
          this.highlightPrevious();
          break;
        case 'Enter':
          e.preventDefault();
          this.selectHighlighted();
          break;
        case 'Escape':
          this.hideSuggestions();
          break;
      }
    }

    private highlightNext() {
      const items = this.suggestionsList.querySelectorAll('.suggestion-item');
      if (items.length === 0) return;

      if (this.highlightedIndex < items.length - 1) {
        this.highlightedIndex++;
      } else {
        this.highlightedIndex = 0;
      }

      this.updateHighlight();
    }

    private highlightPrevious() {
      const items = this.suggestionsList.querySelectorAll('.suggestion-item');
      if (items.length === 0) return;

      if (this.highlightedIndex > 0) {
        this.highlightedIndex--;
      } else {
        this.highlightedIndex = items.length - 1;
      }

      this.updateHighlight();
    }

    private updateHighlight() {
      const items = this.suggestionsList.querySelectorAll('.suggestion-item');
      items.forEach((item, index) => {
        item.classList.toggle('highlighted', index === this.highlightedIndex);
      });
    }

    private selectHighlighted() {
      if (this.highlightedIndex >= 0 && this.suggestions[this.highlightedIndex]) {
        this.selectTag(this.suggestions[this.highlightedIndex]);
      }
    }

    private selectTag(tag: any) {
      window.location.href = tag.url;
    }

    private navigateToTagsPage() {
      const query = this.currentQuery;
      const category = this.categorySelect?.value || 'all';

      let url = '/tags';
      const params = new URLSearchParams();

      if (query) params.set('q', query);
      if (category !== 'all') params.set('category', category);

      if (params.toString()) {
        url += '?' + params.toString();
      }

      window.location.href = url;
    }

    private clearSearch() {
      this.input.value = '';
      this.currentQuery = '';
      this.hideSuggestions();
      this.updateClearButton();
      this.input.focus();
    }

    private updateClearButton() {
      this.clearButton.style.display = this.input.value ? 'block' : 'none';
    }

    private showSuggestions() {
      this.suggestionsDropdown.style.display = 'block';
      this.loadingIndicator.style.display = 'none';
      this.noResults.style.display = 'none';
    }

    private hideSuggestions() {
      this.suggestionsDropdown.style.display = 'none';
      this.loadingIndicator.style.display = 'none';
      this.noResults.style.display = 'none';
    }

    private showLoading() {
      this.loadingIndicator.style.display = 'flex';
      this.suggestionsDropdown.style.display = 'none';
      this.noResults.style.display = 'none';
    }

    private hideLoading() {
      this.loadingIndicator.style.display = 'none';
    }

    private showNoResults() {
      this.noResults.style.display = 'block';
      this.suggestionsDropdown.style.display = 'none';
      this.loadingIndicator.style.display = 'none';
    }

    private isSuggestionsVisible(): boolean {
      return this.suggestionsDropdown.style.display === 'block';
    }
  }

  // 初始化所有标签搜索组件
  document.addEventListener('DOMContentLoaded', () => {
    const searchContainers = document.querySelectorAll('.tag-search');
    searchContainers.forEach(container => {
      new TagSearch(container as HTMLElement);
    });
  });
</script>
