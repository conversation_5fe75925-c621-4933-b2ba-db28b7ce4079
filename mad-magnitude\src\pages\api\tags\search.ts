/**
 * 标签搜索 API
 * 提供标签搜索和建议功能
 */
import type { APIRoute } from 'astro';
import { globalTagManager } from '../../../utils/tagManager';

export const GET: APIRoute = async ({ url }) => {
  try {
    const searchParams = new URL(url).searchParams;
    const query = searchParams.get('q');
    const limit = parseInt(searchParams.get('limit') || '10');
    const category = searchParams.get('category');

    if (!query || query.trim().length === 0) {
      return new Response(
        JSON.stringify({
          error: 'Query parameter is required',
          code: 'MISSING_QUERY',
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    // 搜索标签
    const searchResults = await globalTagManager.searchTags(query.trim());

    // 按分类筛选（如果指定）
    let filteredResults = searchResults;
    if (category && category !== 'all') {
      filteredResults = searchResults.filter(tag => tag.category === category);
    }

    // 限制结果数量
    const limitedResults = filteredResults.slice(0, limit);

    // 构建响应数据
    const response = {
      query: query.trim(),
      total: filteredResults.length,
      results: limitedResults.map(tag => ({
        name: tag.name,
        count: tag.count,
        category: tag.category,
        categoryDisplayName: getCategoryDisplayName(tag.category),
        color: tag.color,
        url: `/tags/${encodeURIComponent(tag.name)}`,
        relatedTags: tag.relatedTags.slice(0, 3),
      })),
      suggestions: await generateSearchSuggestions(query.trim(), searchResults),
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=300', // 缓存5分钟
      },
    });
  } catch (error) {
    console.error('标签搜索 API 错误:', error);

    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        code: 'INTERNAL_ERROR',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};

/**
 * 生成搜索建议
 */
async function generateSearchSuggestions(query: string, allResults: any[]): Promise<string[]> {
  const suggestions: string[] = [];
  const queryLower = query.toLowerCase();

  // 1. 完全匹配的标签
  const exactMatches = allResults.filter(tag => tag.name.toLowerCase() === queryLower);

  // 2. 以查询词开头的标签
  const startsWith = allResults.filter(
    tag => tag.name.toLowerCase().startsWith(queryLower) && tag.name.toLowerCase() !== queryLower
  );

  // 3. 包含查询词的标签
  const contains = allResults.filter(
    tag =>
      tag.name.toLowerCase().includes(queryLower) && !tag.name.toLowerCase().startsWith(queryLower)
  );

  // 4. 相关标签（基于已找到的标签）
  const relatedTags = new Set<string>();
  [...exactMatches, ...startsWith].forEach(tag => {
    tag.relatedTags.forEach((related: string) => {
      if (related.toLowerCase().includes(queryLower)) {
        relatedTags.add(related);
      }
    });
  });

  // 组合建议，按优先级排序
  suggestions.push(
    ...exactMatches.slice(0, 2).map(tag => tag.name),
    ...startsWith.slice(0, 3).map(tag => tag.name),
    ...contains.slice(0, 3).map(tag => tag.name),
    ...Array.from(relatedTags).slice(0, 2)
  );

  // 去重并限制数量
  return [...new Set(suggestions)].slice(0, 8);
}

/**
 * 获取分类显示名称
 */
function getCategoryDisplayName(category: string): string {
  const names: Record<string, string> = {
    technology: '技术',
    economics: '经济',
    philosophy: '哲学',
    society: '社会',
    research: '研究',
    tools: '工具',
    general: '通用',
  };
  return names[category] || category;
}
