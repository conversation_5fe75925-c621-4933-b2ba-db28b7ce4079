---
/**
 * 灵活的网格布局组件
 * 支持响应式网格布局，提供多种列数和间距选项
 */

export interface Props {
  /** 默认列数 */
  cols?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
  /** 小屏幕列数 (sm: 640px+) */
  smCols?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
  /** 中等屏幕列数 (md: 768px+) */
  mdCols?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
  /** 大屏幕列数 (lg: 1024px+) */
  lgCols?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
  /** 超大屏幕列数 (xl: 1280px+) */
  xlCols?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
  /** 2XL屏幕列数 (2xl: 1536px+) */
  '2xlCols'?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
  /** 网格间距 */
  gap?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  /** 水平间距 */
  gapX?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  /** 垂直间距 */
  gapY?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  /** 网格项目对齐方式 */
  itemsAlign?: 'start' | 'end' | 'center' | 'stretch';
  /** 网格内容对齐方式 */
  contentAlign?: 'start' | 'end' | 'center' | 'between' | 'around' | 'evenly';
  /** 自定义 CSS 类 */
  class?: string;
  /** HTML 标签类型 */
  as?: 'div' | 'section' | 'article' | 'ul' | 'ol';
}

const {
  cols = 1,
  smCols,
  mdCols,
  lgCols,
  xlCols,
  '2xlCols': xxlCols,
  gap = 'md',
  gapX,
  gapY,
  itemsAlign = 'stretch',
  contentAlign,
  class: className = '',
  as: Tag = 'div',
} = Astro.props;

// 列数类名映射
const colsClasses = {
  1: 'grid-cols-1',
  2: 'grid-cols-2',
  3: 'grid-cols-3',
  4: 'grid-cols-4',
  5: 'grid-cols-5',
  6: 'grid-cols-6',
  7: 'grid-cols-7',
  8: 'grid-cols-8',
  9: 'grid-cols-9',
  10: 'grid-cols-10',
  11: 'grid-cols-11',
  12: 'grid-cols-12',
};

// 间距类名映射
const gapClasses = {
  none: 'gap-0',
  xs: 'gap-1',
  sm: 'gap-2',
  md: 'gap-4',
  lg: 'gap-6',
  xl: 'gap-8',
  '2xl': 'gap-12',
};

const gapXClasses = {
  none: 'gap-x-0',
  xs: 'gap-x-1',
  sm: 'gap-x-2',
  md: 'gap-x-4',
  lg: 'gap-x-6',
  xl: 'gap-x-8',
  '2xl': 'gap-x-12',
};

const gapYClasses = {
  none: 'gap-y-0',
  xs: 'gap-y-1',
  sm: 'gap-y-2',
  md: 'gap-y-4',
  lg: 'gap-y-6',
  xl: 'gap-y-8',
  '2xl': 'gap-y-12',
};

// 对齐类名映射
const itemsAlignClasses = {
  start: 'items-start',
  end: 'items-end',
  center: 'items-center',
  stretch: 'items-stretch',
};

const contentAlignClasses = {
  start: 'content-start',
  end: 'content-end',
  center: 'content-center',
  between: 'content-between',
  around: 'content-around',
  evenly: 'content-evenly',
};

// 构建响应式列数类名
const responsiveColsClasses = [
  colsClasses[cols],
  smCols ? `sm:${colsClasses[smCols]}` : '',
  mdCols ? `md:${colsClasses[mdCols]}` : '',
  lgCols ? `lg:${colsClasses[lgCols]}` : '',
  xlCols ? `xl:${colsClasses[xlCols]}` : '',
  xxlCols ? `2xl:${colsClasses[xxlCols]}` : '',
].filter(Boolean);

// 构建间距类名
const gapClassNames = [];
if (gapX || gapY) {
  // 如果指定了 gapX 或 gapY，使用它们
  if (gapX) gapClassNames.push(gapXClasses[gapX]);
  if (gapY) gapClassNames.push(gapYClasses[gapY]);
} else {
  // 否则使用统一的 gap
  gapClassNames.push(gapClasses[gap]);
}

// 组合所有类名
const gridClasses = [
  'grid',
  ...responsiveColsClasses,
  ...gapClassNames,
  itemsAlignClasses[itemsAlign],
  contentAlign ? contentAlignClasses[contentAlign] : '',
  className,
]
  .filter(Boolean)
  .join(' ');
---

<Tag class={gridClasses}>
  <slot />
</Tag>
