# 简化版ESLint配置示例

以下是一个简化版的ESLint配置，保留了核心规则，移除了不必要的复杂性，适合小型项目或项目初期使用。

## 简化版 eslint.config.js

```javascript
import js from '@eslint/js';
import typescript from '@typescript-eslint/eslint-plugin';
import typescriptParser from '@typescript-eslint/parser';
import astro from 'eslint-plugin-astro';

export default [
  // 基础推荐配置
  js.configs.recommended,

  // TypeScript和JavaScript文件配置
  {
    files: ['**/*.{js,mjs,cjs,ts}'],
    plugins: {
      '@typescript-eslint': typescript,
    },
    languageOptions: {
      parser: typescriptParser,
      parserOptions: {
        ecmaVersion: 'latest',
        sourceType: 'module',
      },
      globals: {
        console: 'readonly',
        process: 'readonly',
      },
    },
    rules: {
      // TypeScript核心规则
      '@typescript-eslint/no-unused-vars': 'error',
      '@typescript-eslint/no-explicit-any': 'warn',

      // 通用规则
      'no-console': 'warn',
      'no-debugger': 'error',
      'prefer-const': 'error',

      // 基础安全规则
      'no-eval': 'error',
      'no-implied-eval': 'error',
      'no-script-url': 'error',
    },
  },

  // Astro文件配置
  ...astro.configs.recommended,

  // Astro文件特定规则
  {
    files: ['**/*.astro'],
    rules: {
      'no-console': 'warn',
      'no-debugger': 'error',
    },
  },
];
```

## 简化说明

1. **移除复杂规则**:
   - 删除了过于严格的规则
   - 移除了非必要的警告规则
   - 保留了代码质量和安全的核心规则

2. **简化配置结构**:
   - 减少了嵌套层级
   - 合并了相似的配置
   - 简化了规则定义

3. **保留核心功能**:
   - 仍然保持代码格式一致性
   - 仍然检测潜在问题
   - 仍然提供基本的安全保护

## 对比原配置

原配置有70+条规则，而简化版只保留了15条核心规则，减少了约80%的规则数量，同时保留了最重要的代码质量和安全功能。

## 使用建议

1. 可以根据项目需要逐步添加更多规则
2. 定期评估规则的有效性，移除不必要的规则
3. 保持配置简洁，避免过度工程化
