/**
 * 缓存工具类
 */

export interface CacheOptions {
  ttl?: number; // 生存时间（毫秒）
  maxSize?: number; // 最大缓存条目数
  storage?: 'memory' | 'localStorage' | 'sessionStorage';
}

export class Cache {
  private cache = new Map<string, { value: any; timestamp: number; ttl: number }>();
  private options: Required<CacheOptions>;

  constructor(options: CacheOptions = {}) {
    this.options = {
      ttl: 5 * 60 * 1000, // 默认5分钟
      maxSize: 100,
      storage: 'memory',
      ...options,
    };
  }

  set(key: string, value: any, ttl?: number): void {
    const actualTtl = ttl ?? this.options.ttl;
    const timestamp = Date.now();

    // 如果缓存已满，删除最旧的条目
    if (this.cache.size >= this.options.maxSize) {
      const oldestKey = this.cache.keys().next().value;
      if (oldestKey) {
        this.cache.delete(oldestKey);
      }
    }

    this.cache.set(key, { value, timestamp, ttl: actualTtl });

    // 如果使用浏览器存储
    if (this.options.storage !== 'memory' && typeof window !== 'undefined') {
      try {
        const storage = this.options.storage === 'localStorage' ? localStorage : sessionStorage;
        storage.setItem(`cache_${key}`, JSON.stringify({ value, timestamp, ttl: actualTtl }));
      } catch (error) {
        console.warn('Failed to save to storage:', error);
      }
    }
  }

  get<T = any>(key: string): T | null {
    // 先检查内存缓存
    const memoryItem = this.cache.get(key);
    if (memoryItem && this.isValid(memoryItem)) {
      return memoryItem.value;
    }

    // 检查浏览器存储
    if (this.options.storage !== 'memory' && typeof window !== 'undefined') {
      try {
        const storage = this.options.storage === 'localStorage' ? localStorage : sessionStorage;
        const stored = storage.getItem(`cache_${key}`);
        if (stored) {
          const item = JSON.parse(stored);
          if (this.isValid(item)) {
            // 恢复到内存缓存
            this.cache.set(key, item);
            return item.value;
          } else {
            // 清理过期的存储项
            storage.removeItem(`cache_${key}`);
          }
        }
      } catch (error) {
        console.warn('Failed to read from storage:', error);
      }
    }

    return null;
  }

  has(key: string): boolean {
    return this.get(key) !== null;
  }

  delete(key: string): boolean {
    const deleted = this.cache.delete(key);

    if (this.options.storage !== 'memory' && typeof window !== 'undefined') {
      try {
        const storage = this.options.storage === 'localStorage' ? localStorage : sessionStorage;
        storage.removeItem(`cache_${key}`);
      } catch (error) {
        console.warn('Failed to delete from storage:', error);
      }
    }

    return deleted;
  }

  clear(): void {
    this.cache.clear();

    if (this.options.storage !== 'memory' && typeof window !== 'undefined') {
      try {
        const storage = this.options.storage === 'localStorage' ? localStorage : sessionStorage;
        const keys = Object.keys(storage).filter(key => key.startsWith('cache_'));
        keys.forEach(key => storage.removeItem(key));
      } catch (error) {
        console.warn('Failed to clear storage:', error);
      }
    }
  }

  size(): number {
    return this.cache.size;
  }

  private isValid(item: { timestamp: number; ttl: number }): boolean {
    return Date.now() - item.timestamp < item.ttl;
  }

  // 清理过期条目
  cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp >= item.ttl) {
        this.cache.delete(key);
      }
    }
  }
}

// 全局缓存实例
export const globalCache = new Cache({
  ttl: 10 * 60 * 1000, // 10分钟
  maxSize: 200,
  storage: 'localStorage',
});

// 内容缓存（用于文章、页面等）
export const contentCache = new Cache({
  ttl: 30 * 60 * 1000, // 30分钟
  maxSize: 50,
  storage: 'localStorage',
});

// 搜索结果缓存
export const searchCache = new Cache({
  ttl: 5 * 60 * 1000, // 5分钟
  maxSize: 20,
  storage: 'sessionStorage',
});

// 缓存装饰器
export function cached(cache: Cache, keyGenerator?: (...args: any[]) => string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const key = keyGenerator ? keyGenerator(...args) : `${propertyName}_${JSON.stringify(args)}`;

      // 尝试从缓存获取
      const cached = cache.get(key);
      if (cached !== null) {
        return cached;
      }

      // 执行原方法
      const result = await method.apply(this, args);

      // 缓存结果
      cache.set(key, result);

      return result;
    };
  };
}

// 定期清理过期缓存
if (typeof window !== 'undefined') {
  setInterval(
    () => {
      globalCache.cleanup();
      contentCache.cleanup();
      searchCache.cleanup();
    },
    5 * 60 * 1000
  ); // 每5分钟清理一次
}
