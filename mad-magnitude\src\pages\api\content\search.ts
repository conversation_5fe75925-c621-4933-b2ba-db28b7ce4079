/**
 * 内容搜索 API
 * 搜索内容
 */
import type { APIRoute } from 'astro';
import { contentManager } from '../../../utils/contentManager';

export const prerender = false;

export const GET: APIRoute = async ({ url }) => {
  try {
    const searchParams = new URL(url).searchParams;
    const query = searchParams.get('q');

    if (!query || query.trim().length === 0) {
      return new Response(
        JSON.stringify({
          error: 'Search query is required',
          code: 'MISSING_QUERY',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    // 搜索内容
    const results = await contentManager.searchContent(query.trim());

    // 分页处理
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;

    const paginatedResults = results.slice(offset, offset + limit);

    const response = {
      query: query.trim(),
      results: paginatedResults.map(item => ({
        id: item.id,
        collection: item.collection,
        slug: item.slug,
        title: item.title,
        description: item.description,
        publishDate: item.publishDate.toISOString(),
        updateDate: item.updateDate?.toISOString(),
        draft: item.draft,
        featured: item.featured,
        tags: item.tags,
        author: item.author,
        // 返回内容摘要，突出显示搜索词
        contentPreview: highlightSearchTerm(
          item.content.substring(0, 300) + (item.content.length > 300 ? '...' : ''),
          query.trim()
        ),
      })),
      pagination: {
        page,
        limit,
        total: results.length,
        totalPages: Math.ceil(results.length / limit),
        hasNext: offset + limit < results.length,
        hasPrev: page > 1,
      },
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=300',
      },
    });
  } catch (error) {
    console.error('内容搜索 API 错误:', error);

    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        code: 'INTERNAL_ERROR',
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      }
    );
  }
};

/**
 * 在文本中突出显示搜索词
 */
function highlightSearchTerm(text: string, searchTerm: string): string {
  const regex = new RegExp(`(${escapeRegExp(searchTerm)})`, 'gi');
  return text.replace(regex, '<mark>$1</mark>');
}

/**
 * 转义正则表达式特殊字符
 */
function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}
