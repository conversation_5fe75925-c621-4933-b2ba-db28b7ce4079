/**
 * Security configuration for the application
 * Centralizes security-related settings and policies
 */

import { getEnvConfig } from '@/utils/env.js';

export interface SecurityConfig {
  // Content Security Policy
  csp: {
    enabled: boolean;
    reportOnly: boolean;
    directives: Record<string, string[]>;
    reportUri?: string;
  };
  
  // CORS settings
  cors: {
    origin: string | string[];
    credentials: boolean;
    methods: string[];
    allowedHeaders: string[];
  };
  
  // Rate limiting
  rateLimit: {
    windowMs: number;
    maxRequests: number;
    skipSuccessfulRequests: boolean;
    skipFailedRequests: boolean;
  };
  
  // Session security
  session: {
    secure: boolean;
    httpOnly: boolean;
    sameSite: 'strict' | 'lax' | 'none';
    maxAge: number;
  };
  
  // Input validation
  validation: {
    maxInputLength: number;
    allowedFileTypes: string[];
    maxFileSize: number;
  };
  
  // Security headers
  headers: {
    hsts: {
      enabled: boolean;
      maxAge: number;
      includeSubDomains: boolean;
      preload: boolean;
    };
    noSniff: boolean;
    frameOptions: 'DENY' | 'SAMEORIGIN' | 'ALLOW-FROM';
    xssProtection: boolean;
    referrerPolicy: string;
  };
}

/**
 * Get security configuration based on environment
 */
export function getSecurityConfig(): SecurityConfig {
  const env = getEnvConfig();
  const isDev = env.NODE_ENV === 'development';
  
  return {
    csp: {
      enabled: env.CSP_ENABLED,
      reportOnly: env.CSP_REPORT_ONLY,
      directives: {
        'default-src': ["'self'"],
        'script-src': isDev 
          ? ["'self'", "'unsafe-inline'", "'unsafe-eval'"] // Allow dev tools
          : ["'self'", "'unsafe-inline'"], // Astro needs unsafe-inline for hydration
        'style-src': ["'self'", "'unsafe-inline'"], // Tailwind needs unsafe-inline
        'img-src': ["'self'", 'data:', 'https:'],
        'font-src': ["'self'", 'data:'],
        'connect-src': isDev 
          ? ["'self'", 'ws:', 'wss:'] // Allow WebSocket for dev
          : ["'self'"],
        'media-src': ["'self'"],
        'object-src': ["'none'"],
        'child-src': ["'none'"],
        'worker-src': ["'self'"],
        'frame-ancestors': ["'none'"],
        'form-action': ["'self'"],
        'base-uri': ["'self'"],
        'manifest-src': ["'self'"],
        'upgrade-insecure-requests': [],
      },
      reportUri: isDev ? undefined : '/api/csp-report',
    },
    
    cors: {
      origin: env.CORS_ORIGIN === '*' ? '*' : env.CORS_ORIGIN.split(','),
      credentials: env.CORS_CREDENTIALS,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-Requested-With',
        'Accept',
        'Origin',
      ],
    },
    
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: isDev ? 1000 : 100, // More lenient in development
      skipSuccessfulRequests: false,
      skipFailedRequests: false,
    },
    
    session: {
      secure: !isDev, // Only secure in production
      httpOnly: true,
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
    },
    
    validation: {
      maxInputLength: 10000, // 10KB
      allowedFileTypes: [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
        'image/svg+xml',
        'text/plain',
        'application/pdf',
      ],
      maxFileSize: 5 * 1024 * 1024, // 5MB
    },
    
    headers: {
      hsts: {
        enabled: !isDev,
        maxAge: 31536000, // 1 year
        includeSubDomains: true,
        preload: true,
      },
      noSniff: true,
      frameOptions: 'DENY',
      xssProtection: true,
      referrerPolicy: 'strict-origin-when-cross-origin',
    },
  };
}

/**
 * Generate CSP header value from configuration
 */
export function generateCSPHeader(config: SecurityConfig['csp']): string {
  const directives: string[] = [];
  
  Object.entries(config.directives).forEach(([directive, values]) => {
    if (values.length === 0) {
      directives.push(directive);
    } else {
      directives.push(`${directive} ${values.join(' ')}`);
    }
  });
  
  if (config.reportUri) {
    directives.push(`report-uri ${config.reportUri}`);
  }
  
  return directives.join('; ');
}

/**
 * Security policy for different content types
 */
export const CONTENT_TYPE_POLICIES = {
  'text/html': {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
  },
  'application/json': {
    'X-Content-Type-Options': 'nosniff',
  },
  'text/css': {
    'X-Content-Type-Options': 'nosniff',
  },
  'application/javascript': {
    'X-Content-Type-Options': 'nosniff',
  },
  'image/*': {
    'X-Content-Type-Options': 'nosniff',
    'Cache-Control': 'public, max-age=31536000, immutable',
  },
} as const;

/**
 * Trusted domains for external resources
 */
export const TRUSTED_DOMAINS = {
  fonts: [
    'fonts.googleapis.com',
    'fonts.gstatic.com',
  ],
  analytics: [
    'www.google-analytics.com',
    'analytics.google.com',
    'plausible.io',
  ],
  cdn: [
    'cdn.jsdelivr.net',
    'unpkg.com',
  ],
} as const;

/**
 * Security validation rules
 */
export const VALIDATION_RULES = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  url: /^https?:\/\/.+/,
  slug: /^[a-z0-9-]+$/,
  username: /^[a-zA-Z0-9_-]{3,20}$/,
  password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
} as const;

/**
 * Dangerous file extensions that should never be uploaded
 */
export const DANGEROUS_FILE_EXTENSIONS = [
  '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js', '.jar',
  '.php', '.asp', '.aspx', '.jsp', '.py', '.rb', '.pl', '.sh', '.bash',
  '.ps1', '.psm1', '.psd1', '.msi', '.dll', '.so', '.dylib',
] as const;

/**
 * Common XSS attack patterns
 */
export const XSS_PATTERNS = [
  /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
  /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
  /<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi,
  /<embed\b[^<]*>/gi,
  /<form\b[^<]*(?:(?!<\/form>)<[^<]*)*<\/form>/gi,
  /javascript:/gi,
  /vbscript:/gi,
  /data:text\/html/gi,
  /\son\w+\s*=\s*["'][^"']*["']/gi,
  /\son\w+\s*=\s*[^"'\s>]+/gi,
] as const;