---

---

<div class="search-container" role="search">
  <label for="search-input" class="sr-only">搜索文章</label>
  <div class="relative">
    <input
      type="search"
      id="search-input"
      placeholder="搜索文章..."
      class="w-full rounded-lg border border-gray-300 px-4 py-2 pr-4 pl-10 focus:border-transparent focus:ring-2 focus:ring-blue-500"
      aria-label="搜索文章"
      aria-describedby="search-help"
      aria-expanded="false"
      aria-autocomplete="list"
      aria-controls="search-results"
      autocomplete="off"
    />
    <svg
      class="absolute top-2.5 left-3 h-5 w-5 text-gray-400"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
      aria-hidden="true"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
    </svg>
  </div>

  <div id="search-help" class="sr-only">
    输入至少2个字符开始搜索。使用上下箭头键导航结果，按回车键选择。
  </div>

  <div id="search-results" class="mt-4 hidden" role="listbox" aria-label="搜索结果">
    <div class="max-h-96 overflow-y-auto rounded-lg border border-gray-200 bg-white shadow-lg">
      <!-- 搜索结果将在这里显示 -->
    </div>
  </div>

  <!-- 搜索状态提示 -->
  <div id="search-status" class="sr-only" aria-live="polite" aria-atomic="true"></div>
</div>

<script>
  import Fuse from 'fuse.js';

  // 搜索配置
  const fuseOptions = {
    keys: [
      { name: 'title', weight: 0.4 },
      { name: 'description', weight: 0.3 },
      { name: 'content', weight: 0.2 },
      { name: 'tags', weight: 0.1 },
    ],
    threshold: 0.3,
    includeScore: true,
    includeMatches: true,
  };

  let fuse: any;
  let searchIndex: any[] = [];

  // 加载搜索索引
  async function loadSearchIndex() {
    try {
      const response = await fetch('/search-index.json');
      searchIndex = await response.json();
      fuse = new Fuse(searchIndex, fuseOptions);
    } catch (error) {
      console.error('Failed to load search index:', error);
    }
  }

  // 执行搜索
  function performSearch(query: string) {
    if (!fuse || query.length < 2) {
      hideSearchResults();
      return;
    }

    const results = fuse.search(query).slice(0, 5);
    displaySearchResults(results);
  }

  // 显示搜索结果
  function displaySearchResults(results: any[]) {
    const resultsContainer = document.getElementById('search-results');
    const resultsContent = resultsContainer?.querySelector('div');
    const searchInput = document.getElementById('search-input');
    const searchStatus = document.getElementById('search-status');

    if (!resultsContainer || !resultsContent) return;

    if (results.length === 0) {
      resultsContent.innerHTML =
        '<div class="p-4 text-gray-500" role="option">未找到相关内容</div>';
      if (searchStatus) searchStatus.textContent = '未找到搜索结果';
    } else {
      resultsContent.innerHTML = results
        .map((result: any, index: number) => {
          const item = result.item;
          return `
          <a href="${item.url}" 
             class="block p-4 hover:bg-gray-50 border-b border-gray-100 last:border-b-0 focus:bg-blue-50 focus:outline-none" 
             role="option"
             aria-selected="false"
             data-index="${index}">
            <h3 class="font-semibold text-gray-900 mb-1">${highlightMatches(item.title, result.matches)}</h3>
            <p class="text-sm text-gray-600 mb-2">${highlightMatches(item.description, result.matches)}</p>
            <div class="flex items-center text-xs text-gray-500">
              <span class="bg-gray-100 px-2 py-1 rounded">${item.category}</span>
              ${item.tags
                .slice(0, 2)
                .map(
                  (tag: string) =>
                    `<span class="ml-2 bg-blue-100 text-blue-800 px-2 py-1 rounded">${tag}</span>`
                )
                .join('')}
            </div>
          </a>
        `;
        })
        .join('');

      if (searchStatus) searchStatus.textContent = `找到 ${results.length} 个搜索结果`;
    }

    resultsContainer.classList.remove('hidden');
    searchInput?.setAttribute('aria-expanded', 'true');
  }

  // 高亮匹配文本
  function highlightMatches(text: string, matches: any[]) {
    if (!matches) return text;

    let highlightedText = text;
    matches.forEach((match: any) => {
      if (match.key === 'title' || match.key === 'description') {
        match.indices.forEach(([start, end]: [number, number]) => {
          const matchedText = text.substring(start, end + 1);
          highlightedText = highlightedText.replace(
            matchedText,
            `<mark class="bg-yellow-200">${matchedText}</mark>`
          );
        });
      }
    });

    return highlightedText;
  }

  // 隐藏搜索结果
  function hideSearchResults() {
    const resultsContainer = document.getElementById('search-results');
    const searchInput = document.getElementById('search-input');
    resultsContainer?.classList.add('hidden');
    searchInput?.setAttribute('aria-expanded', 'false');
  }

  // 初始化搜索功能
  document.addEventListener('DOMContentLoaded', () => {
    loadSearchIndex();

    const searchInput = document.getElementById('search-input') as HTMLInputElement;
    let searchTimeout: ReturnType<typeof setTimeout>;
    let selectedIndex = -1;

    searchInput?.addEventListener('input', (e: Event) => {
      const target = e.target as HTMLInputElement;
      const query = target.value;

      clearTimeout(searchTimeout);
      searchTimeout = setTimeout(() => {
        performSearch(query);
        selectedIndex = -1;
      }, 300);
    });

    // 键盘导航支持
    searchInput?.addEventListener('keydown', (e: KeyboardEvent) => {
      const resultsContainer = document.getElementById('search-results');
      const results = resultsContainer?.querySelectorAll('[role="option"]');

      if (!results || results.length === 0) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          selectedIndex = Math.min(selectedIndex + 1, results.length - 1);
          updateSelection(results, selectedIndex);
          break;
        case 'ArrowUp':
          e.preventDefault();
          selectedIndex = Math.max(selectedIndex - 1, -1);
          updateSelection(results, selectedIndex);
          break;
        case 'Enter':
          e.preventDefault();
          if (selectedIndex >= 0 && results[selectedIndex]) {
            const link = results[selectedIndex] as HTMLAnchorElement;
            window.location.href = link.href;
          }
          break;
        case 'Escape':
          hideSearchResults();
          selectedIndex = -1;
          break;
      }
    });

    // 更新选中状态
    function updateSelection(results: NodeListOf<Element>, index: number) {
      results.forEach((result, i) => {
        const isSelected = i === index;
        result.setAttribute('aria-selected', isSelected.toString());
        if (isSelected) {
          result.scrollIntoView({ block: 'nearest' });
        }
      });
    }

    // 点击外部隐藏搜索结果
    document.addEventListener('click', (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      const searchContainer = document.querySelector('.search-container');
      if (!searchContainer?.contains(target)) {
        hideSearchResults();
        selectedIndex = -1;
      }
    });
  });
</script>
