import type { CollectionEntry } from 'astro:content';

export interface ContentMeta {
  readingTime: number;
  wordCount: number;
  lastModified: Date;
  relatedPosts: CollectionEntry<'research' | 'reflections'>[];
}

export interface SearchIndex {
  id: string;
  title: string;
  description: string;
  content: string;
  tags: string[];
  category: string;
  url: string;
  publishDate: string;
}

// 计算阅读时间
export function calculateReadingTime(content: string): number {
  const wordsPerMinute = 200; // 中文阅读速度
  const wordCount = content.length;
  return Math.ceil(wordCount / wordsPerMinute);
}

// 提取内容摘要
export function extractExcerpt(content: string, maxLength: number = 200): string {
  const plainText = content.replace(/[#*`\[\]]/g, '').trim();
  return plainText.length > maxLength
    ? plainText.substring(0, maxLength) + '...'
    : plainText;
}

// 生成相关文章
export async function getRelatedPosts(
  currentPost: CollectionEntry<'research' | 'reflections'>,
  allPosts: CollectionEntry<'research' | 'reflections'>[],
  limit: number = 3
): Promise<CollectionEntry<'research' | 'reflections'>[]> {
  const currentTags = currentPost.data.tags;

  const scored = allPosts
    .filter(post => post.id !== currentPost.id && !post.data.draft)
    .map(post => {
      const commonTags = post.data.tags.filter(tag => currentTags.includes(tag));
      const score = commonTags.length;
      return { post, score };
    })
    .filter(item => item.score > 0)
    .sort((a, b) => b.score - a.score)
    .slice(0, limit);

  return scored.map(item => item.post);
}

// 构建搜索索引
export function buildSearchIndex(
  posts: CollectionEntry<'research' | 'reflections'>[]
): SearchIndex[] {
  return posts
    .filter(post => !post.data.draft)
    .map(post => ({
      id: post.id,
      title: post.data.title.zh,
      description: post.data.description.zh,
      content: post.body,
      tags: post.data.tags,
      category: post.collection,
      url: `/${post.collection}/${post.slug}`,
      publishDate: post.data.publishDate.toISOString(),
    }));
}
