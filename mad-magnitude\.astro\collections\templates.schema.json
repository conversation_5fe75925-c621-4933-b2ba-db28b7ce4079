{"$ref": "#/definitions/templates", "definitions": {"templates": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "category": {"type": "string", "enum": ["layout", "component", "page", "example"]}, "tags": {"type": "array", "items": {"type": "string"}, "default": []}, "updateDate": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "string", "format": "date"}, {"type": "integer", "format": "unix-time"}]}, "draft": {"type": "boolean", "default": false}, "$schema": {"type": "string"}}, "required": ["title", "description", "category", "updateDate"], "additionalProperties": false}}, "$schema": "http://json-schema.org/draft-07/schema#"}