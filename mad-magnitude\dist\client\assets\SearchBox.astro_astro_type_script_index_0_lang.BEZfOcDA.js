import{F as g}from"./vendor-search.Ch1WBRTM.js";const m={keys:[{name:"title",weight:.4},{name:"description",weight:.3},{name:"content",weight:.2},{name:"tags",weight:.1}],threshold:.3,includeScore:!0,includeMatches:!0};let l,u=[];async function p(){try{u=await(await fetch("/search-index.json")).json(),l=new g(u,m)}catch(t){console.error("Failed to load search index:",t)}}function f(t){if(!l||t.length<2){d();return}const r=l.search(t).slice(0,5);y(r)}function y(t){const r=document.getElementById("search-results"),e=r?.querySelector("div"),c=document.getElementById("search-input"),s=document.getElementById("search-status");!r||!e||(t.length===0?(e.innerHTML='<div class="p-4 text-gray-500" role="option">未找到相关内容</div>',s&&(s.textContent="未找到搜索结果")):(e.innerHTML=t.map((a,n)=>{const o=a.item;return`
          <a href="${o.url}" 
             class="block p-4 hover:bg-gray-50 border-b border-gray-100 last:border-b-0 focus:bg-blue-50 focus:outline-none" 
             role="option"
             aria-selected="false"
             data-index="${n}">
            <h3 class="font-semibold text-gray-900 mb-1">${h(o.title,a.matches)}</h3>
            <p class="text-sm text-gray-600 mb-2">${h(o.description,a.matches)}</p>
            <div class="flex items-center text-xs text-gray-500">
              <span class="bg-gray-100 px-2 py-1 rounded">${o.category}</span>
              ${o.tags.slice(0,2).map(i=>`<span class="ml-2 bg-blue-100 text-blue-800 px-2 py-1 rounded">${i}</span>`).join("")}
            </div>
          </a>
        `}).join(""),s&&(s.textContent=`找到 ${t.length} 个搜索结果`)),r.classList.remove("hidden"),c?.setAttribute("aria-expanded","true"))}function h(t,r){if(!r)return t;let e=t;return r.forEach(c=>{(c.key==="title"||c.key==="description")&&c.indices.forEach(([s,a])=>{const n=t.substring(s,a+1);e=e.replace(n,`<mark class="bg-yellow-200">${n}</mark>`)})}),e}function d(){const t=document.getElementById("search-results"),r=document.getElementById("search-input");t?.classList.add("hidden"),r?.setAttribute("aria-expanded","false")}document.addEventListener("DOMContentLoaded",()=>{p();const t=document.getElementById("search-input");let r,e=-1;t?.addEventListener("input",s=>{const n=s.target.value;clearTimeout(r),r=setTimeout(()=>{f(n),e=-1},300)}),t?.addEventListener("keydown",s=>{const n=document.getElementById("search-results")?.querySelectorAll('[role="option"]');if(!(!n||n.length===0))switch(s.key){case"ArrowDown":s.preventDefault(),e=Math.min(e+1,n.length-1),c(n,e);break;case"ArrowUp":s.preventDefault(),e=Math.max(e-1,-1),c(n,e);break;case"Enter":if(s.preventDefault(),e>=0&&n[e]){const o=n[e];window.location.href=o.href}break;case"Escape":d(),e=-1;break}});function c(s,a){s.forEach((n,o)=>{const i=o===a;n.setAttribute("aria-selected",i.toString()),i&&n.scrollIntoView({block:"nearest"})})}document.addEventListener("click",s=>{const a=s.target;document.querySelector(".search-container")?.contains(a)||(d(),e=-1)})});
