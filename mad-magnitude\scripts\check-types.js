#!/usr/bin/env node

/**
 * 简单的类型检查脚本
 * 检查常见的 TypeScript 错误模式
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const SRC_DIR = path.join(__dirname, '../src');

// 常见的类型错误模式
const ERROR_PATTERNS = [
  {
    pattern: /参数.*隐式具有.*any.*类型/,
    description: '参数隐式具有 any 类型',
    severity: 'error',
  },
  {
    pattern: /元素隐式具有.*any.*类型/,
    description: '元素隐式具有 any 类型',
    severity: 'error',
  },
  {
    pattern: /算术运算.*必须是.*number/,
    description: '算术运算类型错误',
    severity: 'error',
  },
  {
    pattern: /getCollection\([^)]*\)\.filter\(\s*\w+\s*=>/,
    description: '可能的 getCollection 类型问题',
    severity: 'warning',
  },
];

/**
 * 扫描文件中的类型错误
 */
function scanFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const issues = [];

    // 检查常见的类型错误模式
    const lines = content.split('\n');
    lines.forEach((line, index) => {
      ERROR_PATTERNS.forEach(errorPattern => {
        if (errorPattern.pattern.test(line)) {
          issues.push({
            line: index + 1,
            content: line.trim(),
            type: errorPattern.description,
            severity: errorPattern.severity,
          });
        }
      });
    });

    return issues;
  } catch (error) {
    return [{ error: error.message }];
  }
}

/**
 * 递归扫描目录
 */
function scanDirectory(dir) {
  const results = [];

  try {
    const items = fs.readdirSync(dir);

    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        // 跳过 node_modules 和 .git 等目录
        if (!['node_modules', '.git', 'dist', '.astro'].includes(item)) {
          results.push(...scanDirectory(fullPath));
        }
      } else if (item.endsWith('.astro') || item.endsWith('.ts') || item.endsWith('.tsx')) {
        const issues = scanFile(fullPath);
        if (issues.length > 0) {
          results.push({
            file: path.relative(SRC_DIR, fullPath),
            issues,
          });
        }
      }
    }
  } catch (error) {
    console.error(`扫描目录失败: ${dir}`, error.message);
  }

  return results;
}

/**
 * 生成报告
 */
function generateReport(results) {
  console.log('\n🔍 TypeScript 类型检查报告');
  console.log('='.repeat(50));

  if (results.length === 0) {
    console.log('\n✅ 没有发现类型错误！');
    return;
  }

  let totalErrors = 0;
  let totalWarnings = 0;

  results.forEach(result => {
    console.log(`\n📄 ${result.file}`);
    console.log('-'.repeat(30));

    result.issues.forEach(issue => {
      if (issue.error) {
        console.log(`   ❌ 文件读取错误: ${issue.error}`);
      } else {
        const icon = issue.severity === 'error' ? '❌' : '⚠️';
        console.log(`   ${icon} 第${issue.line}行: ${issue.type}`);
        console.log(`      ${issue.content}`);

        if (issue.severity === 'error') {
          totalErrors++;
        } else {
          totalWarnings++;
        }
      }
    });
  });

  console.log('\n📊 统计信息');
  console.log('-'.repeat(20));
  console.log(`❌ 错误: ${totalErrors}`);
  console.log(`⚠️  警告: ${totalWarnings}`);
  console.log(`📁 文件: ${results.length}`);

  if (totalErrors > 0) {
    console.log('\n💡 建议:');
    console.log('- 为函数参数添加显式的类型注解');
    console.log('- 使用类型断言 (as Type) 来明确类型');
    console.log('- 检查日期比较时使用 .getTime() 方法');
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 开始扫描 TypeScript 类型错误...');

  const results = scanDirectory(SRC_DIR);
  generateReport(results);

  // 如果有错误，退出码为1
  const hasErrors = results.some(result => result.issues.some(issue => issue.severity === 'error'));

  process.exit(hasErrors ? 1 : 0);
}

// 运行脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
