---
inclusion: manual
---

# 内容创建指南

## 内容类型和用途

### 动态资讯 (news)

**用途**: 发布研究动态、重要公告、个人思考和项目里程碑
**特点**: 时效性强，更新频繁 **格式要求**:

```yaml
type: research | announcement | reflection | milestone
relatedInstitute: [economics, philosophy, internet, ai, future]
```

### 研究日志 (logs)

**用途**: 记录日常研究过程、思考片段、实验记录
**特点**: 个人化，非正式，记录研究过程 **格式要求**:

```yaml
date: 2025-01-14
mood: thoughtful | critical | optimistic | analytical
relatedInstitute: [economics, philosophy, internet, ai, future]
```

### 研究报告 (research)

**用途**: 正式的研究成果、深度分析、学术论文 **特点**: 结构化，权威性，引用规范
**格式要求**:

```yaml
category: ai | education | philosophy | technology
readingTime: number (分钟)
```

### 研究所内容

**经济研究所 (economics)**:

```yaml
analysisType: market | policy | theory | data
dataSource: string
```

**哲学研究所 (philosophy)**:

```yaml
philosophyBranch: ethics | metaphysics | epistemology | logic | aesthetics
thinkers: [string]
```

**互联网研究所 (internet)**:

```yaml
industry: social | ecommerce | fintech | education | entertainment
companies: [string]
```

**AI 研究所 (ai)**:

```yaml
aiField: ml | nlp | cv | robotics | ethics | agi
techStack: [string]
models: [string]
```

**未来研究所 (future)**:

```yaml
timeHorizon: short | medium | long
domains: [string]
confidence: low | medium | high
```

### 产品发布 (products)

**用途**: 发布项目、工具、应用程序 **特点**: 实用性，可访问的演示链接
**格式要求**:

```yaml
demo: url (可选)
```

## 内容创建最佳实践

### 标题规范

- **中文标题**: 简洁明了，突出核心观点
- **英文标题**: 可选，用于国际化
- 避免过长的标题 (建议 < 50 字符)
- 使用动词开头增加吸引力

### 描述规范

- **中文描述**: 2-3 句话概括内容要点
- **英文描述**: 可选，与中文对应
- 包含关键词，有利于搜索
- 避免与标题重复

### 标签使用

- 每篇内容 3-8 个标签
- 使用一致的标签词汇
- 包含技术标签和主题标签
- 避免过于宽泛的标签

**推荐标签分类**:

- **技术类**: AI, 机器学习, 区块链, 云计算
- **领域类**: 经济学, 哲学, 教育, 社会学
- **方法类**: 数据分析, 理论研究, 实证研究
- **主题类**: 创新, 伦理, 未来, 治理

### 内容结构

1. **引言**: 背景介绍，问题提出
2. **主体**: 分段论述，逻辑清晰
3. **结论**: 总结观点，提出展望
4. **参考**: 引用来源，延伸阅读

### 学术写作规范

- 使用客观、准确的语言
- 提供数据和事实支撑
- 引用权威来源
- 避免主观臆断
- 保持逻辑一致性

## 多媒体内容

### 数学公式

使用 KaTeX 语法:

```latex
$E = mc^2$

inline math: $x = \frac{-b \pm \sqrt{b^2-4ac}}{2a}$
```

### 图表和图形

使用 Mermaid 语法:

```mermaid
graph TD
    A[开始] --> B{条件判断}
    B -->|是| C[执行操作]
    B -->|否| D[结束]
    C --> D
```

### 代码示例

使用语法高亮:

```typescript
interface User {
  id: string;
  name: string;
  email: string;
}

function createUser(data: Partial<User>): User {
  return {
    id: generateId(),
    ...data,
  } as User;
}
```

### 图片使用

- 使用描述性的文件名
- 提供 alt 文本
- 优化文件大小
- 使用适当的格式 (WebP/AVIF)

## 内容审核清单

### 发布前检查

- [ ] 标题和描述准确
- [ ] 标签合适且一致
- [ ] 语法和拼写正确
- [ ] 链接有效
- [ ] 图片优化
- [ ] 数学公式渲染正确
- [ ] 代码示例可运行
- [ ] 引用格式规范

### SEO 优化

- [ ] 包含目标关键词
- [ ] 元描述吸引人
- [ ] 内部链接合理
- [ ] 外部链接权威
- [ ] 图片 alt 属性完整

### 可访问性检查

- [ ] 标题层级正确
- [ ] 颜色对比度足够
- [ ] 链接文本描述性
- [ ] 表格有标题
- [ ] 列表结构清晰

## 内容维护

### 定期更新

- 检查外部链接有效性
- 更新过时的信息
- 添加新的相关内容
- 修正发现的错误

### 版本控制

- 记录重要修改
- 保留修改历史
- 标注更新日期
- 说明修改原因

### 内容归档

- 定期整理旧内容
- 合并相似主题
- 删除过时信息
- 创建内容索引
