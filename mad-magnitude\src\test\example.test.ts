/**
 * 示例测试文件
 *
 * 验证测试配置是否正确工作
 */

import { describe, expect, it, vi } from 'vitest';
import {
  createTestElement,
  expectToBeValidUrl,
  expectToHaveValidHtml,
  mockFetchResponse,
  mockLocalStorage,
  waitForAsync,
} from './utils';

describe('测试配置验证', () => {
  it('应该正确设置测试环境', () => {
    expect(process.env.NODE_ENV).toBe('test');
    expect(global.IntersectionObserver).toBeDefined();
    expect(global.ResizeObserver).toBeDefined();
    expect(global.fetch).toBeDefined();
  });

  it('应该支持 vi 全局变量', () => {
    expect(vi).toBeDefined();
    expect(vi.fn).toBeDefined();
    expect(vi.mock).toBeDefined();
  });

  it('应该支持 DOM 操作', () => {
    const element = document.createElement('div');
    element.textContent = 'Test Element';
    document.body.appendChild(element);

    expect(document.body.children.length).toBe(1);
    expect(element.textContent).toBe('Test Element');
  });
});

describe('测试工具函数', () => {
  describe('URL 验证', () => {
    it('应该验证有效的 URL', () => {
      expectToBeValidUrl('https://example.com');
      expectToBeValidUrl('http://localhost:3000');
    });

    it('应该拒绝无效的 URL', () => {
      expect(() => expectToBeValidUrl('invalid-url')).toThrow();
      expect(() => expectToBeValidUrl('ftp://example.com')).toThrow();
    });
  });

  describe('HTML 验证', () => {
    it('应该验证有效的 HTML 结构', () => {
      const validHtml = `
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <title>Test</title>
        </head>
        <body>
          <h1>Hello World</h1>
        </body>
        </html>
      `;

      expectToHaveValidHtml(validHtml);
    });

    it('应该拒绝无效的 HTML 结构', () => {
      const invalidHtml = '<div>Just a div</div>';

      expect(() => expectToHaveValidHtml(invalidHtml)).toThrow();
    });
  });

  describe('异步工具', () => {
    it('应该支持异步等待', async () => {
      const start = Date.now();
      await waitForAsync(10);
      const end = Date.now();

      expect(end - start).toBeGreaterThanOrEqual(10);
    });
  });

  describe('模拟工具', () => {
    it('应该创建模拟的 fetch 响应', async () => {
      const mockData = { message: 'Hello World' };
      const mockFetch = mockFetchResponse(mockData);

      global.fetch = mockFetch;

      const response = await fetch('/api/test');
      const data = await response.json();

      expect(data).toEqual(mockData);
      expect(mockFetch).toHaveBeenCalledWith('/api/test');
    });

    it('应该创建测试 DOM 元素', () => {
      const element = createTestElement('button', {
        'data-testid': 'test-button',
        class: 'btn btn-primary',
      });

      expect(element.tagName).toBe('BUTTON');
      expect(element.getAttribute('data-testid')).toBe('test-button');
      expect(element.getAttribute('class')).toBe('btn btn-primary');
    });

    it('应该模拟 localStorage', () => {
      const mockStorage = mockLocalStorage();

      mockStorage.setItem('test-key', 'test-value');
      expect(mockStorage.getItem('test-key')).toBe('test-value');

      mockStorage.removeItem('test-key');
      expect(mockStorage.getItem('test-key')).toBeNull();

      expect(mockStorage.length).toBe(0);
    });
  });
});

describe('路径别名测试', () => {
  it('应该支持 @ 路径别名', async () => {
    // 这个测试验证路径别名配置是否正确
    // 在实际项目中，你可以导入使用别名的模块来测试
    expect(true).toBe(true); // 占位测试
  });
});

describe('TypeScript 支持测试', () => {
  it('应该支持 TypeScript 类型检查', () => {
    interface TestInterface {
      id: number;
      name: string;
    }

    const testObject: TestInterface = {
      id: 1,
      name: 'Test Object',
    };

    expect(testObject.id).toBe(1);
    expect(testObject.name).toBe('Test Object');
  });

  it('应该支持泛型函数', () => {
    function identity<T>(arg: T): T {
      return arg;
    }

    const stringResult = identity('hello');
    const numberResult = identity(42);

    expect(stringResult).toBe('hello');
    expect(numberResult).toBe(42);
  });
});

describe('模拟和间谍功能', () => {
  it('应该支持函数模拟', () => {
    const mockFn = vi.fn();
    mockFn('test-arg');

    expect(mockFn).toHaveBeenCalledWith('test-arg');
    expect(mockFn).toHaveBeenCalledTimes(1);
  });

  it('应该支持模块模拟', () => {
    const mockConsole = vi.spyOn(console, 'log').mockImplementation(() => {});

    console.log('test message');

    expect(mockConsole).toHaveBeenCalledWith('test message');

    mockConsole.mockRestore();
  });

  it('应该支持定时器模拟', () => {
    vi.useFakeTimers();

    const callback = vi.fn();
    setTimeout(callback, 1000);

    expect(callback).not.toHaveBeenCalled();

    vi.advanceTimersByTime(1000);

    expect(callback).toHaveBeenCalledTimes(1);

    vi.useRealTimers();
  });
});
