import {
  createSecurityMiddleware,
  generateSecureToken,
  getSecurityHeaders,
  isValidEmail,
  isValidUrl,
  rateLimiter,
  sanitizeHtml,
  sanitizeText,
  simpleHash,
  validateFormInput,
} from '@/utils/security';
import { beforeEach, describe, expect, it } from 'vitest';

describe('Security Utils', () => {
  describe('sanitizeHtml', () => {
    it('should remove script tags', () => {
      const input = '<div>Hello <script>alert("xss")</script> World</div>';
      const result = sanitizeHtml(input);
      expect(result).toBe('<div>Hello  World</div>');
    });

    it('should remove iframe tags', () => {
      const input = '<div>Content <iframe src="evil.com"></iframe> More</div>';
      const result = sanitizeHtml(input);
      expect(result).toBe('<div>Content  More</div>');
    });

    it('should remove event handlers', () => {
      const input = '<div onclick="alert(1)">Click me</div>';
      const result = sanitizeHtml(input);
      expect(result).toBe('<div>Click me</div>');
    });

    it('should remove javascript: protocols', () => {
      const input = '<a href="javascript:alert(1)">Link</a>';
      const result = sanitizeHtml(input);
      expect(result).toBe('<a href="alert(1)">Link</a>');
    });

    it('should handle empty input', () => {
      expect(sanitizeHtml('')).toBe('');
      expect(sanitizeHtml(null as any)).toBe('');
      expect(sanitizeHtml(undefined as any)).toBe('');
    });
  });

  describe('sanitizeText', () => {
    it('should remove angle brackets', () => {
      const input = 'Hello <script>alert(1)</script> World';
      const result = sanitizeText(input);
      expect(result).toBe('Hello scriptalert(1)/script World');
    });

    it('should remove dangerous protocols', () => {
      const input = 'javascript:alert(1) and vbscript:msgbox(1)';
      const result = sanitizeText(input);
      expect(result).toBe('alert(1) and msgbox(1)');
    });

    it('should trim whitespace', () => {
      const input = '  Hello World  ';
      const result = sanitizeText(input);
      expect(result).toBe('Hello World');
    });
  });

  describe('isValidEmail', () => {
    it('should validate correct email addresses', () => {
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
    });

    it('should reject invalid email addresses', () => {
      expect(isValidEmail('invalid-email')).toBe(false);
      expect(isValidEmail('@example.com')).toBe(false);
      expect(isValidEmail('test@')).toBe(false);
      expect(isValidEmail('')).toBe(false);
      expect(isValidEmail(null as any)).toBe(false);
    });

    it('should reject overly long email addresses', () => {
      const longEmail = 'a'.repeat(250) + '@example.com';
      expect(isValidEmail(longEmail)).toBe(false);
    });
  });

  describe('isValidUrl', () => {
    it('should validate correct URLs', () => {
      expect(isValidUrl('https://example.com')).toBe(true);
      expect(isValidUrl('http://localhost:3000')).toBe(true);
      expect(isValidUrl('https://sub.domain.com/path?query=1')).toBe(true);
    });

    it('should reject invalid URLs', () => {
      expect(isValidUrl('not-a-url')).toBe(false);
      expect(isValidUrl('ftp://example.com')).toBe(false);
      expect(isValidUrl('javascript:alert(1)')).toBe(false);
      expect(isValidUrl('')).toBe(false);
      expect(isValidUrl(null as any)).toBe(false);
    });
  });

  describe('validateFormInput', () => {
    it('should validate required fields', () => {
      const result = validateFormInput('', { required: true });
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('This field is required');
    });

    it('should validate minimum length', () => {
      const result = validateFormInput('ab', { minLength: 3 });
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Minimum length is 3 characters');
    });

    it('should validate maximum length', () => {
      const result = validateFormInput('abcdef', { maxLength: 3 });
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Maximum length is 3 characters');
      expect(result.sanitizedValue).toBe('abc');
    });

    it('should validate email type', () => {
      const result = validateFormInput('invalid-email', { type: 'email' });
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid email format');
    });

    it('should validate URL type', () => {
      const result = validateFormInput('not-a-url', { type: 'url' });
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid URL format');
    });

    it('should validate pattern', () => {
      const result = validateFormInput('abc123', { pattern: /^\d+$/ });
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid format');
    });

    it('should pass valid input', () => {
      const result = validateFormInput('<EMAIL>', {
        required: true,
        type: 'email',
        minLength: 5,
        maxLength: 50,
      });
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.sanitizedValue).toBe('<EMAIL>');
    });
  });

  describe('generateSecureToken', () => {
    it('should generate token of specified length', () => {
      const token = generateSecureToken(16);
      expect(token).toHaveLength(16);
    });

    it('should generate different tokens', () => {
      const token1 = generateSecureToken();
      const token2 = generateSecureToken();
      expect(token1).not.toBe(token2);
    });

    it('should use default length', () => {
      const token = generateSecureToken();
      expect(token).toHaveLength(32);
    });
  });

  describe('simpleHash', () => {
    it('should generate consistent hash for same input', () => {
      const hash1 = simpleHash('test');
      const hash2 = simpleHash('test');
      expect(hash1).toBe(hash2);
    });

    it('should generate different hashes for different inputs', () => {
      const hash1 = simpleHash('test1');
      const hash2 = simpleHash('test2');
      expect(hash1).not.toBe(hash2);
    });

    it('should handle empty string', () => {
      const hash = simpleHash('');
      expect(hash).toBe('0');
    });
  });

  describe('getSecurityHeaders', () => {
    it('should return security headers', () => {
      const headers = getSecurityHeaders();

      expect(headers['X-Content-Type-Options']).toBe('nosniff');
      expect(headers['X-Frame-Options']).toBe('DENY');
      expect(headers['X-XSS-Protection']).toBe('1; mode=block');
      expect(headers['Referrer-Policy']).toBe('strict-origin-when-cross-origin');
      expect(headers['Permissions-Policy']).toContain('camera=()');
    });

    it('should include CSP when enabled', () => {
      // This test assumes CSP is enabled by default
      const headers = getSecurityHeaders();

      // Check if either CSP or CSP-Report-Only is present
      const hasCSP =
        headers['Content-Security-Policy'] || headers['Content-Security-Policy-Report-Only'];

      if (hasCSP) {
        const cspValue =
          headers['Content-Security-Policy'] || headers['Content-Security-Policy-Report-Only'];
        expect(cspValue).toContain("default-src 'self'");
        expect(cspValue).toContain("script-src 'self'");
      }
    });
  });

  describe('rateLimiter', () => {
    beforeEach(() => {
      // Reset rate limiter for each test
      rateLimiter.reset('test-client');
    });

    it('should allow requests within limit', () => {
      expect(rateLimiter.isAllowed('test-client')).toBe(true);
      expect(rateLimiter.isAllowed('test-client')).toBe(true);
    });

    it('should reset client requests', () => {
      rateLimiter.isAllowed('test-client');
      rateLimiter.reset('test-client');
      expect(rateLimiter.isAllowed('test-client')).toBe(true);
    });
  });

  describe('createSecurityMiddleware', () => {
    it('should create middleware with security functions', () => {
      const middleware = createSecurityMiddleware();

      expect(middleware.applyHeaders).toBeDefined();
      expect(middleware.checkRateLimit).toBeDefined();
      expect(middleware.validateOrigin).toBeDefined();
    });

    it('should apply security headers to response', () => {
      const middleware = createSecurityMiddleware();
      const mockResponse = new Response('test', {
        headers: new Headers(),
      });

      const secureResponse = middleware.applyHeaders(mockResponse);

      expect(secureResponse.headers.get('X-Content-Type-Options')).toBe('nosniff');
      expect(secureResponse.headers.get('X-Frame-Options')).toBe('DENY');
    });

    it('should validate request origin', () => {
      const middleware = createSecurityMiddleware();

      // Mock request with valid origin
      const validRequest = new Request('https://example.com', {
        headers: {
          origin: 'https://pennfly.com',
        },
      });

      // Mock request with invalid origin
      const invalidRequest = new Request('https://example.com', {
        headers: {
          origin: 'https://evil.com',
        },
      });

      // Note: These tests depend on environment configuration
      // In a real scenario, you'd mock the environment
      expect(typeof middleware.validateOrigin(validRequest)).toBe('boolean');
      expect(typeof middleware.validateOrigin(invalidRequest)).toBe('boolean');
    });
  });

  describe('Advanced XSS Protection', () => {
    it('should handle complex XSS attempts', () => {
      const maliciousInputs = [
        '<img src="x" onerror="alert(1)">',
        '<svg onload="alert(1)">',
        '<iframe src="javascript:alert(1)"></iframe>',
        '<object data="javascript:alert(1)"></object>',
        '<embed src="javascript:alert(1)">',
        '<form><input type="submit" formaction="javascript:alert(1)"></form>',
        '<a href="javascript:alert(1)">Click</a>',
        '<div style="background:url(javascript:alert(1))">Test</div>',
        '"><script>alert(1)</script>',
        "';alert(1);//",
        '<script>eval("alert(1)")</script>',
      ];

      maliciousInputs.forEach(input => {
        const sanitized = sanitizeHtml(input);
        expect(sanitized).not.toContain('script');
        expect(sanitized).not.toContain('javascript:');
        expect(sanitized).not.toContain('onerror');
        expect(sanitized).not.toContain('onload');
        expect(sanitized).not.toContain('eval');
      });
    });

    it('should preserve safe HTML', () => {
      const safeInputs = [
        '<p>This is safe text</p>',
        '<div class="container"><h1>Title</h1></div>',
        '<a href="https://example.com">Safe link</a>',
        '<img src="image.jpg" alt="Safe image">',
        '<strong>Bold text</strong>',
        '<em>Italic text</em>',
      ];

      safeInputs.forEach(input => {
        const sanitized = sanitizeHtml(input);
        // Should preserve basic structure while removing dangerous attributes
        expect(sanitized.length).toBeGreaterThan(0);
      });
    });
  });

  describe('Input Validation Edge Cases', () => {
    it('should handle null and undefined inputs', () => {
      expect(sanitizeHtml(null as any)).toBe('');
      expect(sanitizeHtml(undefined as any)).toBe('');
      expect(sanitizeText(null as any)).toBe('');
      expect(sanitizeText(undefined as any)).toBe('');
      expect(isValidEmail(null as any)).toBe(false);
      expect(isValidEmail(undefined as any)).toBe(false);
      expect(isValidUrl(null as any)).toBe(false);
      expect(isValidUrl(undefined as any)).toBe(false);
    });

    it('should handle non-string inputs', () => {
      expect(sanitizeHtml(123 as any)).toBe('');
      expect(sanitizeText(123 as any)).toBe('');
      expect(isValidEmail(123 as any)).toBe(false);
      expect(isValidUrl(123 as any)).toBe(false);
    });

    it('should handle very long inputs', () => {
      const longString = 'a'.repeat(10000);
      const sanitized = sanitizeText(longString);
      expect(sanitized.length).toBeLessThanOrEqual(longString.length);
    });
  });

  describe('Security Headers Configuration', () => {
    it('should include all required security headers', () => {
      const headers = getSecurityHeaders();

      const requiredHeaders = [
        'X-Content-Type-Options',
        'X-Frame-Options',
        'X-XSS-Protection',
        'Referrer-Policy',
        'Permissions-Policy',
      ];

      requiredHeaders.forEach(header => {
        expect(headers[header as keyof typeof headers]).toBeDefined();
      });
    });

    it('should have secure default values', () => {
      const headers = getSecurityHeaders();

      expect(headers['X-Content-Type-Options']).toBe('nosniff');
      expect(headers['X-Frame-Options']).toBe('DENY');
      expect(headers['X-XSS-Protection']).toBe('1; mode=block');
      expect(headers['Referrer-Policy']).toBe('strict-origin-when-cross-origin');
    });

    it('should include CSP header when enabled', () => {
      const headers = getSecurityHeaders();

      // Check if either CSP or CSP-Report-Only is present
      const hasCSP =
        headers['Content-Security-Policy'] || headers['Content-Security-Policy-Report-Only'];

      if (hasCSP) {
        const cspValue =
          headers['Content-Security-Policy'] || headers['Content-Security-Policy-Report-Only'];
        expect(cspValue).toContain("default-src 'self'");
        expect(cspValue).toContain("script-src 'self'");
        expect(cspValue).toContain("style-src 'self'");
        expect(cspValue).toContain("object-src 'none'");
        expect(cspValue).toContain("frame-ancestors 'none'");
      }
    });

    it('should include HSTS header in production', () => {
      const headers = getSecurityHeaders();

      // HSTS should be present in production (when not in dev mode)
      if (headers['Strict-Transport-Security']) {
        expect(headers['Strict-Transport-Security']).toContain('max-age=');
        expect(headers['Strict-Transport-Security']).toContain('includeSubDomains');
        expect(headers['Strict-Transport-Security']).toContain('preload');
      }
    });

    it('should include comprehensive Permissions Policy', () => {
      const headers = getSecurityHeaders();

      expect(headers['Permissions-Policy']).toContain('camera=()');
      expect(headers['Permissions-Policy']).toContain('microphone=()');
      expect(headers['Permissions-Policy']).toContain('geolocation=()');
      expect(headers['Permissions-Policy']).toContain('interest-cohort=()');
    });
  });

  describe('Token Generation Security', () => {
    it('should generate cryptographically secure tokens', () => {
      const tokens = new Set();
      const iterations = 100;

      // Generate multiple tokens to check for uniqueness
      for (let i = 0; i < iterations; i++) {
        const token = generateSecureToken(16);
        expect(token).toHaveLength(16);
        expect(tokens.has(token)).toBe(false); // Should be unique
        tokens.add(token);
      }

      expect(tokens.size).toBe(iterations);
    });

    it('should use only safe characters', () => {
      const token = generateSecureToken(100);
      const safeCharPattern = /^[A-Za-z0-9]+$/;
      expect(safeCharPattern.test(token)).toBe(true);
    });
  });

  describe('Security Middleware Integration', () => {
    it('should create middleware with all required functions', () => {
      const middleware = createSecurityMiddleware();

      expect(middleware.applyHeaders).toBeDefined();
      expect(middleware.checkRateLimit).toBeDefined();
      expect(middleware.validateOrigin).toBeDefined();
      expect(typeof middleware.applyHeaders).toBe('function');
      expect(typeof middleware.checkRateLimit).toBe('function');
      expect(typeof middleware.validateOrigin).toBe('function');
    });

    it('should apply security headers correctly', () => {
      const middleware = createSecurityMiddleware();
      const mockResponse = new Response('test content', {
        status: 200,
        headers: new Headers({
          'Content-Type': 'text/plain',
        }),
      });

      const secureResponse = middleware.applyHeaders(mockResponse);

      // Check that security headers are applied
      expect(secureResponse.headers.get('X-Content-Type-Options')).toBe('nosniff');
      expect(secureResponse.headers.get('X-Frame-Options')).toBe('DENY');
      expect(secureResponse.headers.get('X-XSS-Protection')).toBe('1; mode=block');
      expect(secureResponse.headers.get('Referrer-Policy')).toBe('strict-origin-when-cross-origin');

      // Check that original headers are preserved
      expect(secureResponse.headers.get('Content-Type')).toBe('text/plain');
    });

    it('should validate request rate limiting', () => {
      const middleware = createSecurityMiddleware();

      // Create mock request
      const mockRequest = new Request('https://example.com/api/test', {
        method: 'GET',
        headers: {
          'x-forwarded-for': '192.168.1.1',
        },
      });

      // First request should be allowed
      const firstCheck = middleware.checkRateLimit(mockRequest);
      expect(typeof firstCheck).toBe('boolean');
    });
  });

  describe('Security Configuration Validation', () => {
    it('should have secure default CSP directives', () => {
      const headers = getSecurityHeaders();
      const csp =
        headers['Content-Security-Policy'] || headers['Content-Security-Policy-Report-Only'];

      if (csp) {
        // Should restrict default sources
        expect(csp).toContain("default-src 'self'");

        // Should prevent object and embed execution
        expect(csp).toContain("object-src 'none'");

        // Should prevent framing
        expect(csp).toContain("frame-ancestors 'none'");

        // Should restrict form actions
        expect(csp).toContain("form-action 'self'");

        // Should restrict base URI
        expect(csp).toContain("base-uri 'self'");
      }
    });

    it('should validate all security headers are strings', () => {
      const headers = getSecurityHeaders();

      Object.entries(headers).forEach(([key, value]) => {
        if (value !== undefined) {
          expect(typeof value).toBe('string');
          expect(value.length).toBeGreaterThan(0);
        }
      });
    });
  });
});
