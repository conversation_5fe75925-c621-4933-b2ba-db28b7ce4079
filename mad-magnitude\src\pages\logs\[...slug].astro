---
export const prerender = true;

import { getCollection } from 'astro:content';
import Layout from '../../layouts/Layout.astro';
import { formatDate } from '../../utils/dateUtils';

export async function getStaticPaths() {
  const logEntries = await getCollection('logs');
  return logEntries.map(entry => ({
    params: { slug: entry.slug },
    props: { entry },
  }));
}

const { entry } = Astro.props;

if (!entry) {
  return Astro.redirect('/404');
}

const { Content } = await entry.render();

// 获取相关日志（同一天或相关研究所）
const allLogs = await getCollection('logs', ({ data }) => !data.draft);
const relatedLogs = allLogs
  .filter(
    log =>
      log.slug !== entry.slug &&
      (log.data.mood === entry.data.mood ||
        (entry.data.relatedInstitute &&
          log.data.relatedInstitute &&
          entry.data.relatedInstitute.some(institute =>
            log.data.relatedInstitute?.includes(institute)
          )))
  )
  .sort((a, b) => new Date(b.data.date).getTime() - new Date(a.data.date).getTime())
  .slice(0, 3);

// 面包屑导航
const breadcrumbs = [
  { label: '首页', href: '/' },
  { label: '研究日志', href: '/logs' },
  { label: entry.data.title, href: `/logs/${entry.slug}` },
];

// 心情配置
const moodConfig = {
  thoughtful: { icon: '🤔', label: '深思', color: 'blue' },
  critical: { icon: '🧐', label: '批判', color: 'red' },
  optimistic: { icon: '😊', label: '乐观', color: 'green' },
  analytical: { icon: '🔍', label: '分析', color: 'purple' },
};

const currentMood = entry.data.mood ? moodConfig[entry.data.mood] : null;
---

<Layout
  title={`${entry.data.title} - 研究日志 - Pennfly Private Academy`}
  description={`${formatDate(entry.data.date)} 的研究日志记录`}
>
  <article class="min-h-screen bg-gray-50">
    <!-- 面包屑导航 -->
    <nav class="border-b border-gray-200 bg-white py-4">
      <div class="container mx-auto px-6">
        <ol class="flex items-center space-x-2 text-sm text-gray-600">
          {
            breadcrumbs.map((crumb, index) => (
              <li class="flex items-center">
                {index > 0 && <span class="mr-2 text-gray-400">/</span>}
                {index === breadcrumbs.length - 1 ? (
                  <span class="font-medium text-gray-800">{crumb.label}</span>
                ) : (
                  <a href={crumb.href} class="transition-colors hover:text-amber-600">
                    {crumb.label}
                  </a>
                )}
              </li>
            ))
          }
        </ol>
      </div>
    </nav>

    <!-- 日志头部 -->
    <header class="bg-white py-12">
      <div class="container mx-auto px-6">
        <div class="mx-auto max-w-4xl">
          <!-- 日期和心情 -->
          <div class="mb-6 flex items-center space-x-4">
            <div
              class="flex items-center space-x-2 rounded-lg bg-amber-100 px-3 py-1 text-sm font-medium text-amber-800"
            >
              <span>📅</span>
              <span>{formatDate(entry.data.date)}</span>
            </div>

            {
              currentMood && (
                <div
                  class={`flex items-center space-x-2 rounded-lg px-3 py-1 text-sm font-medium bg-${currentMood.color}-100 text-${currentMood.color}-800`}
                >
                  <span>{currentMood.icon}</span>
                  <span>{currentMood.label}</span>
                </div>
              )
            }

            {
              entry.data.relatedInstitute && entry.data.relatedInstitute.length > 0 && (
                <div class="flex items-center space-x-2">
                  <span class="text-sm text-gray-600">相关研究所:</span>
                  {entry.data.relatedInstitute.map(institute => (
                    <a
                      href={`/${institute}`}
                      class="inline-flex items-center space-x-1 rounded bg-green-100 px-2 py-1 text-xs text-green-700 transition-colors hover:bg-green-200"
                    >
                      <span>
                        {institute === 'economics'
                          ? '💰'
                          : institute === 'philosophy'
                            ? '🤔'
                            : institute === 'internet'
                              ? '🌐'
                              : institute === 'ai'
                                ? '🤖'
                                : '🔮'}
                      </span>
                      <span>
                        {institute === 'economics'
                          ? '经济研究所'
                          : institute === 'philosophy'
                            ? '哲学研究所'
                            : institute === 'internet'
                              ? '互联网研究所'
                              : institute === 'ai'
                                ? 'AI研究所'
                                : '未来研究所'}
                      </span>
                    </a>
                  ))}
                </div>
              )
            }
          </div>

          <!-- 标题 -->
          <h1 class="mb-6 text-4xl leading-tight font-bold text-gray-800">
            {entry.data.title}
          </h1>

          <!-- 元信息 -->
          <div
            class="flex flex-wrap items-center gap-6 border-t border-gray-200 pt-6 text-sm text-gray-600"
          >
            <div class="flex items-center space-x-2">
              <span>📔</span>
              <span>研究日志</span>
            </div>

            <div class="flex items-center space-x-2">
              <span>📅</span>
              <span>记录时间: {formatDate(entry.data.date)}</span>
            </div>

            {
              entry.data.weather && (
                <div class="flex items-center space-x-2">
                  <span>🌤️</span>
                  <span>天气: {entry.data.weather}</span>
                </div>
              )
            }

            {
              entry.data.location && (
                <div class="flex items-center space-x-2">
                  <span>📍</span>
                  <span>地点: {entry.data.location}</span>
                </div>
              )
            }
          </div>
        </div>
      </div>
    </header>

    <!-- 日志内容 -->
    <main class="py-12">
      <div class="container mx-auto px-6">
        <div class="mx-auto max-w-4xl">
          <div class="prose prose-lg prose-gray max-w-none">
            <Content />
          </div>
        </div>
      </div>
    </main>

    <!-- 标签 -->
    {
      entry.data.tags && entry.data.tags.length > 0 && (
        <section class="bg-white py-8">
          <div class="container mx-auto px-6">
            <div class="mx-auto max-w-4xl">
              <h3 class="mb-4 text-lg font-semibold text-gray-800">相关标签</h3>
              <div class="flex flex-wrap gap-2">
                {entry.data.tags.map(tag => (
                  <span class="cursor-pointer rounded-full bg-gray-100 px-4 py-2 text-sm text-gray-700 transition-colors hover:bg-gray-200">
                    #{tag}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </section>
      )
    }

    <!-- 相关日志 -->
    {
      relatedLogs.length > 0 && (
        <section class="bg-gray-50 py-12">
          <div class="container mx-auto px-6">
            <div class="mx-auto max-w-4xl">
              <h3 class="mb-8 text-2xl font-bold text-gray-800">相关日志</h3>
              <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {relatedLogs.map(log => (
                  <article class="rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition-shadow hover:shadow-md">
                    <div class="mb-3 flex items-center justify-between">
                      <span class="text-xs text-gray-600">{formatDate(log.data.date)}</span>
                      {log.data.mood && (
                        <span
                          class={`inline-block rounded px-2 py-1 text-xs font-medium ${
                            log.data.mood === 'thoughtful'
                              ? 'bg-blue-100 text-blue-800'
                              : log.data.mood === 'critical'
                                ? 'bg-red-100 text-red-800'
                                : log.data.mood === 'optimistic'
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-purple-100 text-purple-800'
                          }`}
                        >
                          {log.data.mood === 'thoughtful'
                            ? '🤔'
                            : log.data.mood === 'critical'
                              ? '🧐'
                              : log.data.mood === 'optimistic'
                                ? '😊'
                                : '🔍'}
                        </span>
                      )}
                    </div>

                    <h4 class="mb-2 line-clamp-2 font-semibold text-gray-800">
                      <a href={`/logs/${log.slug}`} class="transition-colors hover:text-amber-600">
                        {log.data.title}
                      </a>
                    </h4>

                    {log.data.tags && log.data.tags.length > 0 && (
                      <div class="flex flex-wrap gap-1">
                        {log.data.tags.slice(0, 3).map(tag => (
                          <span class="rounded bg-gray-100 px-2 py-0.5 text-xs text-gray-600">
                            #{tag}
                          </span>
                        ))}
                      </div>
                    )}
                  </article>
                ))}
              </div>
            </div>
          </div>
        </section>
      )
    }

    <!-- 导航按钮 -->
    <section class="border-t border-gray-200 bg-white py-8">
      <div class="container mx-auto px-6">
        <div class="mx-auto flex max-w-4xl items-center justify-between">
          <a
            href="/logs"
            class="inline-flex items-center space-x-2 rounded-lg bg-gray-600 px-6 py-3 text-white transition-colors hover:bg-gray-700"
          >
            <span>←</span>
            <span>返回日志列表</span>
          </a>

          <div class="flex items-center space-x-4">
            <button
              onclick="window.scrollTo({top: 0, behavior: 'smooth'})"
              class="rounded-lg bg-amber-600 px-6 py-3 text-white transition-colors hover:bg-amber-700"
            >
              回到顶部 ↑
            </button>
          </div>
        </div>
      </div>
    </section>
  </article>
</Layout>

<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 自定义 prose 样式 */
  .prose {
    color: #374151;
    line-height: 1.75;
  }

  .prose h1,
  .prose h2,
  .prose h3,
  .prose h4,
  .prose h5,
  .prose h6 {
    color: #111827;
    font-weight: 600;
    margin-top: 2rem;
    margin-bottom: 1rem;
  }

  .prose h1 {
    font-size: 2.25rem;
  }
  .prose h2 {
    font-size: 1.875rem;
  }
  .prose h3 {
    font-size: 1.5rem;
  }
  .prose h4 {
    font-size: 1.25rem;
  }

  .prose p {
    margin-bottom: 1.5rem;
  }

  .prose a {
    color: #d97706;
    text-decoration: none;
  }

  .prose a:hover {
    color: #b45309;
    text-decoration: underline;
  }

  .prose blockquote {
    border-left: 4px solid #f3f4f6;
    padding-left: 1rem;
    margin: 1.5rem 0;
    font-style: italic;
    color: #6b7280;
    background-color: #f9fafb;
    padding: 1rem;
    border-radius: 0.5rem;
  }

  .prose ul,
  .prose ol {
    margin: 1.5rem 0;
    padding-left: 1.5rem;
  }

  .prose li {
    margin: 0.5rem 0;
  }

  .prose code {
    background-color: #f3f4f6;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
  }

  .prose pre {
    background-color: #1f2937;
    color: #f9fafb;
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 1.5rem 0;
  }

  .prose pre code {
    background-color: transparent;
    padding: 0;
  }

  /* 日志特有样式 */
  .prose .journal-section {
    background-color: #fffbeb;
    border-left: 4px solid #f59e0b;
    padding: 1rem;
    margin: 1.5rem 0;
    border-radius: 0.5rem;
  }

  .prose .journal-section h3 {
    color: #d97706;
    margin-top: 0;
  }
</style>
