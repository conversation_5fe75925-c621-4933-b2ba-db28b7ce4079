/**
 * Environment variable utilities for secure configuration management
 */

/**
 * Environment variable schema for type safety and validation
 */
export interface EnvConfig {
  // Site configuration
  SITE_URL: string;
  SITE_TITLE: string;
  SITE_DESCRIPTION: string;

  // Environment settings
  NODE_ENV: 'development' | 'production' | 'test';
  DEBUG: boolean;

  // Build configuration
  BUILD_ANALYZE: boolean;
  BUILD_SOURCEMAP: boolean;

  // Development settings
  DEV_PORT: number;
  DEV_HOST: string;
  DEV_OPEN_BROWSER: boolean;

  // Security settings
  CSP_ENABLED: boolean;
  CSP_REPORT_ONLY: boolean;
  CORS_ORIGIN: string;
  CORS_CREDENTIALS: boolean;

  // Feature flags
  FEATURE_COMMENTS: boolean;
  FEATURE_SEARCH: boolean;
  FEATURE_ANALYTICS: boolean;
  FEATURE_NEWSLETTER: boolean;
}

/**
 * Default environment configuration
 */
const defaultConfig: EnvConfig = {
  SITE_URL: 'https://pennfly.com',
  SITE_TITLE: 'Pennfly Private Academy',
  SITE_DESCRIPTION: '个人化私人研究院 · 跨领域学术探索 · 思想与创意的交流平台',
  NODE_ENV: 'development',
  DEBUG: false,
  BUILD_ANALYZE: false,
  BUILD_SOURCEMAP: true,
  DEV_PORT: 4321,
  DEV_HOST: 'localhost',
  DEV_OPEN_BROWSER: true,
  CSP_ENABLED: true,
  CSP_REPORT_ONLY: false,
  CORS_ORIGIN: '*',
  CORS_CREDENTIALS: false,
  FEATURE_COMMENTS: false,
  FEATURE_SEARCH: false,
  FEATURE_ANALYTICS: false,
  FEATURE_NEWSLETTER: false,
};

/**
 * Parse string to boolean with fallback
 */
function parseBoolean(value: string | undefined, fallback: boolean): boolean {
  if (!value) return fallback;
  return value.toLowerCase() === 'true';
}

/**
 * Parse string to number with fallback
 */
function parseNumber(value: string | undefined, fallback: number): number {
  if (!value) return fallback;
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? fallback : parsed;
}

/**
 * Get environment variable with type safety and validation
 */
export function getEnvConfig(): EnvConfig {
  const env = import.meta.env;

  return {
    SITE_URL: env.SITE_URL || defaultConfig.SITE_URL,
    SITE_TITLE: env.SITE_TITLE || defaultConfig.SITE_TITLE,
    SITE_DESCRIPTION: env.SITE_DESCRIPTION || defaultConfig.SITE_DESCRIPTION,
    NODE_ENV: (env.NODE_ENV as EnvConfig['NODE_ENV']) || defaultConfig.NODE_ENV,
    DEBUG: parseBoolean(env.DEBUG, defaultConfig.DEBUG),
    BUILD_ANALYZE: parseBoolean(env.BUILD_ANALYZE, defaultConfig.BUILD_ANALYZE),
    BUILD_SOURCEMAP: parseBoolean(env.BUILD_SOURCEMAP, defaultConfig.BUILD_SOURCEMAP),
    DEV_PORT: parseNumber(env.DEV_PORT, defaultConfig.DEV_PORT),
    DEV_HOST: env.DEV_HOST || defaultConfig.DEV_HOST,
    DEV_OPEN_BROWSER: parseBoolean(env.DEV_OPEN_BROWSER, defaultConfig.DEV_OPEN_BROWSER),
    CSP_ENABLED: parseBoolean(env.CSP_ENABLED, defaultConfig.CSP_ENABLED),
    CSP_REPORT_ONLY: parseBoolean(env.CSP_REPORT_ONLY, defaultConfig.CSP_REPORT_ONLY),
    CORS_ORIGIN: env.CORS_ORIGIN || defaultConfig.CORS_ORIGIN,
    CORS_CREDENTIALS: parseBoolean(env.CORS_CREDENTIALS, defaultConfig.CORS_CREDENTIALS),
    FEATURE_COMMENTS: parseBoolean(env.FEATURE_COMMENTS, defaultConfig.FEATURE_COMMENTS),
    FEATURE_SEARCH: parseBoolean(env.FEATURE_SEARCH, defaultConfig.FEATURE_SEARCH),
    FEATURE_ANALYTICS: parseBoolean(env.FEATURE_ANALYTICS, defaultConfig.FEATURE_ANALYTICS),
    FEATURE_NEWSLETTER: parseBoolean(env.FEATURE_NEWSLETTER, defaultConfig.FEATURE_NEWSLETTER),
  };
}

/**
 * Validate required environment variables
 */
export function validateEnv(): void {
  const config = getEnvConfig();
  const errors: string[] = [];

  // Validate required fields
  if (!config.SITE_URL) {
    errors.push('SITE_URL is required');
  }

  if (!config.SITE_TITLE) {
    errors.push('SITE_TITLE is required');
  }

  // Validate URL format
  try {
    new URL(config.SITE_URL);
  } catch {
    errors.push('SITE_URL must be a valid URL');
  }

  // Validate port range
  if (config.DEV_PORT < 1 || config.DEV_PORT > 65535) {
    errors.push('DEV_PORT must be between 1 and 65535');
  }

  if (errors.length > 0) {
    throw new Error(`Environment validation failed:\n${errors.join('\n')}`);
  }
}

/**
 * Check if running in development mode
 */
export function isDev(): boolean {
  return getEnvConfig().NODE_ENV === 'development';
}

/**
 * Check if running in production mode
 */
export function isProd(): boolean {
  return getEnvConfig().NODE_ENV === 'production';
}

/**
 * Check if debug mode is enabled
 */
export function isDebug(): boolean {
  return getEnvConfig().DEBUG;
}

/**
 * Get sensitive environment variables (for server-side only)
 * These should never be exposed to the client
 */
export function getSensitiveEnv() {
  if (typeof window !== 'undefined') {
    throw new Error('Sensitive environment variables should not be accessed on the client side');
  }

  return {
    // API Keys
    OPENAI_API_KEY: process.env.OPENAI_API_KEY,
    GITHUB_TOKEN: process.env.GITHUB_TOKEN,
    NOTION_TOKEN: process.env.NOTION_TOKEN,

    // Database
    DATABASE_URL: process.env.DATABASE_URL,
    REDIS_URL: process.env.REDIS_URL,

    // Email
    SMTP_HOST: process.env.SMTP_HOST,
    SMTP_PORT: process.env.SMTP_PORT,
    SMTP_USER: process.env.SMTP_USER,
    SMTP_PASS: process.env.SMTP_PASS,
    CONTACT_EMAIL: process.env.CONTACT_EMAIL,

    // Analytics
    GOOGLE_ANALYTICS_ID: process.env.GOOGLE_ANALYTICS_ID,
    PLAUSIBLE_DOMAIN: process.env.PLAUSIBLE_DOMAIN,

    // Comment systems
    DISQUS_SHORTNAME: process.env.DISQUS_SHORTNAME,
    GISCUS_REPO: process.env.GISCUS_REPO,
    GISCUS_REPO_ID: process.env.GISCUS_REPO_ID,
    GISCUS_CATEGORY: process.env.GISCUS_CATEGORY,
    GISCUS_CATEGORY_ID: process.env.GISCUS_CATEGORY_ID,

    // Search
    ALGOLIA_APP_ID: process.env.ALGOLIA_APP_ID,
    ALGOLIA_API_KEY: process.env.ALGOLIA_API_KEY,
    ALGOLIA_INDEX_NAME: process.env.ALGOLIA_INDEX_NAME,
  };
}

/**
 * Sanitize environment variables for client-side exposure
 */
export function getPublicEnv() {
  const config = getEnvConfig();

  return {
    SITE_URL: config.SITE_URL,
    SITE_TITLE: config.SITE_TITLE,
    SITE_DESCRIPTION: config.SITE_DESCRIPTION,
    NODE_ENV: config.NODE_ENV,
    DEBUG: config.DEBUG,
    FEATURE_COMMENTS: config.FEATURE_COMMENTS,
    FEATURE_SEARCH: config.FEATURE_SEARCH,
    FEATURE_ANALYTICS: config.FEATURE_ANALYTICS,
    FEATURE_NEWSLETTER: config.FEATURE_NEWSLETTER,
  };
}
