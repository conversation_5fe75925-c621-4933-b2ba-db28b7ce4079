#!/usr/bin/env node

/**
 * 批量修复常见的 ESLint 问题
 * 使用方法: node .kiro/scripts/fix-common-issues.js
 */

const fs = require("fs");
const path = require("path");
const { execSync } = require("child_process");

console.log("🔧 开始修复常见问题...\n");

// 1. 修复 parseInt 缺少 radix 参数的问题
function fixParseIntRadix() {
  console.log("📝 修复 parseInt radix 参数...");

  try {
    // 使用 sed 或类似工具批量替换
    const command = `find mad-magnitude/src -name "*.ts" -o -name "*.js" -o -name "*.astro" | xargs grep -l "parseInt(" | xargs sed -i.bak "s/parseInt(\\([^,)]*\\))/parseInt(\\1, 10)/g"`;

    // Windows 兼容的方式
    const files = execSync(
      'dir /s /b mad-magnitude\\src\\*.ts mad-magnitude\\src\\*.js mad-magnitude\\src\\*.astro 2>nul || find mad-magnitude/src -name "*.ts" -o -name "*.js" -o -name "*.astro"',
      { encoding: "utf8" }
    )
      .split("\n")
      .filter((file) => file.trim());

    let fixedCount = 0;
    files.forEach((file) => {
      if (!fs.existsSync(file)) return;

      const content = fs.readFileSync(file, "utf8");
      const fixed = content.replace(
        /parseInt\(([^,)]+)\)/g,
        "parseInt($1, 10)"
      );

      if (content !== fixed) {
        fs.writeFileSync(file, fixed);
        fixedCount++;
        console.log(`  ✅ 修复: ${file}`);
      }
    });

    console.log(`  📊 修复了 ${fixedCount} 个文件的 parseInt 问题\n`);
  } catch (error) {
    console.log("  ⚠️ 自动修复失败，请手动修复 parseInt 问题\n");
  }
}

// 2. 移除生产代码中的 console.log
function removeConsoleStatements() {
  console.log("🗑️ 移除 console.log 语句...");

  try {
    const files = execSync(
      'dir /s /b mad-magnitude\\src\\*.ts mad-magnitude\\src\\*.js mad-magnitude\\src\\*.astro 2>nul || find mad-magnitude/src -name "*.ts" -o -name "*.js" -o -name "*.astro"',
      { encoding: "utf8" }
    )
      .split("\n")
      .filter((file) => file.trim());

    let fixedCount = 0;
    files.forEach((file) => {
      if (!fs.existsSync(file)) return;

      const content = fs.readFileSync(file, "utf8");
      // 移除简单的 console.log 语句，保留复杂的调试代码
      const fixed = content.replace(/^\s*console\.log\([^;]*\);\s*$/gm, "");

      if (content !== fixed) {
        fs.writeFileSync(file, fixed);
        fixedCount++;
        console.log(`  ✅ 清理: ${file}`);
      }
    });

    console.log(`  📊 清理了 ${fixedCount} 个文件的 console.log\n`);
  } catch (error) {
    console.log("  ⚠️ 自动清理失败，请手动移除 console.log\n");
  }
}

// 3. 修复未使用变量（添加下划线前缀）
function fixUnusedVariables() {
  console.log("🔧 修复未使用变量...");
  console.log("  ℹ️ 这需要手动处理，建议使用 ESLint 的自动修复功能\n");
}

// 4. 运行 ESLint 自动修复
function runESLintFix() {
  console.log("🔍 运行 ESLint 自动修复...");

  try {
    execSync("cd mad-magnitude && npm run lint", { stdio: "inherit" });
    console.log("  ✅ ESLint 自动修复完成\n");
  } catch (error) {
    console.log("  ⚠️ ESLint 修复过程中发现需要手动处理的问题\n");
  }
}

// 5. 生成问题报告
function generateReport() {
  console.log("📋 生成问题报告...");

  try {
    const result = execSync("cd mad-magnitude && npm run lint:check", {
      encoding: "utf8",
    });
    fs.writeFileSync(".kiro/lint-report.txt", result);
    console.log("  📄 报告已保存到 .kiro/lint-report.txt\n");
  } catch (error) {
    const errorOutput = error.stdout || error.message;
    fs.writeFileSync(".kiro/lint-report.txt", errorOutput);
    console.log("  📄 问题报告已保存到 .kiro/lint-report.txt\n");
  }
}

// 主函数
async function main() {
  console.log("🚀 Pennfly Private Academy - 代码质量修复工具\n");

  // 执行修复步骤
  fixParseIntRadix();
  removeConsoleStatements();
  fixUnusedVariables();
  runESLintFix();
  generateReport();

  console.log("✨ 修复完成！");
  console.log("📝 请查看 .kiro/lint-report.txt 了解剩余问题");
  console.log("💡 建议接下来手动处理类型注解和未使用变量问题");
}

// 运行
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  fixParseIntRadix,
  removeConsoleStatements,
  runESLintFix,
  generateReport,
};
