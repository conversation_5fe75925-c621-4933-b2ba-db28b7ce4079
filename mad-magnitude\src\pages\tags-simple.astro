---
/**
 * 简单标签展示页面
 * 用于测试标签系统的基本功能
 */
import TagCloud from '../components/tags/TagCloud.astro';
import TagList from '../components/tags/TagList.astro';
import Layout from '../layouts/Layout.astro';

// 测试标签数据
const testTags = [
  '人工智能',
  '机器学习',
  '深度学习',
  '自然语言处理',
  '数字经济',
  '区块链',
  '金融科技',
  '市场分析',
];
---

<Layout title="简单标签测试 - Pennfly Private Academy" description="测试标签系统的基本功能">
  <main class="simple-tags-page">
    <div class="container">
      <header class="page-header">
        <h1 class="page-title">简单标签测试</h1>
        <p class="page-description">这个页面用于测试标签系统的基本功能</p>
      </header>

      <!-- 标签云测试 -->
      <section class="test-section">
        <h2 class="section-title">标签云组件</h2>
        <TagCloud maxTags={20} showCount={true} size="medium" />
      </section>

      <!-- 标签列表测试 -->
      <section class="test-section">
        <h2 class="section-title">标签列表组件</h2>
        <div class="tag-list-demo">
          <h3>默认样式</h3>
          <TagList tags={testTags} />

          <h3>紧凑样式</h3>
          <TagList tags={testTags.slice(0, 6)} variant="compact" />

          <h3>徽章样式</h3>
          <TagList tags={testTags.slice(0, 5)} variant="badge" />
        </div>
      </section>
    </div>
  </main>
</Layout>

<style>
  .simple-tags-page {
    min-height: 100vh;
    background: #f8fafc;
    padding: 2rem 0;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  .page-header {
    text-align: center;
    margin-bottom: 3rem;
  }

  .page-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 1rem;
  }

  .page-description {
    font-size: 1.125rem;
    color: #64748b;
    max-width: 600px;
    margin: 0 auto;
  }

  .test-section {
    background: white;
    border-radius: 0.75rem;
    border: 1px solid #e2e8f0;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e2e8f0;
  }

  .tag-list-demo h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #374151;
    margin: 2rem 0 1rem 0;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f1f5f9;
  }

  .tag-list-demo h3:first-child {
    margin-top: 0;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .simple-tags-page {
      padding: 1rem 0;
    }

    .container {
      padding: 0 0.75rem;
    }

    .page-title {
      font-size: 2rem;
    }

    .test-section {
      padding: 1.5rem;
    }
  }
</style>
