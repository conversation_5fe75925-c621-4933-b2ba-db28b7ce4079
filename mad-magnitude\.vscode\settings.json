{"astro.typescript.allowArbitraryAttributes": true, "astro.format.enable": true, "astro.language-server.runtime": "node", "files.associations": {"*.astro": "astro"}, "emmet.includeLanguages": {"astro": "html"}, "tailwindCSS.includeLanguages": {"astro": "html"}, "typescript.preferences.importModuleSpecifier": "relative", "typescript.suggest.autoImports": true, "eslint.validate": ["javascript", "typescript", "astro"], "prettier.documentSelectors": ["**/*.astro"]}