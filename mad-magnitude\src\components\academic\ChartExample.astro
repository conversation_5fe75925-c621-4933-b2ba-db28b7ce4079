---
// 图表示例组件
const flowchartCode = `graph TD
    A[开始] --> B{是否有数据?}
    B -->|是| C[处理数据]
    B -->|否| D[收集数据]
    C --> E[分析结果]
    D --> C
    E --> F[生成报告]
    F --> G[结束]`;

const sequenceCode = `sequenceDiagram
    participant U as 用户
    participant S as 系统
    participant D as 数据库
    
    U->>S: 发送请求
    S->>D: 查询数据
    D-->>S: 返回结果
    S-->>U: 响应数据`;

const ganttCode = `gantt
    title 项目开发时间线
    dateFormat  YYYY-MM-DD
    section 设计阶段
    需求分析    :done, des1, 2024-01-01, 2024-01-05
    系统设计    :done, des2, after des1, 5d
    section 开发阶段
    前端开发    :active, dev1, 2024-01-10, 10d
    后端开发    :dev2, after des2, 10d
    section 测试阶段
    单元测试    :test1, after dev1, 5d
    集成测试    :test2, after dev2, 5d`;
---

<div class="chart-examples bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
  <h3 class="text-lg font-semibold mb-4 text-green-900">图表示例</h3>
  
  <div class="space-y-6">
    <!-- Mermaid 流程图 -->
    <div>
      <h4 class="font-medium mb-2">流程图：</h4>
      <pre><code class="language-mermaid" set:html={flowchartCode}></code></pre>
    </div>
    
    <!-- 序列图 -->
    <div>
      <h4 class="font-medium mb-2">序列图：</h4>
      <pre><code class="language-mermaid" set:html={sequenceCode}></code></pre>
    </div>
    
    <!-- 甘特图 -->
    <div>
      <h4 class="font-medium mb-2">甘特图：</h4>
      <pre><code class="language-mermaid" set:html={ganttCode}></code></pre>
    </div>
  </div>
</div>