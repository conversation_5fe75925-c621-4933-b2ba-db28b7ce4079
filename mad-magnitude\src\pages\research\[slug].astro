---
export const prerender = true;

import Layout from '@/layouts/Layout.astro';
import { calculateReadingTime } from '@/utils/content';
import { getCollection, type CollectionEntry } from 'astro:content';

export async function getStaticPaths() {
  const research = await getCollection('research');

  // 为每篇文章生成静态路径
  return research.map((post: CollectionEntry<'research'>) => {
    // 计算阅读时间
    const readingTime = calculateReadingTime(post.body);

    // 获取相关文章
    const relatedPosts = research
      .filter(p => p.id !== post.id && !p.data.draft)
      .slice(0, 3);

    return {
      params: { slug: post.slug },
      props: { 
        post,
        readingTime,
        relatedPosts: relatedPosts || []
      }
    };
  });
}

interface Props {
  post: CollectionEntry<'research'>;
  readingTime: number;
  relatedPosts: CollectionEntry<'research'>[];
}

const { post, readingTime, relatedPosts } = Astro.props as Props;

if (!post) {
  return Astro.redirect('/404');
}

const { Content } = await post.render();
---

<Layout title={post?.data.title.zh || "研究文章"}>
  <main class="container mx-auto px-4 py-8 max-w-4xl">
    {post ? (
      <>
        <article class="prose prose-lg max-w-none">
          <!-- 文章头部 -->
          <header class="mb-8">
            <h1 class="text-3xl md:text-4xl font-bold mb-4">{post.data.title.zh}</h1>
            {post.data.title.en && (
              <p class="text-gray-600 italic mb-6">{post.data.title.en}</p>
            )}

            <div class="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-6">
              <span class="flex items-center">
                <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                {post.data.publishDate.toLocaleDateString('zh-CN')}
              </span>

              {post.data.updateDate && (
                <span class="flex items-center">
                  <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                  </svg>
                  {post.data.updateDate.toLocaleDateString('zh-CN')}
                </span>
              )}

              <span class="flex items-center">
                <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                {readingTime} 分钟阅读
              </span>

              <span class="flex items-center">
                <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                {post.data.author}
              </span>
            </div>

            <!-- 文章摘要 -->
            <p class="text-gray-700 mb-6">{post.data.description.zh}</p>

            <!-- 标签 -->
            <div class="flex flex-wrap gap-2 mb-8">
              {post.data.tags.map(tag => (
                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                  {tag}
                </span>
              ))}
            </div>
          </header>

          <!-- 文章内容 -->
          <div class="prose prose-lg max-w-none">
            <Content />
          </div>

          <!-- 文章底部 -->
          <footer class="mt-12 pt-6 border-t border-gray-200">
            <p class="text-gray-600 text-sm">
              版权所有 © {post.data.author}。保留所有权利。
            </p>
          </footer>
        </article>

        <!-- 相关文章 -->
        {relatedPosts && relatedPosts.length > 0 && (
          <section class="mt-12">
            <h2 class="text-2xl font-bold mb-6">相关文章</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              {relatedPosts.map(related => (
                <a
                  href={`/research/${related.slug}`}
                  class="block p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300"
                >
                  <h3 class="text-xl font-semibold mb-2">{related.data.title.zh}</h3>
                  <p class="text-gray-600 mb-4">{related.data.description.zh}</p>
                  <div class="flex items-center text-sm text-gray-500">
                    <span>{related.data.publishDate.toLocaleDateString('zh-CN')}</span>
                    <span class="mx-2">•</span>
                    <span>{related.data.author}</span>
                  </div>
                </a>
              ))}
            </div>
          </section>
        )}
      </>
    ) : (
      <div class="text-center py-12">
        <h1 class="text-2xl font-bold mb-4">文章未找到</h1>
        <p class="text-gray-600">抱歉，您要查找的文章不存在或已被删除。</p>
      </div>
    )}
  </main>
</Layout>
