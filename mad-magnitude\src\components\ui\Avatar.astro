---
/**
 * Enhanced Avatar component for Pennfly Private Academy
 * Displays user avatars with fallbacks and accessibility features
 */

export interface Props {
  src?: string;
  alt?: string;
  name?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  shape?: 'circle' | 'square' | 'rounded';
  status?: 'online' | 'offline' | 'away' | 'busy';
  interactive?: boolean;
  href?: string;
  target?: '_blank' | '_self' | '_parent' | '_top';
  class?: string;
  'aria-label'?: string;
}

const {
  src,
  alt,
  name = '',
  size = 'md',
  shape = 'circle',
  status,
  interactive = false,
  href,
  target,
  class: className = '',
  'aria-label': ariaLabel,
  ...rest
} = Astro.props;

// Size configurations
const sizeClasses = {
  xs: 'w-6 h-6 text-xs',
  sm: 'w-8 h-8 text-sm',
  md: 'w-10 h-10 text-base',
  lg: 'w-12 h-12 text-lg',
  xl: 'w-16 h-16 text-xl',
  '2xl': 'w-20 h-20 text-2xl',
};

// Shape configurations
const shapeClasses = {
  circle: 'rounded-full',
  square: 'rounded-none',
  rounded: 'rounded-lg',
};

// Status indicator configurations
const statusClasses = {
  online: 'bg-green-500',
  offline: 'bg-gray-400',
  away: 'bg-yellow-500',
  busy: 'bg-red-500',
};

// Status indicator sizes
const statusSizes = {
  xs: 'w-1.5 h-1.5',
  sm: 'w-2 h-2',
  md: 'w-2.5 h-2.5',
  lg: 'w-3 h-3',
  xl: 'w-4 h-4',
  '2xl': 'w-5 h-5',
};

// Interactive states
const interactiveClasses =
  interactive || href
    ? `
  transition-all duration-200 ease-in-out
  hover:opacity-80 hover:scale-105
  active:scale-95
  focus:outline-none focus:ring-2 focus:ring-theme-interactive-focus focus:ring-offset-2
  cursor-pointer
`
    : '';

// Base classes
const baseClasses = `
  inline-flex items-center justify-center
  bg-theme-bg-tertiary text-theme-fg-primary
  border-2 border-theme-border-default
  font-medium
  overflow-hidden
  ${sizeClasses[size]}
  ${shapeClasses[shape]}
  ${interactiveClasses}
`;

const combinedClasses = `
  ${baseClasses}
  ${className}
`
  .trim()
  .replace(/\s+/g, ' ');

// Generate initials from name
const getInitials = (name: string): string => {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
};

const initials = getInitials(name);

// Determine if this should render as a link or div
const isLink = href && !rest.disabled;
const Component = isLink ? 'a' : 'div';

// Prepare props for the component
const componentProps = isLink ? { href, target } : {};

// Add role for interactive non-link avatars
const accessibilityProps = interactive && !isLink ? { role: 'button', tabindex: '0' } : {};

// Generate a consistent background color based on name
const getBackgroundColor = (name: string): string => {
  const colors = [
    'bg-red-500',
    'bg-orange-500',
    'bg-amber-500',
    'bg-yellow-500',
    'bg-lime-500',
    'bg-green-500',
    'bg-emerald-500',
    'bg-teal-500',
    'bg-cyan-500',
    'bg-sky-500',
    'bg-blue-500',
    'bg-indigo-500',
    'bg-violet-500',
    'bg-purple-500',
    'bg-fuchsia-500',
    'bg-pink-500',
    'bg-rose-500',
  ];

  let hash = 0;
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash);
  }

  return colors[Math.abs(hash) % colors.length];
};

const backgroundColorClass = name ? getBackgroundColor(name) : '';
---

<div class="relative inline-block">
  <Component
    class={`${combinedClasses} ${!src && backgroundColorClass}`}
    aria-label={ariaLabel || (name ? `${name}'s avatar` : 'User avatar')}
    {...componentProps}
    {...accessibilityProps}
    {...rest}
  >
    {
      src ? (
        <img
          src={src}
          alt={alt || name || 'Avatar'}
          class="h-full w-full object-cover"
          loading="lazy"
          onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
        />
      ) : null
    }

    {/* Fallback initials */}
    <div
      class={`w-full h-full flex items-center justify-center text-white font-semibold ${src ? 'hidden' : ''}`}
      aria-hidden="true"
    >
      {initials || '?'}
    </div>
  </Component>

  {/* Status indicator */}
  {
    status && (
      <div
        class={`absolute right-0 bottom-0 ${statusSizes[size]} ${statusClasses[status]} border-theme-bg-primary rounded-full border-2`}
        aria-label={`Status: ${status}`}
        role="img"
      />
    )
  }
</div>

<style>
  /* Ensure smooth transitions respect user preferences */
  @media (prefers-reduced-motion: reduce) {
    div,
    a {
      transition: none !important;
      transform: none !important;
    }
  }

  /* Enhanced focus styles for better accessibility */
  div[role='button']:focus-visible,
  a:focus-visible {
    outline: 2px solid var(--color-interactive-focus);
    outline-offset: 2px;
  }

  /* Keyboard interaction for interactive avatars */
  div[role='button']:focus,
  div[role='button']:hover {
    opacity: 0.8;
    transform: scale(1.05);
  }

  div[role='button']:active {
    transform: scale(0.95);
  }

  /* Image loading states */
  img {
    transition: opacity 0.2s ease-in-out;
  }

  img[src=''],
  img:not([src]) {
    display: none;
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    div,
    a {
      border-width: 3px;
      font-weight: 700;
    }
  }

  /* Print styles */
  @media print {
    div,
    a {
      border: 2px solid black !important;
      transform: none !important;
    }

    /* Hide status indicators in print */
    .absolute {
      display: none !important;
    }
  }

  /* Improved image rendering */
  img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }

  /* Status indicator animations */
  .absolute {
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.8;
    }
  }

  /* Disable animations for reduced motion */
  @media (prefers-reduced-motion: reduce) {
    .absolute {
      animation: none !important;
    }
  }
</style>
