// Astro 组件通用类型定义

export interface BaseArticle {
  slug: string;
  data: {
    title: { zh: string; en?: string };
    description: { zh: string; en?: string };
    publishDate: Date;
    updateDate?: Date;
    tags: string[];
    featured: boolean;
    draft: boolean;
    author: string;
    readingTime?: number;
    summary?: string;
    relatedContent?: string[];
    [key: string]: any;
  };
}

export interface NewsArticle extends BaseArticle {
  data: BaseArticle['data'] & {
    type: 'research' | 'announcement' | 'reflection' | 'milestone';
    relatedInstitute?: string[];
    category?: 'ai' | 'education' | 'philosophy' | 'technology';
    mood?: 'thoughtful' | 'critical' | 'optimistic' | 'analytical';
  };
}

export interface LogEntry {
  slug: string;
  data: {
    date: Date;
    title: string;
    tags: string[];
    mood?: 'thoughtful' | 'critical' | 'optimistic' | 'analytical';
    relatedInstitute?: string[];
    draft: boolean;
  };
}

export interface InstituteArticle extends BaseArticle {
  data: BaseArticle['data'] & {
    // 经济研究所特有字段
    analysisType?: 'market' | 'policy' | 'theory' | 'data';
    dataSource?: string;

    // 哲学研究所特有字段
    philosophyBranch?: 'ethics' | 'metaphysics' | 'epistemology' | 'logic' | 'aesthetics';
    thinkers?: string[];

    // 互联网研究所特有字段
    industry?: 'social' | 'ecommerce' | 'fintech' | 'education' | 'entertainment';
    companies?: string[];

    // AI研究所特有字段
    aiField?: 'ml' | 'nlp' | 'cv' | 'robotics' | 'ethics' | 'agi';
    techStack?: string[];
    models?: string[];

    // 未来研究所特有字段
    timeHorizon?: 'short' | 'medium' | 'long';
    domains?: string[];
    confidence?: 'low' | 'medium' | 'high';
  };
}

// DOM 事件类型辅助
export type HTMLElementEvent<T extends HTMLElement> = Event & {
  target: T;
  currentTarget: T;
};

// 常用的 DOM 元素类型
export type ButtonClickEvent = HTMLElementEvent<HTMLButtonElement>;
export type InputChangeEvent = HTMLElementEvent<HTMLInputElement>;
export type SelectChangeEvent = HTMLElementEvent<HTMLSelectElement>;
export type AnchorClickEvent = HTMLElementEvent<HTMLAnchorElement>;

// 筛选和排序选项
export interface SortOptions {
  field: 'date' | 'title' | 'readingTime' | 'featured';
  order: 'asc' | 'desc';
}

export interface FilterOptions {
  type?: string;
  featured?: boolean;
  tags?: string[];
  dateRange?: {
    start?: string;
    end?: string;
  };
}

// 统计信息类型
export interface ContentStats {
  total: number;
  featured: number;
  totalTags: number;
  averageReadingTime: number;
  latestDate: Date | null;
  oldestDate: Date | null;
}

export interface PopularTag {
  tag: string;
  count: number;
}
