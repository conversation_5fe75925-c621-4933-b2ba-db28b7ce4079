/**
 * 标签统计 API
 * 提供标签统计信息
 */
import type { APIRoute } from 'astro';
import { globalTagManager } from '../../../utils/tagManager';

export const GET: APIRoute = async ({ url }) => {
  try {
    const searchParams = new URL(url).searchParams;
    const category = searchParams.get('category');
    const includeRelated = searchParams.get('includeRelated') === 'true';
    const limit = parseInt(searchParams.get('limit') || '20');

    // 获取标签统计
    const tagStats = await globalTagManager.getTagStats();

    // 构建响应数据
    const response: any = {
      totalTags: tagStats.totalTags,
      totalUniqueContent: tagStats.totalUniqueContent,
      categories: Object.keys(tagStats.tagsByCategory).map(cat => ({
        name: cat,
        displayName: getCategoryDisplayName(cat),
        count: tagStats.tagsByCategory[cat].length,
        totalUsage: tagStats.tagsByCategory[cat].reduce((sum, tag) => sum + tag.count, 0),
        topTag: tagStats.tagsByCategory[cat][0]?.name || null,
      })),
      recentTags: tagStats.recentTags.slice(0, 10),
    };

    // 如果指定了分类，返回该分类的详细信息
    if (category && category !== 'all' && tagStats.tagsByCategory[category]) {
      const categoryTags = tagStats.tagsByCategory[category].slice(0, limit);
      response.categoryDetails = {
        name: category,
        displayName: getCategoryDisplayName(category),
        tags: categoryTags.map(tag => ({
          name: tag.name,
          count: tag.count,
          color: tag.color,
          relatedTags: includeRelated ? tag.relatedTags.slice(0, 3) : [],
          url: `/tags/${encodeURIComponent(tag.name)}`,
        })),
      };
    } else {
      // 返回最受欢迎的标签
      response.popularTags = tagStats.mostPopularTags.slice(0, limit).map(tag => ({
        name: tag.name,
        count: tag.count,
        category: tag.category,
        color: tag.color,
        relatedTags: includeRelated ? tag.relatedTags.slice(0, 3) : [],
        url: `/tags/${encodeURIComponent(tag.name)}`,
      }));
    }

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=600', // 缓存10分钟
      },
    });
  } catch (error) {
    console.error('标签统计 API 错误:', error);

    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        code: 'INTERNAL_ERROR',
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};

/**
 * 获取分类显示名称
 */
function getCategoryDisplayName(category: string): string {
  const names: Record<string, string> = {
    technology: '技术',
    economics: '经济',
    philosophy: '哲学',
    society: '社会',
    research: '研究',
    tools: '工具',
    general: '通用',
  };
  return names[category] || category;
}
