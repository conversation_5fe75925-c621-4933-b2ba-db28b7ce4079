/**
 * 内容验证和处理工具
 */

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface ContentError extends Error {
  code: string;
  details?: any;
}

/**
 * 验证内容的基本格式和必填字段
 */
export function validateContent(content: any): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 验证必填字段
  if (!content.title?.zh) {
    errors.push('缺少中文标题');
  }

  if (!content.description?.zh) {
    errors.push('缺少中文描述');
  }

  if (!content.publishDate) {
    errors.push('缺少发布日期');
  }

  // 验证日期格式
  if (content.publishDate && !(content.publishDate instanceof Date)) {
    try {
      new Date(content.publishDate);
    } catch {
      errors.push('发布日期格式无效');
    }
  }

  // 验证标签格式
  if (content.tags && !Array.isArray(content.tags)) {
    errors.push('标签必须是数组格式');
  }

  // 验证关联内容存在性（这里只做格式检查，实际存在性需要在构建时检查）
  if (content.relatedContent && !Array.isArray(content.relatedContent)) {
    errors.push('关联内容必须是数组格式');
  }

  // 警告检查
  if (!content.tags || content.tags.length === 0) {
    warnings.push('建议添加标签以便分类');
  }

  if (!content.summary && content.type !== 'logs') {
    warnings.push('建议添加内容摘要');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * 处理内容验证错误
 */
export function handleContentError(error: ContentError): void {
  console.error(`Content validation failed [${error.code}]:`, error.message);

  if (error.details) {
    console.error('Error details:', error.details);
  }

  // 在开发环境中提供更详细的错误信息
  if (import.meta.env.DEV) {
    console.error('Stack trace:', error.stack);
  }
}

/**
 * 生成内容ID（基于文件路径）
 */
export function generateContentId(filePath: string): string {
  return filePath
    .replace(/^.*\/content\//, '')
    .replace(/\.md$/, '')
    .replace(/\//g, '-');
}

/**
 * 验证关联内容的存在性
 */
export function validateRelatedContent(
  contentId: string,
  relatedIds: string[],
  allContentIds: string[]
): string[] {
  const missingIds: string[] = [];

  for (const relatedId of relatedIds) {
    if (!allContentIds.includes(relatedId)) {
      missingIds.push(relatedId);
    }
  }

  if (missingIds.length > 0) {
    console.warn(`Content ${contentId} references missing content:`, missingIds);
  }

  return missingIds;
}

/**
 * 计算阅读时间（基于字数估算）
 */
export function calculateReadingTime(content: string): number {
  // 中文字符数 + 英文单词数
  const chineseChars = (content.match(/[\u4e00-\u9fa5]/g) || []).length;
  const englishWords = content
    .replace(/[\u4e00-\u9fa5]/g, '')
    .split(/\s+/)
    .filter(word => word.length > 0).length;

  // 假设中文阅读速度 300字/分钟，英文阅读速度 200词/分钟
  const readingTimeMinutes = Math.ceil(chineseChars / 300 + englishWords / 200);

  return Math.max(1, readingTimeMinutes); // 至少1分钟
}

/**
 * 提取内容摘要（如果没有手动设置）
 */
export function extractSummary(content: string, maxLength: number = 150): string {
  // 移除 Markdown 标记
  const plainText = content
    .replace(/#{1,6}\s+/g, '') // 移除标题标记
    .replace(/\*\*(.*?)\*\*/g, '$1') // 移除粗体标记
    .replace(/\*(.*?)\*/g, '$1') // 移除斜体标记
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // 移除链接，保留文本
    .replace(/```[\s\S]*?```/g, '') // 移除代码块
    .replace(/`([^`]+)`/g, '$1') // 移除行内代码标记
    .replace(/\n+/g, ' ') // 将换行替换为空格
    .trim();

  // 截取指定长度
  if (plainText.length <= maxLength) {
    return plainText;
  }

  // 在单词边界截取，避免截断中文字符
  let summary = plainText.substring(0, maxLength);
  const lastSpace = summary.lastIndexOf(' ');
  const lastChinese = summary.search(/[\u4e00-\u9fa5][^\u4e00-\u9fa5]*$/);

  if (lastChinese > lastSpace && lastChinese > maxLength * 0.8) {
    summary = summary.substring(0, lastChinese + 1);
  } else if (lastSpace > maxLength * 0.8) {
    summary = summary.substring(0, lastSpace);
  }

  return summary + '...';
}
