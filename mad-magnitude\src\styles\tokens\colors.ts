/**
 * 设计令牌 - 颜色系统
 * 基于 Pennfly Private Academy 的学术品牌定位
 */

export const colors = {
  // 主色调 - 学术蓝 (基于现有项目配色)
  primary: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6',
    600: '#2563eb', // 主要使用色
    700: '#1d4ed8', // 悬停状态
    800: '#1e40af', // 深色主题
    900: '#1e3a8a',
    950: '#172554',
  },

  // 辅助色 - 学术金 (作为强调色使用)
  secondary: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b', // 主要辅助色
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
    950: '#451a03',
  },

  // 语义色系统
  semantic: {
    success: {
      light: '#10b981',
      DEFAULT: '#059669',
      dark: '#047857',
    },
    warning: {
      light: '#f59e0b',
      DEFAULT: '#d97706',
      dark: '#b45309',
    },
    error: {
      light: '#ef4444',
      DEFAULT: '#dc2626',
      dark: '#b91c1c',
    },
    info: {
      light: '#3b82f6',
      DEFAULT: '#2563eb',
      dark: '#1d4ed8',
    },
  },

  // 中性色系统 (支持亮色和暗色主题)
  neutral: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
    950: '#030712',
  },

  // 背景色系统
  background: {
    light: {
      primary: '#ffffff',
      secondary: '#f9fafb',
      tertiary: '#f3f4f6',
    },
    dark: {
      primary: '#111827',
      secondary: '#1f2937',
      tertiary: '#374151',
    },
  },

  // 文本色系统
  text: {
    light: {
      primary: '#111827',
      secondary: '#374151',
      tertiary: '#6b7280',
      inverse: '#ffffff',
    },
    dark: {
      primary: '#f9fafb',
      secondary: '#e5e7eb',
      tertiary: '#9ca3af',
      inverse: '#111827',
    },
  },

  // 边框色系统
  border: {
    light: {
      primary: '#e5e7eb',
      secondary: '#d1d5db',
      focus: '#3b82f6',
    },
    dark: {
      primary: '#374151',
      secondary: '#4b5563',
      focus: '#60a5fa',
    },
  },
} as const;

// 导出类型定义
export type ColorToken = typeof colors;
export type ColorScale = keyof typeof colors.primary;
export type SemanticColor = keyof typeof colors.semantic;
