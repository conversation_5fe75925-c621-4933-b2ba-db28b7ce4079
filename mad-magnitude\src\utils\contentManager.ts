/**
 * 内容管理核心工具
 * 提供内容的 CRUD 操作和管理功能
 */
import { getCollection } from 'astro:content';

export interface ContentItem {
  id: string;
  collection: string;
  slug: string;
  title: string;
  description?: string;
  publishDate: Date;
  updateDate?: Date;
  draft: boolean;
  featured: boolean;
  tags: string[];
  author: string;
  content: string;
  filePath: string;
}

export interface ContentFilter {
  collection?: string;
  draft?: boolean;
  featured?: boolean;
  tags?: string[];
  author?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  search?: string;
}

export interface ContentStats {
  total: number;
  published: number;
  drafts: number;
  featured: number;
  byCollection: Record<string, number>;
  byAuthor: Record<string, number>;
  recentActivity: Array<{
    action: string;
    content: string;
    date: Date;
  }>;
}

export class ContentManager {
  private static instance: ContentManager;
  private contentCache: Map<string, ContentItem> = new Map();
  private lastCacheUpdate: number = 0;
  private cacheTimeout = 5 * 60 * 1000; // 5分钟缓存

  static getInstance(): ContentManager {
    if (!ContentManager.instance) {
      ContentManager.instance = new ContentManager();
    }
    return ContentManager.instance;
  }

  /**
   * 获取所有内容列表
   */
  async getAllContent(filter?: ContentFilter): Promise<ContentItem[]> {
    try {
      await this.refreshCacheIfNeeded();

      let items = Array.from(this.contentCache.values());

      // 应用筛选器
      if (filter) {
        items = this.applyFilter(items, filter);
      }

      // 按更新时间排序
      return items.sort((a, b) => {
        const dateA = a.updateDate || a.publishDate;
        const dateB = b.updateDate || b.publishDate;
        return dateB.getTime() - dateA.getTime();
      });
    } catch (error) {
      console.error('Error getting all content:', error);
      return [];
    }
  }

  /**
   * 根据ID获取单个内容
   */
  async getContentById(id: string): Promise<ContentItem | null> {
    await this.refreshCacheIfNeeded();
    return this.contentCache.get(id) || null;
  }

  /**
   * 创建新内容（简化版本，实际文件操作需要通过API）
   */
  async createContent(
    collection: string,
    slug: string,
    frontmatter: Record<string, any>,
    content: string
  ): Promise<ContentItem> {
    // 生成前置元数据
    const now = new Date();
    const fullFrontmatter = {
      title: frontmatter.title || 'Untitled',
      description: frontmatter.description || '',
      publishDate: frontmatter.publishDate || now,
      updateDate: now,
      draft: frontmatter.draft !== undefined ? frontmatter.draft : true,
      featured: frontmatter.featured || false,
      tags: frontmatter.tags || [],
      author: frontmatter.author || 'Pennfly',
      ...frontmatter,
    };

    // 创建内容项
    const contentItem: ContentItem = {
      id: `${collection}/${slug}`,
      collection,
      slug,
      title: fullFrontmatter.title,
      description: fullFrontmatter.description,
      publishDate: new Date(fullFrontmatter.publishDate),
      updateDate: new Date(fullFrontmatter.updateDate),
      draft: fullFrontmatter.draft,
      featured: fullFrontmatter.featured,
      tags: fullFrontmatter.tags,
      author: fullFrontmatter.author,
      content,
      filePath: `src/content/${collection}/${slug}.md`,
    };

    // 更新缓存
    this.contentCache.set(contentItem.id, contentItem);

    return contentItem;
  }

  /**
   * 更新内容（简化版本，实际文件操作需要通过API）
   */
  async updateContent(
    id: string,
    frontmatter: Record<string, any>,
    content: string
  ): Promise<ContentItem | null> {
    const existingItem = await this.getContentById(id);
    if (!existingItem) {
      return null;
    }

    // 更新前置元数据
    const updatedFrontmatter: Record<string, any> = {
      ...frontmatter,
      updateDate: new Date(),
    };

    // 更新内容项
    const updatedItem: ContentItem = {
      ...existingItem,
      title: updatedFrontmatter.title || existingItem.title,
      description: updatedFrontmatter.description || existingItem.description,
      updateDate: updatedFrontmatter.updateDate,
      draft: updatedFrontmatter.draft !== undefined ? updatedFrontmatter.draft : existingItem.draft,
      featured:
        updatedFrontmatter.featured !== undefined
          ? updatedFrontmatter.featured
          : existingItem.featured,
      tags: updatedFrontmatter.tags || existingItem.tags,
      author: updatedFrontmatter.author || existingItem.author,
      content,
    };

    // 更新缓存
    this.contentCache.set(id, updatedItem);

    return updatedItem;
  }

  /**
   * 删除内容（简化版本，实际文件操作需要通过API）
   */
  async deleteContent(id: string): Promise<boolean> {
    const item = await this.getContentById(id);
    if (!item) {
      return false;
    }

    try {
      // 从缓存中移除
      this.contentCache.delete(id);
      return true;
    } catch (error) {
      console.error('删除内容失败:', error);
      return false;
    }
  }

  /**
   * 获取内容统计信息
   */
  async getContentStats(): Promise<ContentStats> {
    await this.refreshCacheIfNeeded();

    const items = Array.from(this.contentCache.values());
    const published = items.filter(item => !item.draft);
    const drafts = items.filter(item => item.draft);
    const featured = items.filter(item => item.featured);

    // 按集合统计
    const byCollection: Record<string, number> = {};
    items.forEach(item => {
      byCollection[item.collection] = (byCollection[item.collection] || 0) + 1;
    });

    // 按作者统计
    const byAuthor: Record<string, number> = {};
    items.forEach(item => {
      byAuthor[item.author] = (byAuthor[item.author] || 0) + 1;
    });

    // 最近活动（简化版本）
    const recentActivity = items
      .sort((a, b) => {
        const dateA = a.updateDate || a.publishDate;
        const dateB = b.updateDate || b.publishDate;
        return dateB.getTime() - dateA.getTime();
      })
      .slice(0, 10)
      .map(item => ({
        action: item.updateDate ? '更新' : '创建',
        content: item.title,
        date: item.updateDate || item.publishDate,
      }));

    return {
      total: items.length,
      published: published.length,
      drafts: drafts.length,
      featured: featured.length,
      byCollection,
      byAuthor,
      recentActivity,
    };
  }

  /**
   * 搜索内容
   */
  async searchContent(query: string): Promise<ContentItem[]> {
    await this.refreshCacheIfNeeded();

    const searchTerm = query.toLowerCase();
    const items = Array.from(this.contentCache.values());

    return items.filter(item => {
      return (
        item.title.toLowerCase().includes(searchTerm) ||
        item.description?.toLowerCase().includes(searchTerm) ||
        item.content.toLowerCase().includes(searchTerm) ||
        item.tags.some(tag => tag.toLowerCase().includes(searchTerm)) ||
        item.author.toLowerCase().includes(searchTerm)
      );
    });
  }

  /**
   * 刷新缓存（如果需要）
   */
  private async refreshCacheIfNeeded(): Promise<void> {
    const now = Date.now();
    if (now - this.lastCacheUpdate < this.cacheTimeout && this.contentCache.size > 0) {
      return;
    }

    await this.refreshCache();
  }

  /**
   * 刷新缓存
   */
  private async refreshCache(): Promise<void> {
    this.contentCache.clear();

    const collections = [
      'news',
      'logs',
      'research',
      'reflections',
      'economics',
      'philosophy',
      'internet',
      'ai',
      'future',
      'products',
    ];

    for (const collectionName of collections) {
      try {
        const collection = await getCollection(collectionName as any);

        for (const entry of collection) {
          const contentItem: ContentItem = {
            id: `${collectionName}/${entry.slug}`,
            collection: collectionName,
            slug: entry.slug,
            title: this.extractTitle(entry.data),
            description: this.extractDescription(entry.data),
            publishDate: new Date(entry.data.publishDate || entry.data.date || Date.now()),
            updateDate: entry.data.updateDate ? new Date(entry.data.updateDate) : undefined,
            draft: entry.data.draft || false,
            featured: entry.data.featured || false,
            tags: entry.data.tags || [],
            author: entry.data.author || 'Pennfly',
            content: '', // Content body would need to be rendered separately
            filePath: `src/content/${collectionName}/${entry.slug}.md`,
          };

          this.contentCache.set(contentItem.id, contentItem);
        }
      } catch (error) {
        console.warn(`Failed to load collection ${collectionName}:`, error);
      }
    }

    this.lastCacheUpdate = Date.now();
  }

  /**
   * 提取标题（处理多语言格式）
   */
  private extractTitle(data: any): string {
    if (typeof data.title === 'string') {
      return data.title;
    }
    if (typeof data.title === 'object' && data.title.zh) {
      return data.title.zh;
    }
    return 'Untitled';
  }

  /**
   * 提取描述（处理多语言格式）
   */
  private extractDescription(data: any): string | undefined {
    if (typeof data.description === 'string') {
      return data.description;
    }
    if (typeof data.description === 'object' && data.description.zh) {
      return data.description.zh;
    }
    return undefined;
  }

  /**
   * 应用筛选器
   */
  private applyFilter(items: ContentItem[], filter: ContentFilter): ContentItem[] {
    return items.filter(item => {
      // 集合筛选
      if (filter.collection && item.collection !== filter.collection) {
        return false;
      }

      // 草稿状态筛选
      if (filter.draft !== undefined && item.draft !== filter.draft) {
        return false;
      }

      // 特色内容筛选
      if (filter.featured !== undefined && item.featured !== filter.featured) {
        return false;
      }

      // 标签筛选
      if (filter.tags && filter.tags.length > 0) {
        const hasMatchingTag = filter.tags.some(tag => item.tags.includes(tag));
        if (!hasMatchingTag) {
          return false;
        }
      }

      // 作者筛选
      if (filter.author && item.author !== filter.author) {
        return false;
      }

      // 日期范围筛选
      if (filter.dateRange) {
        const itemDate = item.updateDate || item.publishDate;
        if (itemDate < filter.dateRange.start || itemDate > filter.dateRange.end) {
          return false;
        }
      }

      // 搜索筛选
      if (filter.search) {
        const searchTerm = filter.search.toLowerCase();
        const matchesSearch =
          item.title.toLowerCase().includes(searchTerm) ||
          item.description?.toLowerCase().includes(searchTerm) ||
          item.content.toLowerCase().includes(searchTerm) ||
          item.tags.some(tag => tag.toLowerCase().includes(searchTerm));
        if (!matchesSearch) {
          return false;
        }
      }

      return true;
    });
  }

  /**
   * 生成 Markdown 文件内容
   */
  private generateMarkdownFile(frontmatter: Record<string, any>, content: string): string {
    const yamlFrontmatter = Object.entries(frontmatter)
      .map(([key, value]) => {
        if (value instanceof Date) {
          return `${key}: ${value.toISOString()}`;
        } else if (Array.isArray(value)) {
          return `${key}: [${value.map(v => `'${v}'`).join(', ')}]`;
        } else if (typeof value === 'string') {
          return `${key}: '${value.replace(/'/g, "''")}'`;
        } else {
          return `${key}: ${value}`;
        }
      })
      .join('\n');

    return `---\n${yamlFrontmatter}\n---\n\n${content}`;
  }

  /**
   * 解析 Markdown 文件
   */
  private parseMarkdownFile(fileContent: string): {
    frontmatter: Record<string, any>;
    content: string;
  } {
    const frontmatterRegex = /^---\n([\s\S]*?)\n---\n([\s\S]*)$/;
    const match = fileContent.match(frontmatterRegex);

    if (!match) {
      return { frontmatter: {}, content: fileContent };
    }

    const [, frontmatterStr, content] = match;

    // 简单的 YAML 解析（生产环境建议使用专业的 YAML 解析器）
    const frontmatter: Record<string, any> = {};
    const lines = frontmatterStr.split('\n');

    for (const line of lines) {
      const colonIndex = line.indexOf(':');
      if (colonIndex === -1) continue;

      const key = line.substring(0, colonIndex).trim();
      const value = line.substring(colonIndex + 1).trim();

      // 简单的类型推断
      if (value.startsWith('[') && value.endsWith(']')) {
        // 数组
        const arrayContent = value.slice(1, -1);
        frontmatter[key] = arrayContent.split(',').map(item => item.trim().replace(/^'|'$/g, ''));
      } else if (value === 'true' || value === 'false') {
        // 布尔值
        frontmatter[key] = value === 'true';
      } else if (!isNaN(Number(value))) {
        // 数字
        frontmatter[key] = Number(value);
      } else if (value.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)) {
        // 日期
        frontmatter[key] = new Date(value);
      } else {
        // 字符串
        frontmatter[key] = value.replace(/^'|'$/g, '');
      }
    }

    return { frontmatter, content: content.trim() };
  }
}

// 导出单例实例
export const contentManager = ContentManager.getInstance();
