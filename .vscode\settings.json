{"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "editor.tabSize": 2, "editor.insertSpaces": true, "editor.detectIndentation": false, "typescript.preferences.importModuleSpecifier": "relative", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "astro.typescript.allowArbitraryAttributes": true, "astro.format.enable": true, "files.associations": {"*.astro": "astro"}, "emmet.includeLanguages": {"astro": "html"}, "tailwindCSS.includeLanguages": {"astro": "html"}, "tailwindCSS.experimental.classRegex": [["class:list=\\{([^}]*)\\}", "'([^']*)'"], ["class:list=\\{([^}]*)\\}", "\"([^\"]*)\""]], "search.exclude": {"**/node_modules": true, "**/dist": true, "**/.astro": true, "**/coverage": true}, "files.exclude": {"**/.astro": true}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "astro"], "prettier.documentSelectors": ["**/*.astro"], "json.schemas": [{"fileMatch": ["package.json"], "url": "https://json.schemastore.org/package.json"}, {"fileMatch": ["tsconfig.json", "tsconfig.*.json"], "url": "https://json.schemastore.org/tsconfig.json"}]}