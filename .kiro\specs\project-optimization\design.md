# 设计文档 - 项目配置优化

## 概述

本文档描述了 Pennfly Private Academy 项目配置优化的技术设计。通过完善开发工具链、构建配置、代码质量保证和开发体验，为项目建立现代化的开发基础设施。

## 架构

### 开发工具链架构

```
┌─────────────────────────────────────────────────────────────┐
│                    开发体验层                                │
├─────────────────────────────────────────────────────────────┤
│  VS Code    │  热重载     │  类型检查   │  调试工具   │  扩展   │
│  配置       │  开发服务器  │  TypeScript │  Source Map │  推荐   │
├─────────────────────────────────────────────────────────────┤
│                    代码质量层                                │
├─────────────────────────────────────────────────────────────┤
│  ESLint     │  Prettier   │  Husky      │  lint-staged │  测试  │
│  代码检查    │  代码格式化  │  Git 钩子   │  暂存区检查  │  框架   │
├─────────────────────────────────────────────────────────────┤
│                    构建优化层                                │
├─────────────────────────────────────────────────────────────┤
│  Vite       │  代码分割   │  资源优化   │  缓存策略   │  压缩   │
│  构建工具    │  懒加载     │  图片处理   │  浏览器缓存  │  混淆   │
├─────────────────────────────────────────────────────────────┤
│                    配置管理层                                │
├─────────────────────────────────────────────────────────────┤
│  环境变量   │  依赖管理   │  脚本命令   │  路径别名   │  模块   │
│  .env       │  package.json│  npm scripts│  tsconfig   │  解析   │
└─────────────────────────────────────────────────────────────┘
```

### 技术栈优化

- **代码质量**: ESLint + Prettier + TypeScript strict
- **Git 工作流**: Husky + lint-staged + Conventional Commits
- **构建优化**: Vite + 代码分割 + 资源压缩
- **开发体验**: VS Code 配置 + 调试支持
- **测试框架**: Vitest + Testing Library
- **文档生成**: JSDoc + TypeDoc

## 组件和接口

### 1. ESLint 配置

```javascript
// eslint.config.js
import js from '@eslint/js';
import typescript from '@typescript-eslint/eslint-plugin';
import typescriptParser from '@typescript-eslint/parser';
import astro from 'eslint-plugin-astro';

export default [
  js.configs.recommended,
  {
    files: ['**/*.{js,mjs,cjs,ts,astro}'],
    plugins: {
      '@typescript-eslint': typescript,
      astro: astro,
    },
    languageOptions: {
      parser: typescriptParser,
      parserOptions: {
        ecmaVersion: 'latest',
        sourceType: 'module',
        project: './tsconfig.json',
      },
    },
    rules: {
      // TypeScript 规则
      '@typescript-eslint/no-unused-vars': 'error',
      '@typescript-eslint/no-explicit-any': 'warn',
      '@typescript-eslint/prefer-const': 'error',
      
      // 通用规则
      'no-console': 'warn',
      'no-debugger': 'error',
      'prefer-const': 'error',
      
      // Astro 特定规则
      'astro/no-conflict-set-directives': 'error',
      'astro/no-unused-define-vars-in-style': 'error',
    },
  },
  {
    files: ['**/*.astro'],
    processor: 'astro/client-side-ts',
  },
];
```

### 2. Prettier 配置

```javascript
// prettier.config.js
export default {
  // 基础格式化
  printWidth: 100,
  tabWidth: 2,
  useTabs: false,
  semi: true,
  singleQuote: true,
  quoteProps: 'as-needed',
  trailingComma: 'es5',
  bracketSpacing: true,
  bracketSameLine: false,
  arrowParens: 'avoid',
  
  // 文件特定配置
  overrides: [
    {
      files: '*.astro',
      options: {
        parser: 'astro',
      },
    },
    {
      files: '*.md',
      options: {
        printWidth: 80,
        proseWrap: 'always',
      },
    },
    {
      files: '*.json',
      options: {
        printWidth: 120,
      },
    },
  ],
  
  // 插件
  plugins: ['prettier-plugin-astro', 'prettier-plugin-tailwindcss'],
};
```

### 3. TypeScript 配置优化

```json
{
  "extends": "astro/tsconfigs/strict",
  "include": [
    ".astro/types.d.ts",
    "**/*",
    "src/**/*"
  ],
  "exclude": [
    "dist",
    "node_modules",
    ".astro"
  ],
  "compilerOptions": {
    // 严格类型检查
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    
    // 模块解析
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    
    // 路径别名
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@/components/*": ["src/components/*"],
      "@/layouts/*": ["src/layouts/*"],
      "@/utils/*": ["src/utils/*"],
      "@/styles/*": ["src/styles/*"],
      "@/content/*": ["src/content/*"]
    },
    
    // 其他选项
    "skipLibCheck": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true
  }
}
```

### 4. Vite 配置优化

```javascript
// astro.config.mjs
import { defineConfig } from 'astro/config';
import tailwindcss from '@tailwindcss/vite';
import { resolve } from 'path';

export default defineConfig({
  // 站点配置
  site: 'https://pennfly.com',
  base: '/',
  
  // 构建配置
  build: {
    assets: 'assets',
    assetsInlineLimit: 4096,
    cssCodeSplit: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['astro'],
          utils: ['src/utils'],
        },
      },
    },
  },
  
  // Vite 配置
  vite: {
    plugins: [tailwindcss()],
    
    // 路径别名
    resolve: {
      alias: {
        '@': resolve('./src'),
        '@/components': resolve('./src/components'),
        '@/layouts': resolve('./src/layouts'),
        '@/utils': resolve('./src/utils'),
        '@/styles': resolve('./src/styles'),
        '@/content': resolve('./src/content'),
      },
    },
    
    // 开发服务器
    server: {
      port: 4321,
      host: true,
      open: true,
    },
    
    // 构建优化
    build: {
      target: 'es2022',
      minify: 'esbuild',
      cssMinify: true,
      rollupOptions: {
        output: {
          manualChunks(id) {
            if (id.includes('node_modules')) {
              return 'vendor';
            }
            if (id.includes('src/utils')) {
              return 'utils';
            }
          },
        },
      },
    },
    
    // 优化依赖
    optimizeDeps: {
      include: ['astro', 'tailwindcss'],
    },
  },
  
  // 图片优化
  image: {
    domains: ['pennfly.com'],
    formats: ['webp', 'avif'],
    quality: 85,
  },
  
  // 实验性功能
  experimental: {
    contentCollectionCache: true,
  },
});
```

### 5. Package.json 优化

```json
{
  "name": "pennfly-private-academy",
  "type": "module",
  "version": "1.0.0",
  "description": "Pennfly Private Academy - 专业的私人学院教育平台",
  "keywords": ["astro", "education", "research", "blog", "i18n"],
  "author": "Pennfly",
  "license": "MIT",
  "homepage": "https://pennfly.com",
  "repository": {
    "type": "git",
    "url": "https://github.com/pennfly/pennfly-academy.git"
  },
  "engines": {
    "node": ">=18.0.0",
    "npm": ">=9.0.0"
  },
  "scripts": {
    "dev": "astro dev",
    "build": "astro build",
    "preview": "astro preview",
    "astro": "astro",
    "lint": "eslint src --ext .ts,.astro --fix",
    "lint:check": "eslint src --ext .ts,.astro",
    "format": "prettier --write src/**/*.{ts,astro,md,json}",
    "format:check": "prettier --check src/**/*.{ts,astro,md,json}",
    "type-check": "tsc --noEmit",
    "test": "vitest",
    "test:ui": "vitest --ui",
    "test:coverage": "vitest --coverage",
    "build:analyze": "astro build --analyze",
    "clean": "rm -rf dist .astro node_modules/.cache",
    "prepare": "husky install"
  },
  "dependencies": {
    "@tailwindcss/vite": "^4.1.11",
    "astro": "^5.12.9",
    "tailwindcss": "^4.1.11"
  },
  "devDependencies": {
    "@eslint/js": "^9.0.0",
    "@typescript-eslint/eslint-plugin": "^7.0.0",
    "@typescript-eslint/parser": "^7.0.0",
    "@vitest/ui": "^1.0.0",
    "eslint": "^9.0.0",
    "eslint-plugin-astro": "^0.31.0",
    "husky": "^9.0.0",
    "lint-staged": "^15.0.0",
    "prettier": "^3.0.0",
    "prettier-plugin-astro": "^0.13.0",
    "prettier-plugin-tailwindcss": "^0.5.0",
    "typescript": "^5.3.0",
    "vitest": "^1.0.0"
  },
  "lint-staged": {
    "*.{ts,astro}": [
      "eslint --fix",
      "prettier --write"
    ],
    "*.{md,json}": [
      "prettier --write"
    ]
  }
}
```

### 6. VS Code 配置

```json
// .vscode/settings.json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "explicit"
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.suggest.autoImports": true,
  "astro.typescript.allowArbitraryAttributes": true,
  "files.associations": {
    "*.astro": "astro"
  },
  "emmet.includeLanguages": {
    "astro": "html"
  },
  "tailwindCSS.includeLanguages": {
    "astro": "html"
  },
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/.astro": true
  }
}
```

```json
// .vscode/extensions.json
{
  "recommendations": [
    "astro-build.astro-vscode",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "streetsidesoftware.code-spell-checker",
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml"
  ]
}
```

### 7. Git 工作流配置

```javascript
// .husky/pre-commit
#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

npx lint-staged
```

```javascript
// .husky/commit-msg
#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

npx commitlint --edit $1
```

```javascript
// commitlint.config.js
export default {
  extends: ['@commitlint/config-conventional'],
  rules: {
    'type-enum': [
      2,
      'always',
      [
        'feat',     // 新功能
        'fix',      // 修复
        'docs',     // 文档
        'style',    // 格式
        'refactor', // 重构
        'test',     // 测试
        'chore',    // 构建过程或辅助工具的变动
        'perf',     // 性能优化
        'ci',       // CI配置
        'revert',   // 撤销
      ],
    ],
    'subject-max-length': [2, 'always', 100],
    'subject-case': [2, 'never', ['pascal-case', 'upper-case']],
  },
};
```

## 错误处理

### 1. 构建错误处理

```javascript
// src/utils/errorHandling.ts
export class BuildError extends Error {
  constructor(message: string, public code: string, public details?: any) {
    super(message);
    this.name = 'BuildError';
  }
}

export function handleBuildError(error: Error): void {
  console.error('Build failed:', error.message);
  
  if (error.stack) {
    console.error('Stack trace:', error.stack);
  }
  
  process.exit(1);
}
```

### 2. 开发环境错误处理

```javascript
// src/utils/devErrorHandler.ts
export function setupDevErrorHandler(): void {
  if (import.meta.env.DEV) {
    window.addEventListener('error', (event) => {
      console.error('Runtime error:', event.error);
    });
    
    window.addEventListener('unhandledrejection', (event) => {
      console.error('Unhandled promise rejection:', event.reason);
    });
  }
}
```

## 测试策略

### 1. 单元测试配置

```javascript
// vitest.config.ts
import { defineConfig } from 'vitest/config';
import { resolve } from 'path';

export default defineConfig({
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
  },
  resolve: {
    alias: {
      '@': resolve('./src'),
    },
  },
});
```

### 2. 测试工具函数

```typescript
// src/test/utils.ts
import { expect } from 'vitest';

export function expectToBeValidUrl(url: string): void {
  expect(() => new URL(url)).not.toThrow();
}

export function expectToHaveValidHtml(html: string): void {
  expect(html).toMatch(/^<!DOCTYPE html>/);
  expect(html).toContain('<html');
  expect(html).toContain('</html>');
}
```

## 性能优化

### 1. 构建性能优化

```javascript
// 代码分割策略
const chunkStrategy = {
  vendor: ['astro', 'tailwindcss'],
  utils: ['src/utils'],
  components: ['src/components'],
  i18n: ['src/i18n'],
};
```

### 2. 开发性能优化

```javascript
// 开发服务器优化
const devOptimization = {
  hmr: true,
  cors: true,
  proxy: {},
  fs: {
    strict: false,
  },
};
```

## 安全配置

### 1. 环境变量管理

```bash
# .env.example
# 站点配置
SITE_URL=https://pennfly.com
SITE_TITLE=Pennfly Private Academy

# 开发配置
NODE_ENV=development
DEBUG=false

# 第三方服务（示例）
# ANALYTICS_ID=
# COMMENT_SYSTEM_ID=
```

### 2. 安全头配置

```javascript
// src/middleware/security.ts
export function securityHeaders() {
  return {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'",
  };
}
```