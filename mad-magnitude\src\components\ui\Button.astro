---
/**
 * Enhanced <PERSON><PERSON> component for Pennfly Private Academy
 * Supports multiple variants, sizes, and states with full design system integration
 */

export interface Props {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'link' | 'destructive';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  type?: 'button' | 'submit' | 'reset';
  href?: string;
  target?: '_blank' | '_self' | '_parent' | '_top';
  class?: string;
  'aria-label'?: string;
}

const {
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  fullWidth = false,
  type = 'button',
  href,
  target,
  class: className = '',
  'aria-label': ariaLabel,
  ...rest
} = Astro.props;

// Size configurations
const sizeClasses = {
  sm: 'px-3 py-1.5 text-sm',
  md: 'px-4 py-2 text-base',
  lg: 'px-6 py-3 text-lg',
  xl: 'px-8 py-4 text-xl',
};

// Variant configurations using theme-aware classes
const variantClasses = {
  primary: `
    bg-theme-brand-primary text-theme-fg-inverse
    hover:bg-theme-interactive-hover
    active:bg-theme-interactive-active
    focus:ring-theme-interactive-focus
    shadow-theme-sm hover:shadow-theme-md
  `,
  secondary: `
    bg-theme-brand-secondary text-theme-fg-inverse
    hover:bg-opacity-90
    active:bg-opacity-80
    focus:ring-theme-brand-secondary
    shadow-theme-sm hover:shadow-theme-md
  `,
  outline: `
    bg-transparent text-theme-brand-primary
    border-2 border-theme-brand-primary
    hover:bg-theme-brand-primary hover:text-theme-fg-inverse
    active:bg-theme-interactive-active active:text-theme-fg-inverse
    focus:ring-theme-brand-primary
  `,
  ghost: `
    bg-transparent text-theme-brand-primary
    hover:bg-theme-bg-secondary
    active:bg-theme-bg-tertiary
    focus:ring-theme-brand-primary
  `,
  link: `
    bg-transparent text-theme-brand-primary
    hover:text-theme-brand-accent hover:underline
    active:text-theme-interactive-active
    focus:ring-theme-brand-primary
    p-0 h-auto shadow-none
  `,
  destructive: `
    bg-theme-semantic-error text-theme-fg-inverse
    hover:bg-red-600
    active:bg-red-700
    focus:ring-theme-semantic-error
    shadow-theme-sm hover:shadow-theme-md
  `,
};

// Base classes with theme-aware styling
const baseClasses = `
  inline-flex items-center justify-center
  font-medium rounded-lg
  transition-all duration-200 ease-in-out
  focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-theme-bg-primary
  disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none
  ${fullWidth ? 'w-full' : ''}
  ${loading ? 'cursor-wait' : ''}
`;

const combinedClasses = `
  ${baseClasses}
  ${sizeClasses[size]}
  ${variantClasses[variant]}
  ${className}
`
  .trim()
  .replace(/\s+/g, ' ');

// Determine if this should render as a link or button
const isLink = href && !disabled && !loading;
const Component = isLink ? 'a' : 'button';

// Prepare props for the component
const componentProps = isLink
  ? { href, target, role: 'button' }
  : { type, disabled: disabled || loading };
---

<Component
  class={combinedClasses}
  aria-label={ariaLabel}
  aria-disabled={disabled || loading}
  {...componentProps}
  {...rest}
>
  {
    loading && (
      <svg
        class="mr-2 -ml-1 h-4 w-4 animate-spin"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        aria-hidden="true"
      >
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" />
        <path
          class="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
    )
  }

  <slot />

  {
    isLink && target === '_blank' && (
      <svg
        class="ml-1 h-4 w-4"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
        aria-hidden="true"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
        />
      </svg>
    )
  }
</Component>

<style>
  /* Ensure smooth transitions respect user preferences */
  @media (prefers-reduced-motion: reduce) {
    button,
    a[role='button'] {
      transition: none !important;
    }

    .animate-spin {
      animation: none !important;
    }
  }

  /* Enhanced focus styles for better accessibility */
  button:focus-visible,
  a[role='button']:focus-visible {
    outline: 2px solid var(--color-interactive-focus);
    outline-offset: 2px;
  }

  /* Loading state improvements */
  button[aria-disabled='true'] {
    position: relative;
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    button,
    a[role='button'] {
      border: 2px solid currentColor;
    }
  }
</style>
