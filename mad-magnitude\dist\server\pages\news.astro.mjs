import { a as createComponent, h as renderComponent, r as renderScript, d as renderTemplate, m as maybeRenderHead, b as addAttribute } from "../assets/vendor-astro.kctgsZae.js";
import { i } from "../assets/vendor-astro.kctgsZae.js";
import "kleur/colors";
import { e as getCollection, i as groupByMonth, j as formatDate } from "../assets/utils.CcA_tyNa.js";
import { $ as $$Layout } from "../assets/Layout.BKd1ZXhO.js";
/* empty css                                */
const $$Index = createComponent(async ($$result, $$props, $$slots) => {
  const newsArticles = await getCollection("news");
  const sortedNews = newsArticles.filter((news) => !news.data.draft).sort((a, b) => b.data.publishDate.getTime() - a.data.publishDate.getTime());
  const newsByType = {
    all: sortedNews,
    research: sortedNews.filter((news) => news.data.type === "research"),
    announcement: sortedNews.filter((news) => news.data.type === "announcement"),
    reflection: sortedNews.filter((news) => news.data.type === "reflection"),
    milestone: sortedNews.filter((news) => news.data.type === "milestone")
  };
  const newsByMonth = groupByMonth(sortedNews);
  const stats = {
    total: sortedNews.length,
    research: newsByType.research.length,
    announcement: newsByType.announcement.length,
    reflection: newsByType.reflection.length,
    milestone: newsByType.milestone.length,
    featured: sortedNews.filter((news) => news.data.featured).length
  };
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "动态资讯 - Pennfly Private Academy", "description": "Pennfly Private Academy 的最新动态、研究进展和重要公告", "data-astro-cid-xzrtoo6z": true }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<main class="min-h-screen bg-gray-50" data-astro-cid-xzrtoo6z> <!-- 页面头部 --> <div class="bg-gradient-to-r from-blue-500 to-cyan-600 py-16 text-white" data-astro-cid-xzrtoo6z> <div class="container mx-auto px-6" data-astro-cid-xzrtoo6z> <div class="mb-6 flex items-center" data-astro-cid-xzrtoo6z> <span class="mr-4 text-5xl" data-astro-cid-xzrtoo6z>📰</span> <div data-astro-cid-xzrtoo6z> <h1 class="mb-2 text-4xl font-bold" data-astro-cid-xzrtoo6z>动态资讯</h1> <p class="text-xl opacity-90" data-astro-cid-xzrtoo6z>News & Updates</p> </div> </div> <p class="max-w-3xl text-lg opacity-90" data-astro-cid-xzrtoo6z>
及时分享研究院的最新动态、研究进展和重要公告。
          了解最新的学术思考、项目进展和未来规划，与研究院保持同步。
</p> </div> </div> <!-- 动态列表 --> <div class="container mx-auto px-6 py-12" data-astro-cid-xzrtoo6z> <!-- 统计和筛选 --> <div class="mb-8" data-astro-cid-xzrtoo6z> <div class="mb-6 flex flex-wrap items-center justify-between gap-4" data-astro-cid-xzrtoo6z> <h2 class="text-2xl font-bold text-gray-800" data-astro-cid-xzrtoo6z>最新动态</h2> <div class="flex items-center space-x-4" data-astro-cid-xzrtoo6z> <!-- 视图切换 --> <div class="flex rounded-lg border border-gray-300 bg-white" data-astro-cid-xzrtoo6z> <button class="view-toggle active rounded-l-lg px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50" data-view="list" data-astro-cid-xzrtoo6z>
📋 列表视图
</button> <button class="view-toggle rounded-r-lg border-l border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50" data-view="timeline" data-astro-cid-xzrtoo6z>
📅 时间线
</button> </div> </div> </div> <!-- 分类筛选 --> <div class="mb-6 flex flex-wrap gap-2" data-astro-cid-xzrtoo6z> <button class="filter-btn active" data-type="all" data-astro-cid-xzrtoo6z>
全部 (${stats.total})
</button> <button class="filter-btn" data-type="research" data-astro-cid-xzrtoo6z>
🔬 研究动态 (${stats.research})
</button> <button class="filter-btn" data-type="announcement" data-astro-cid-xzrtoo6z>
📢 重要公告 (${stats.announcement})
</button> <button class="filter-btn" data-type="reflection" data-astro-cid-xzrtoo6z>
💭 个人思考 (${stats.reflection})
</button> ${stats.milestone > 0 && renderTemplate`<button class="filter-btn" data-type="milestone" data-astro-cid-xzrtoo6z>
🎯 里程碑 (${stats.milestone})
</button>`} </div> <!-- 统计信息 --> <div class="mb-8 grid grid-cols-2 gap-4 md:grid-cols-4" data-astro-cid-xzrtoo6z> <div class="rounded-lg bg-blue-50 p-4 text-center" data-astro-cid-xzrtoo6z> <div class="text-2xl font-bold text-blue-600" data-astro-cid-xzrtoo6z>${stats.total}</div> <div class="text-sm text-blue-800" data-astro-cid-xzrtoo6z>总动态数</div> </div> <div class="rounded-lg bg-green-50 p-4 text-center" data-astro-cid-xzrtoo6z> <div class="text-2xl font-bold text-green-600" data-astro-cid-xzrtoo6z>${stats.research}</div> <div class="text-sm text-green-800" data-astro-cid-xzrtoo6z>研究动态</div> </div> <div class="rounded-lg bg-purple-50 p-4 text-center" data-astro-cid-xzrtoo6z> <div class="text-2xl font-bold text-purple-600" data-astro-cid-xzrtoo6z>${stats.announcement}</div> <div class="text-sm text-purple-800" data-astro-cid-xzrtoo6z>重要公告</div> </div> <div class="rounded-lg bg-orange-50 p-4 text-center" data-astro-cid-xzrtoo6z> <div class="text-2xl font-bold text-orange-600" data-astro-cid-xzrtoo6z>${stats.featured}</div> <div class="text-sm text-orange-800" data-astro-cid-xzrtoo6z>置顶动态</div> </div> </div> </div> <div class="mb-12" data-astro-cid-xzrtoo6z> <!-- 列表视图 --> <div id="list-view" class="view-content" data-astro-cid-xzrtoo6z> ${sortedNews.length > 0 ? renderTemplate`<div class="space-y-6" data-astro-cid-xzrtoo6z> ${Object.entries(newsByType).map(([type, articles]) => renderTemplate`<div${addAttribute(`news-category ${type === "all" ? "" : "hidden"}`, "class")}${addAttribute(type, "data-type")} data-astro-cid-xzrtoo6z> ${articles.map((news) => renderTemplate`<article class="news-item mb-6 rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition-all hover:-translate-y-1 hover:shadow-md"${addAttribute(news.data.type, "data-type")} data-astro-cid-xzrtoo6z> <div class="mb-4 flex items-start justify-between" data-astro-cid-xzrtoo6z> <div class="flex-1" data-astro-cid-xzrtoo6z> <div class="mb-2 flex items-center" data-astro-cid-xzrtoo6z> <span class="mr-3 text-2xl" data-astro-cid-xzrtoo6z> ${news.data.type === "research" ? "🔬" : news.data.type === "announcement" ? "📢" : news.data.type === "milestone" ? "🎯" : "💭"} </span> <span${addAttribute(`rounded px-2 py-1 text-xs font-medium ${news.data.type === "research" ? "bg-blue-100 text-blue-800" : news.data.type === "announcement" ? "bg-purple-100 text-purple-800" : news.data.type === "milestone" ? "bg-green-100 text-green-800" : "bg-orange-100 text-orange-800"}`, "class")} data-astro-cid-xzrtoo6z> ${news.data.type === "research" ? "研究动态" : news.data.type === "announcement" ? "重要公告" : news.data.type === "milestone" ? "里程碑" : "个人思考"} </span> ${news.data.relatedInstitute && news.data.relatedInstitute.length > 0 && renderTemplate`<div class="ml-2 flex space-x-1" data-astro-cid-xzrtoo6z> ${news.data.relatedInstitute.map((institute) => renderTemplate`<span class="rounded bg-gray-100 px-1 py-0.5 text-xs text-gray-600" data-astro-cid-xzrtoo6z> ${institute === "economics" ? "💰" : institute === "philosophy" ? "🤔" : institute === "internet" ? "🌐" : institute === "ai" ? "🤖" : "🔮"} </span>`)} </div>`} </div> <h3 class="mb-2 text-xl font-semibold text-gray-800" data-astro-cid-xzrtoo6z> <a${addAttribute(`/news/${news.slug}`, "href")} class="transition-colors hover:text-blue-600" data-astro-cid-xzrtoo6z> ${news.data.title.zh} </a> </h3> ${news.data.summary && renderTemplate`<p class="mb-3 text-gray-600" data-astro-cid-xzrtoo6z>${news.data.summary}</p>`} <p class="mb-3 text-gray-600" data-astro-cid-xzrtoo6z>${news.data.description.zh}</p> <div class="flex items-center space-x-4 text-sm text-gray-500" data-astro-cid-xzrtoo6z> <span data-astro-cid-xzrtoo6z>📅 ${formatDate(news.data.publishDate)}</span> ${news.data.readingTime && renderTemplate`<span data-astro-cid-xzrtoo6z>⏱️ ${news.data.readingTime} 分钟</span>`} ${news.data.author && renderTemplate`<span data-astro-cid-xzrtoo6z>👤 ${news.data.author}</span>`} </div> </div> ${news.data.featured && renderTemplate`<div class="ml-4" data-astro-cid-xzrtoo6z> <span class="rounded bg-red-100 px-2 py-1 text-xs font-medium text-red-800" data-astro-cid-xzrtoo6z>
📌 置顶
</span> </div>`} </div> ${news.data.tags && news.data.tags.length > 0 && renderTemplate`<div class="flex flex-wrap gap-2" data-astro-cid-xzrtoo6z> ${news.data.tags.map((tag) => renderTemplate`<span class="cursor-pointer rounded-full bg-gray-100 px-3 py-1 text-xs text-gray-700 transition-colors hover:bg-gray-200" data-astro-cid-xzrtoo6z>
#${tag} </span>`)} </div>`} </article>`)} </div>`)} </div>` : renderTemplate`<div class="py-12 text-center" data-astro-cid-xzrtoo6z> <div class="mb-4 text-6xl" data-astro-cid-xzrtoo6z>📰</div> <h3 class="mb-2 text-xl font-semibold text-gray-800" data-astro-cid-xzrtoo6z>暂无动态资讯</h3> <p class="mb-6 text-gray-600" data-astro-cid-xzrtoo6z>研究院动态正在更新中，敬请期待最新的研究进展</p> <a href="/admin" class="inline-block rounded-lg bg-blue-600 px-6 py-2 text-white transition-colors hover:bg-blue-700" data-astro-cid-xzrtoo6z>
发布动态
</a> </div>`} </div> <!-- 时间线视图 --> <div id="timeline-view" class="view-content hidden" data-astro-cid-xzrtoo6z> ${Object.entries(newsByMonth).map(([month, articles]) => renderTemplate`<div class="mb-8" data-astro-cid-xzrtoo6z> <div class="mb-4 flex items-center" data-astro-cid-xzrtoo6z> <div class="mr-4 rounded-lg bg-blue-100 px-3 py-1 text-sm font-medium text-blue-800" data-astro-cid-xzrtoo6z> ${month} </div> <div class="h-px flex-1 bg-gray-200" data-astro-cid-xzrtoo6z></div> </div> <div class="space-y-4" data-astro-cid-xzrtoo6z> ${articles.map((news) => renderTemplate`<div class="news-item flex items-start space-x-4"${addAttribute(news.data.type, "data-type")} data-astro-cid-xzrtoo6z> <div class="flex-shrink-0" data-astro-cid-xzrtoo6z> <div class="flex h-10 w-10 items-center justify-center rounded-full border-2 border-blue-200 bg-white" data-astro-cid-xzrtoo6z> <span class="text-sm" data-astro-cid-xzrtoo6z> ${news.data.type === "research" ? "🔬" : news.data.type === "announcement" ? "📢" : news.data.type === "milestone" ? "🎯" : "💭"} </span> </div> </div> <div class="flex-1 rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-shadow hover:shadow-md" data-astro-cid-xzrtoo6z> <div class="mb-2 flex items-center justify-between" data-astro-cid-xzrtoo6z> <h4 class="font-semibold text-gray-800" data-astro-cid-xzrtoo6z> <a${addAttribute(`/news/${news.slug}`, "href")} class="transition-colors hover:text-blue-600" data-astro-cid-xzrtoo6z> ${news.data.title.zh} </a> </h4> <span class="text-xs text-gray-500" data-astro-cid-xzrtoo6z> ${formatDate(news.data.publishDate)} </span> </div> <p class="mb-2 text-sm text-gray-600" data-astro-cid-xzrtoo6z>${news.data.description.zh}</p> ${news.data.tags && news.data.tags.length > 0 && renderTemplate`<div class="flex flex-wrap gap-1" data-astro-cid-xzrtoo6z> ${news.data.tags.slice(0, 3).map((tag) => renderTemplate`<span class="rounded bg-gray-100 px-2 py-0.5 text-xs text-gray-600" data-astro-cid-xzrtoo6z>
#${tag} </span>`)} </div>`} </div> </div>`)} </div> </div>`)} </div> </div> <!-- 返回首页 --> <div class="text-center" data-astro-cid-xzrtoo6z> <a href="/" class="inline-block rounded-lg bg-gray-600 px-6 py-2 text-white transition-colors hover:bg-gray-700" data-astro-cid-xzrtoo6z>
← 返回研究院首页
</a> </div> </div> </main> ` })}  ${renderScript($$result, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/news/index.astro?astro&type=script&index=0&lang.ts")}`;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/news/index.astro", void 0);
const $$file = "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/news/index.astro";
const $$url = "/news";
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({ __proto__: null, default: $$Index, file: $$file, url: $$url }, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  i as renderers
};
