---
import { getCollection } from 'astro:content';
import Layout from '@/layouts/Layout.astro';

const reflections = await getCollection('reflections');
const recent = reflections
  .filter(p => !p.data.draft)
  .sort((a, b) => b.data.publishDate.getTime() - a.data.publishDate.getTime());
---

<Layout title="个人思考">
  <main class="container mx-auto px-4 py-8 max-w-6xl">
    <!-- 页面头部 -->
    <header class="mb-12">
      <h1 class="text-3xl md:text-4xl font-bold mb-4">个人思考</h1>
      <p class="text-gray-600 max-w-3xl">
        记录个人成长与思考，分享对技术、教育和人生的感悟。这些思考源于实践，旨在启发更多深入的讨论。
      </p>
    </header>

    <!-- 最新思考 -->
    <section>
      <div class="space-y-6">
        {recent.map(post => (
          <article class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow duration-300">
            <div class="flex flex-col md:flex-row md:items-start gap-4">
              <div class="md:w-2/3">
                <div class="flex items-center text-sm text-gray-500 mb-2">
                  <span>{post.data.publishDate.toLocaleDateString('zh-CN')}</span>
                  <span class="mx-2">•</span>
                  <span class="bg-gray-100 px-2 py-1 rounded text-xs">思考</span>
                  {post.data.mood && (
                    <span class="mx-2">•</span>
                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">{post.data.mood}</span>
                  )}
                </div>
                <h3 class="text-xl font-semibold mb-2">
                  <a href={`/reflections/${post.slug}`} class="hover:text-blue-600">
                    {post.data.title.zh}
                  </a>
                </h3>
                {post.data.title.en && (
                  <p class="text-gray-600 italic mb-3 text-sm">{post.data.title.en}</p>
                )}
                <p class="text-gray-700 mb-4">{post.data.description.zh}</p>
                <div class="flex flex-wrap gap-2">
                  {post.data.tags.map(tag => (
                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
              <div class="md:w-1/3 flex justify-center">
                <a 
                  href={`/reflections/${post.slug}`}
                  class="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium"
                >
                  阅读全文
                  <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                </a>
              </div>
            </div>
          </article>
        ))}
      </div>
    </section>

    <!-- 分页 -->
    <div class="mt-12 flex justify-center">
      <nav class="flex items-center space-x-2">
        <button class="px-3 py-2 rounded-md bg-gray-100 text-gray-700 hover:bg-gray-200 disabled:opacity-50" disabled>
          上一页
        </button>
        <button class="px-3 py-2 rounded-md bg-blue-600 text-white">1</button>
        <button class="px-3 py-2 rounded-md bg-gray-100 text-gray-700 hover:bg-gray-200">2</button>
        <button class="px-3 py-2 rounded-md bg-gray-100 text-gray-700 hover:bg-gray-200">3</button>
        <button class="px-3 py-2 rounded-md bg-gray-100 text-gray-700 hover:bg-gray-200">
          下一页
        </button>
      </nav>
    </div>
  </main>
</Layout>
