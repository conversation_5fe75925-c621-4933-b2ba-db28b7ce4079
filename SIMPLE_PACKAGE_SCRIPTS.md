# 简化版package.json脚本配置

以下是一个简化版的package.json脚本配置，保留了核心开发命令，移除了不必要的复杂性，适合小型项目或项目初期使用。

## 简化版scripts配置

```json
{
  "scripts": {
    // 核心开发命令
    "dev": "astro dev",
    "build": "astro build",
    "preview": "astro preview",

    // 代码质量命令
    "lint": "eslint src --ext .ts,.astro --fix",
    "lint:check": "eslint src --ext .ts,.astro",
    "format": "prettier --write "src/**/*.{ts,astro,md,json}"",
    "format:check": "prettier --check "src/**/*.{ts,astro,md,json}"",
    "type-check": "tsc --noEmit",

    // 测试命令
    "test": "vitest",
    "test:run": "vitest run",

    // 维护命令
    "clean": "rimraf dist .astro",
    "prepare": "husky install",

    // 组合命令
    "check": "npm run lint:check && npm run format:check && npm run type-check",
    "fix": "npm run lint && npm run format"
  }
}
```

## 简化说明

1. **移除复杂脚本**:
   - 删除了构建分析脚本（build:analyze, analyze:size等）
   - 移除了图片优化脚本
   - 简化了安全检查脚本

2. **保留核心功能**:
   - 仍然支持开发、构建和预览
   - 仍然提供代码质量检查
   - 仍然支持基本测试

3. **简化组合命令**:
   - 只保留最常用的组合命令
   - 减少命令之间的依赖关系

## 对比原配置

原配置有30+个脚本，而简化版只保留了15个核心脚本，减少了约50%的脚本数量，同时保留了最重要的开发功能。

## 使用建议

1. 可以根据项目需要逐步添加更多脚本
2. 考虑将不常用的脚本移到单独的脚本文件中
3. 保持脚本命名清晰直观
4. 定期评估脚本的使用频率，移除不必要的脚本

## 进阶简化方案

如果项目非常简单，还可以进一步简化：

```json
{
  "scripts": {
    "dev": "astro dev",
    "build": "astro build",
    "preview": "astro preview",
    "lint": "eslint src --ext .ts,.astro --fix",
    "format": "prettier --write "src/**/*.{ts,astro,md,json}"",
    "check": "npm run lint && npm run format",
    "clean": "rimraf dist .astro"
  }
}
```

这种极简版本只保留了7个最核心的脚本，适合非常简单的项目或快速原型开发。
