/**
 * Security middleware for Astro application
 * Applies security headers and validates requests
 */

import { getEnvConfig } from '@/utils/env.js';
import { createSecurityMiddleware, getSecurityHeaders } from '@/utils/security.js';

/**
 * Apply security headers to all responses
 */
export function applySecurityHeaders(response: Response): Response {
  const headers = getSecurityHeaders();

  // Apply all security headers
  Object.entries(headers).forEach(([key, value]) => {
    if (value && typeof value === 'string') {
      response.headers.set(key, value);
    }
  });

  return response;
}

/**
 * Validate request security
 */
export function validateRequestSecurity(request: Request): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  const config = getEnvConfig();
  const middleware = createSecurityMiddleware();

  // Check rate limiting
  if (!middleware.checkRateLimit(request)) {
    errors.push('Rate limit exceeded');
  }

  // Validate origin for POST requests
  if (request.method === 'POST' && !middleware.validateOrigin(request)) {
    errors.push('Invalid request origin');
  }

  // Check content type for POST requests
  if (request.method === 'POST') {
    const contentType = request.headers.get('content-type');
    if (
      !contentType ||
      (!contentType.includes('application/json') &&
        !contentType.includes('application/x-www-form-urlencoded') &&
        !contentType.includes('multipart/form-data'))
    ) {
      errors.push('Invalid content type');
    }
  }

  // Check for suspicious user agents
  const userAgent = request.headers.get('user-agent');
  if (!userAgent || userAgent.length < 10) {
    errors.push('Suspicious or missing user agent');
  }

  // Check for required headers in production
  if (config.NODE_ENV === 'production') {
    const requiredHeaders = ['accept', 'accept-language'];
    requiredHeaders.forEach(header => {
      if (!request.headers.get(header)) {
        errors.push(`Missing required header: ${header}`);
      }
    });
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Create security response with appropriate headers
 */
export function createSecurityResponse(
  body: string | object,
  options: {
    status?: number;
    statusText?: string;
    headers?: Record<string, string>;
  } = {}
): Response {
  const responseBody = typeof body === 'string' ? body : JSON.stringify(body);
  const contentType = typeof body === 'string' ? 'text/plain' : 'application/json';

  const response = new Response(responseBody, {
    status: options.status || 200,
    statusText: options.statusText,
    headers: {
      'Content-Type': contentType,
      ...options.headers,
    },
  });

  return applySecurityHeaders(response);
}

/**
 * Create error response with security headers
 */
export function createSecurityErrorResponse(
  message: string,
  status: number = 400,
  details?: any
): Response {
  const errorBody = {
    error: message,
    status,
    timestamp: new Date().toISOString(),
    ...(details && { details }),
  };

  return createSecurityResponse(errorBody, {
    status,
    statusText: message,
  });
}

/**
 * Middleware function for API routes
 */
export function withSecurity<T extends any[]>(
  handler: (...args: T) => Promise<Response> | Response
) {
  return async (...args: T): Promise<Response> => {
    try {
      // Extract request from arguments (typically first argument)
      const request = args.find(arg => arg instanceof Request) as Request;

      if (request) {
        // Validate request security
        const validation = validateRequestSecurity(request);

        if (!validation.isValid) {
          return createSecurityErrorResponse('Security validation failed', 403, {
            errors: validation.errors,
          });
        }
      }

      // Execute the handler
      const response = await handler(...args);

      // Apply security headers to the response
      return applySecurityHeaders(response);
    } catch (error) {
      console.error('Security middleware error:', error);

      return createSecurityErrorResponse('Internal server error', 500);
    }
  };
}

/**
 * CORS middleware for API routes
 */
export function withCORS(
  handler: (request: Request) => Promise<Response> | Response,
  options: {
    origin?: string | string[];
    methods?: string[];
    allowedHeaders?: string[];
    credentials?: boolean;
  } = {}
) {
  return async (request: Request): Promise<Response> => {
    const config = getEnvConfig();

    // Handle preflight requests
    if (request.method === 'OPTIONS') {
      const corsHeaders = {
        'Access-Control-Allow-Origin': Array.isArray(options.origin)
          ? options.origin.join(', ')
          : options.origin || config.CORS_ORIGIN,
        'Access-Control-Allow-Methods': (
          options.methods || ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']
        ).join(', '),
        'Access-Control-Allow-Headers': (
          options.allowedHeaders || [
            'Content-Type',
            'Authorization',
            'X-Requested-With',
            'Accept',
            'Origin',
          ]
        ).join(', '),
        'Access-Control-Max-Age': '86400', // 24 hours
      };

      if (options.credentials || config.CORS_CREDENTIALS) {
        corsHeaders['Access-Control-Allow-Credentials'] = 'true';
      }

      return createSecurityResponse('', {
        status: 204,
        headers: corsHeaders,
      });
    }

    // Execute handler and add CORS headers
    const response = await handler(request);

    response.headers.set(
      'Access-Control-Allow-Origin',
      Array.isArray(options.origin)
        ? options.origin.join(', ')
        : options.origin || config.CORS_ORIGIN
    );

    if (options.credentials || config.CORS_CREDENTIALS) {
      response.headers.set('Access-Control-Allow-Credentials', 'true');
    }

    return applySecurityHeaders(response);
  };
}

/**
 * Rate limiting middleware
 */
export function withRateLimit(
  handler: (request: Request) => Promise<Response> | Response,
  options: {
    maxRequests?: number;
    windowMs?: number;
    message?: string;
  } = {}
) {
  return async (request: Request): Promise<Response> => {
    const middleware = createSecurityMiddleware();

    if (!middleware.checkRateLimit(request)) {
      return createSecurityErrorResponse(options.message || 'Rate limit exceeded', 429);
    }

    const response = await handler(request);
    return applySecurityHeaders(response);
  };
}
