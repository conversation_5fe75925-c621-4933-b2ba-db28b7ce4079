---
/**
 * Enhanced Badge component for Pennfly Private Academy
 * Provides flexible labeling with consistent styling and semantic meaning
 */

export interface Props {
  variant?:
    | 'default'
    | 'primary'
    | 'secondary'
    | 'success'
    | 'warning'
    | 'error'
    | 'info'
    | 'outline';
  size?: 'sm' | 'md' | 'lg';
  rounded?: boolean;
  removable?: boolean;
  interactive?: boolean;
  href?: string;
  target?: '_blank' | '_self' | '_parent' | '_top';
  class?: string;
  'aria-label'?: string;
  onRemove?: string; // Event handler name for removal
}

const {
  variant = 'default',
  size = 'md',
  rounded = false,
  removable = false,
  interactive = false,
  href,
  target,
  class: className = '',
  'aria-label': ariaLabel,
  onRemove,
  ...rest
} = Astro.props;

// Size configurations
const sizeClasses = {
  sm: 'px-2 py-0.5 text-xs',
  md: 'px-2.5 py-1 text-sm',
  lg: 'px-3 py-1.5 text-base',
};

// Variant configurations using theme-aware classes
const variantClasses = {
  default: `
    bg-theme-bg-tertiary text-theme-fg-secondary
    border border-theme-border-default
  `,
  primary: `
    bg-theme-brand-primary text-theme-fg-inverse
    border border-theme-brand-primary
  `,
  secondary: `
    bg-theme-brand-secondary text-theme-fg-inverse
    border border-theme-brand-secondary
  `,
  success: `
    bg-theme-semantic-success text-white
    border border-theme-semantic-success
  `,
  warning: `
    bg-theme-semantic-warning text-white
    border border-theme-semantic-warning
  `,
  error: `
    bg-theme-semantic-error text-white
    border border-theme-semantic-error
  `,
  info: `
    bg-theme-semantic-info text-white
    border border-theme-semantic-info
  `,
  outline: `
    bg-transparent text-theme-brand-primary
    border border-theme-brand-primary
  `,
};

// Interactive states
const interactiveClasses =
  interactive || href
    ? `
  transition-all duration-200 ease-in-out
  hover:opacity-80 hover:scale-105
  active:scale-95
  focus:outline-none focus:ring-2 focus:ring-theme-interactive-focus focus:ring-offset-1
  cursor-pointer
`
    : '';

// Base classes
const baseClasses = `
  inline-flex items-center justify-center
  font-medium
  ${rounded ? 'rounded-full' : 'rounded-md'}
  ${sizeClasses[size]}
  ${variantClasses[variant]}
  ${interactiveClasses}
`;

const combinedClasses = `
  ${baseClasses}
  ${className}
`
  .trim()
  .replace(/\s+/g, ' ');

// Determine if this should render as a link or span
const isLink = href && !rest.disabled;
const Component = isLink ? 'a' : 'span';

// Prepare props for the component
const componentProps = isLink ? { href, target } : {};

// Add role for interactive non-link badges
const accessibilityProps = interactive && !isLink ? { role: 'button', tabindex: '0' } : {};
---

<Component
  class={combinedClasses}
  aria-label={ariaLabel}
  {...componentProps}
  {...accessibilityProps}
  {...rest}
>
  <slot />

  {
    removable && (
      <button
        type="button"
        class="focus:ring-theme-interactive-focus -mr-1 ml-1 rounded-full p-0.5 transition-colors hover:bg-black/10 focus:ring-1 focus:outline-none"
        aria-label="Remove badge"
        onclick={onRemove}
      >
        <svg
          class="h-3 w-3"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          aria-hidden="true"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>
    )
  }

  {
    isLink && target === '_blank' && (
      <svg
        class="ml-1 h-3 w-3"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
        aria-hidden="true"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
        />
      </svg>
    )
  }
</Component>

<style>
  /* Ensure smooth transitions respect user preferences */
  @media (prefers-reduced-motion: reduce) {
    span,
    a {
      transition: none !important;
      transform: none !important;
    }
  }

  /* Enhanced focus styles for better accessibility */
  span[role='button']:focus-visible,
  a:focus-visible {
    outline: 2px solid var(--color-interactive-focus);
    outline-offset: 2px;
  }

  /* Keyboard interaction for interactive badges */
  span[role='button']:focus,
  span[role='button']:hover {
    opacity: 0.8;
    transform: scale(1.05);
  }

  span[role='button']:active {
    transform: scale(0.95);
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    span,
    a {
      border-width: 2px;
      font-weight: 600;
    }
  }

  /* Print styles */
  @media print {
    span,
    a {
      background: transparent !important;
      color: black !important;
      border: 1px solid black !important;
      transform: none !important;
    }

    button {
      display: none !important;
    }
  }

  /* Remove button hover states */
  button:hover {
    background-color: rgba(0, 0, 0, 0.1);
  }

  /* Dark theme adjustments for remove button */
  [data-theme='dark'] button:hover,
  [data-theme='highContrast'] button:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
</style>
