# 内容管理设计文档（简化版）

## 概述

基于简洁化原则，重新设计内容管理系统。专注于**简单的内容创作和发布流程**，避免复杂的内容管理后台和功能。

## 设计原则

### 1. 创作优先

- 专注于内容的创作体验
- 使用熟悉的 Markdown 格式
- 避免复杂的编辑界面

### 2. 流程简单

- 减少内容发布的步骤
- 自动化常见的管理任务
- 避免复杂的审核流程

### 3. 工具轻量

- 使用现有的开发工具
- 避免额外的管理界面
- 保持技术栈的简洁

## 简化的内容管理架构

### 内容创作流程

```
1. 创建Markdown文件 → 2. 编写内容 → 3. Git提交 → 4. 自动部署
```

### 文件组织结构

```
src/content/
├── news/           # 动态资讯
├── logs/           # 研究日志
├── economics/      # 经济研究所
├── philosophy/     # 哲学研究所
├── internet/       # 互联网研究所
├── ai/             # AI研究所
├── future/         # 未来研究所
└── products/       # 产品发布
```

### 内容模板（简化）

```markdown
---
title:
  zh: "文章标题"
description:
  zh: "文章描述"
publishDate: 2025-01-15
tags: ["标签1", "标签2"]
featured: false
---

# 文章标题

文章内容...
```

## 技术实现

### 基础工具函数

- 内容列表获取
- 日期格式化
- 摘要生成
- 搜索索引构建

### 图片处理（简化）

- 基础的图片优化
- 懒加载支持
- 响应式图片

### SEO 优化（基础）

- 自动生成 meta 标签
- 简单的站点地图
- 基础的社交媒体标签

## 删除的复杂功能

- ❌ 在线编辑器和管理后台
- ❌ 复杂的权限和审核系统
- ❌ 自动化内容生成和分析
- ❌ 复杂的备份和监控系统

## 维护策略

### 简单的工作流

1. 本地 Markdown 编辑
2. Git 版本控制
3. 自动构建部署
4. 基础错误处理

### 日常维护

- 定期检查构建状态
- 清理无用文件
- 更新依赖版本
- 整理内容标签
