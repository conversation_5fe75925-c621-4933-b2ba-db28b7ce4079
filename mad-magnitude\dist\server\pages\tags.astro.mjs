import { a as createComponent, h as renderComponent, r as renderScript, d as renderTemplate, m as maybeRenderHead, b as addAttribute } from "../assets/vendor-astro.kctgsZae.js";
import { i } from "../assets/vendor-astro.kctgsZae.js";
import "kleur/colors";
import { $ as $$Layout } from "../assets/Layout.BKd1ZXhO.js";
import { a as $$TagStats, b as $$TagCloud } from "../assets/tags.hhthsB1y.js";
import { g as globalTagManager } from "../assets/utils.CcA_tyNa.js";
/* empty css                                */
const $$Index = createComponent(async ($$result, $$props, $$slots) => {
  const tagStats = await globalTagManager.getTagStats();
  const title = "标签总览 - Pennfly Private Academy";
  const description = `探索 ${tagStats.totalTags} 个标签，涵盖技术、经济、哲学等多个领域的学术内容分类。`;
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": title, "description": description, "data-astro-cid-os4i7owy": true }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<main class="tags-overview-page" data-astro-cid-os4i7owy> <!-- 页面头部 --> <header class="page-header" data-astro-cid-os4i7owy> <div class="container" data-astro-cid-os4i7owy> <div class="header-content" data-astro-cid-os4i7owy> <h1 class="page-title" data-astro-cid-os4i7owy> <span class="title-icon" data-astro-cid-os4i7owy>🏷️</span>
标签总览
</h1> <p class="page-description" data-astro-cid-os4i7owy>
探索我们的内容标签体系，发现感兴趣的主题和领域。 目前共有 <strong data-astro-cid-os4i7owy>${tagStats.totalTags}</strong> 个标签， 累计使用 <strong data-astro-cid-os4i7owy>${tagStats.totalUniqueContent}</strong> 次。
</p> </div> </div> </header> <div class="container" data-astro-cid-os4i7owy> <div class="content-grid" data-astro-cid-os4i7owy> <!-- 标签统计 --> <section class="stats-section" data-astro-cid-os4i7owy> ${renderComponent($$result2, "TagStats", $$TagStats, { "showCategories": true, "showTrends": true, "data-astro-cid-os4i7owy": true })} </section> <!-- 标签云 --> <section class="cloud-section" data-astro-cid-os4i7owy> <div class="section-header" data-astro-cid-os4i7owy> <h2 class="section-title" data-astro-cid-os4i7owy> <span class="title-icon" data-astro-cid-os4i7owy>☁️</span>
热门标签云
</h2> <p class="section-description" data-astro-cid-os4i7owy>标签大小反映使用频率，点击标签查看相关内容</p> </div> ${renderComponent($$result2, "TagCloud", $$TagCloud, { "maxTags": 50, "showCount": true, "size": "medium", "interactive": true, "data-astro-cid-os4i7owy": true })} </section> <!-- 分类标签列表 --> <section class="categories-section" data-astro-cid-os4i7owy> <div class="section-header" data-astro-cid-os4i7owy> <h2 class="section-title" data-astro-cid-os4i7owy> <span class="title-icon" data-astro-cid-os4i7owy>📂</span>
分类标签
</h2> <p class="section-description" data-astro-cid-os4i7owy>按主题分类浏览所有标签</p> </div> <div class="categories-grid" data-astro-cid-os4i7owy> ${Object.entries(tagStats.tagsByCategory).map(([category, tags]) => renderTemplate`<div class="category-section" data-astro-cid-os4i7owy> <h3 class="category-title" data-astro-cid-os4i7owy> <span class="category-icon" data-astro-cid-os4i7owy> ${category === "technology" && "🔬"} ${category === "economics" && "💰"} ${category === "philosophy" && "🤔"} ${category === "society" && "🏛️"} ${category === "research" && "📊"} ${category === "tools" && "🛠️"} ${category === "general" && "📝"} </span> ${category === "technology" && "技术"} ${category === "economics" && "经济"} ${category === "philosophy" && "哲学"} ${category === "society" && "社会"} ${category === "research" && "研究"} ${category === "tools" && "工具"} ${category === "general" && "通用"} <span class="category-count" data-astro-cid-os4i7owy>(${tags.length})</span> </h3> <div class="category-tags" data-astro-cid-os4i7owy> ${tags.slice(0, 12).map((tag) => renderTemplate`<a${addAttribute(`/tags/${encodeURIComponent(tag.name)}`, "href")} class="category-tag"${addAttribute(`--tag-color: ${tag.color}`, "style")}${addAttribute(`${tag.name} (${tag.count} 篇内容)`, "title")} data-astro-cid-os4i7owy> <span class="tag-name" data-astro-cid-os4i7owy>${tag.name}</span> <span class="tag-count" data-astro-cid-os4i7owy>(${tag.count})</span> </a>`)} ${tags.length > 12 && renderTemplate`<button class="show-more-btn"${addAttribute(category, "data-category")} data-astro-cid-os4i7owy>
查看更多 (${tags.length - 12})
</button>`} </div> </div>`)} </div> </section> <!-- 快速导航 --> <aside class="quick-nav" data-astro-cid-os4i7owy> <h3 class="nav-title" data-astro-cid-os4i7owy>快速导航</h3> <nav class="nav-links" data-astro-cid-os4i7owy> <a href="#stats" class="nav-link" data-astro-cid-os4i7owy>📊 统计概览</a> <a href="#cloud" class="nav-link" data-astro-cid-os4i7owy>☁️ 标签云</a> <a href="#categories" class="nav-link" data-astro-cid-os4i7owy>📂 分类浏览</a> <a href="/search" class="nav-link" data-astro-cid-os4i7owy>🔍 搜索内容</a> </nav> <div class="nav-tips" data-astro-cid-os4i7owy> <h4 class="tips-title" data-astro-cid-os4i7owy>💡 使用提示</h4> <ul class="tips-list" data-astro-cid-os4i7owy> <li data-astro-cid-os4i7owy>点击标签查看相关内容</li> <li data-astro-cid-os4i7owy>标签大小反映使用频率</li> <li data-astro-cid-os4i7owy>可按分类浏览标签</li> <li data-astro-cid-os4i7owy>支持标签组合筛选</li> </ul> </div> </aside> </div> </div> </main> ` })}  ${renderScript($$result, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/tags/index.astro?astro&type=script&index=0&lang.ts")}`;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/tags/index.astro", void 0);
const $$file = "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/tags/index.astro";
const $$url = "/tags";
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({ __proto__: null, default: $$Index, file: $$file, url: $$url }, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  i as renderers
};
