/**
 * 精简的设计令牌 CSS 自定义属性
 * 只保留实际使用的颜色和变量
 */

:root {
  /* 核心颜色令牌 - 亮色主题 */
  --color-primary-100: 219 234 254;
  --color-primary-500: 59 130 246;
  --color-primary-600: 37 99 235;
  --color-primary-700: 29 78 216;
  --color-primary-800: 30 64 175;

  --color-secondary-100: 254 243 199;
  --color-secondary-500: 245 158 11;
  --color-secondary-600: 217 119 6;
  --color-secondary-700: 180 83 9;

  /* 中性色 */
  --color-neutral-50: 249 250 251;
  --color-neutral-100: 243 244 246;
  --color-neutral-200: 229 231 235;
  --color-neutral-300: 209 213 219;
  --color-neutral-400: 156 163 175;
  --color-neutral-500: 107 114 128;
  --color-neutral-600: 75 85 99;
  --color-neutral-700: 55 65 81;
  --color-neutral-800: 31 41 55;
  --color-neutral-900: 17 24 39;

  /* 语义颜色 */
  --color-success: 16 185 129;
  --color-warning: 245 158 11;
  --color-error: 239 68 68;
  --color-info: 59 130 246;

  /* 背景颜色 - 亮色主题 */
  --color-background-primary: 255 255 255;
  --color-background-secondary: 249 250 251;
  --color-background-tertiary: 243 244 246;
  --color-background-elevated: 255 255 255;

  /* 前景颜色 - 亮色主题 */
  --color-foreground-primary: 17 24 39;
  --color-foreground-secondary: 55 65 81;
  --color-foreground-tertiary: 107 114 128;
  --color-foreground-inverse: 255 255 255;

  /* 品牌颜色 */
  --color-brand-primary: 59 130 246;
  --color-brand-secondary: 245 158 11;

  /* 边框颜色 */
  --color-border-primary: 229 231 235;
  --color-border-secondary: 209 213 219;
  --color-border-focus: 59 130 246;

  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

  /* 字体大小 */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  /* 间距 */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* 圆角 */
  --radius-sm: 0.125rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;

  /* 过渡 */
  --transition-fast: 150ms ease;
  --transition-normal: 300ms ease;
  --transition-slow: 500ms ease;
}

/* 暗色主题 */
[data-theme='dark'] {
  /* 背景颜色 - 暗色主题 */
  --color-background-primary: 17 24 39;
  --color-background-secondary: 31 41 55;
  --color-background-tertiary: 55 65 81;
  --color-background-elevated: 31 41 55;

  /* 前景颜色 - 暗色主题 */
  --color-foreground-primary: 255 255 255;
  --color-foreground-secondary: 209 213 219;
  --color-foreground-tertiary: 156 163 175;
  --color-foreground-inverse: 17 24 39;

  /* 边框颜色 - 暗色主题 */
  --color-border-primary: 55 65 81;
  --color-border-secondary: 75 85 99;
}

/* 高对比度主题 */
[data-theme='high-contrast'] {
  --color-background-primary: 255 255 255;
  --color-background-secondary: 255 255 255;
  --color-foreground-primary: 0 0 0;
  --color-foreground-secondary: 0 0 0;
  --color-border-primary: 0 0 0;
  --color-brand-primary: 0 0 0;
}

/* 减少动画主题 */
@media (prefers-reduced-motion: reduce) {
  :root {
    --transition-fast: 0ms;
    --transition-normal: 0ms;
    --transition-slow: 0ms;
  }
}

/* 高对比度媒体查询 */
@media (prefers-contrast: high) {
  :root {
    --color-border-primary: 0 0 0;
    --color-foreground-primary: 0 0 0;
  }
}
