---
import { getCollection } from 'astro:content';
import Layout from '../../layouts/Layout.astro';
import { formatDate } from '../../utils/dateUtils';

// 获取所有产品
const products = await getCollection('products');

// 按特色程度和更新时间排序
const sortedProducts = products.sort((a, b) => {
  // 首先按featured排序
  if (a.data.featured && !b.data.featured) return -1;
  if (!a.data.featured && b.data.featured) return 1;
  
  // 然后按更新时间排序
  const dateA = a.data.updateDate || a.data.publishDate;
  const dateB = b.data.updateDate || b.data.publishDate;
  return dateB.getTime() - dateA.getTime();
});

// 统计信息
const stats = {
  total: products.length,
  featured: products.filter(p => p.data.featured).length,
  withDemo: products.filter(p => p.data.demo).length,
};

// 获取所有标签
const allTags = [...new Set(products.flatMap(p => p.data.tags))];
---

<Layout title="产品发布中心 - Pennfly Private Academy" description="展示研究院开发的各类产品和工具，为用户提供实用的解决方案">
  <div class="max-w-7xl mx-auto">
    <!-- 页面标题 -->
    <header class="mb-12">
      <div class="text-center">
        <h1 class="text-4xl font-bold text-gray-900 mb-4">
          🚀 产品发布中心
        </h1>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
          这里汇集了研究院开发的各类产品和工具，每一个产品都经过精心设计和打磨，
          旨在为用户提供实用、高效的解决方案。
        </p>
      </div>
    </header>

    <!-- 统计概览 -->
    <section class="mb-12">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white rounded-lg border border-gray-200 p-8 text-center">
          <div class="text-4xl font-bold text-blue-600 mb-3">{stats.total}</div>
          <div class="text-gray-600">发布产品</div>
        </div>
        <div class="bg-white rounded-lg border border-gray-200 p-8 text-center">
          <div class="text-4xl font-bold text-yellow-600 mb-3">{stats.featured}</div>
          <div class="text-gray-600">精选产品</div>
        </div>
        <div class="bg-white rounded-lg border border-gray-200 p-8 text-center">
          <div class="text-4xl font-bold text-green-600 mb-3">{stats.withDemo}</div>
          <div class="text-gray-600">可在线体验</div>
        </div>
      </div>
    </section>

    <!-- 搜索和筛选 -->
    <section class="mb-8">
      <div class="bg-white rounded-lg border border-gray-200 p-6">
        <div class="flex flex-col md:flex-row gap-4 items-center justify-between">
          <!-- 搜索框 -->
          <div class="flex-1 max-w-md">
            <input
              type="text"
              id="product-search"
              placeholder="搜索产品..."
              class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <!-- 标签筛选 -->
          <div class="flex flex-wrap gap-2">
            <button class="filter-btn active px-3 py-1 text-sm rounded-full border border-gray-300 hover:bg-gray-50" data-tag="all">
              全部
            </button>
            {allTags.slice(0, 6).map((tag) => (
              <button 
                class="filter-btn px-3 py-1 text-sm rounded-full border border-gray-300 hover:bg-gray-50" 
                data-tag={tag}
              >
                {tag}
              </button>
            ))}
          </div>
        </div>
      </div>
    </section>

    <!-- 产品列表 -->
    <section class="mb-12">
      <div class="grid gap-8">
        {sortedProducts.map((product) => {
          const updateDate = product.data.updateDate || product.data.publishDate;
          
          return (
            <article 
              class="product-card bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200"
              data-tags={product.data.tags.join(',')}
            >
              <div class="p-8">
                <div class="flex flex-col lg:flex-row gap-6">
                  <!-- 产品信息 -->
                  <div class="flex-1">
                    <div class="flex items-start justify-between mb-4">
                      <div class="flex-1">
                        <div class="flex items-center gap-3 mb-3">
                          <h2 class="text-2xl font-bold text-gray-900">
                            <a href={`/products/${product.slug}`} class="hover:text-blue-600 transition-colors">
                              {product.data.title.zh}
                            </a>
                          </h2>
                          {product.data.featured && (
                            <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full font-medium">
                              ⭐ 精选
                            </span>
                          )}
                        </div>
                        
                        <div class="text-sm text-gray-600 mb-4">
                          <span>发布于 {formatDate(product.data.publishDate)}</span>
                          {product.data.updateDate && (
                            <span class="ml-4">更新于 {formatDate(updateDate)}</span>
                          )}
                        </div>
                      </div>
                    </div>

                    <p class="text-gray-700 mb-6 leading-relaxed text-lg">
                      {product.data.description.zh}
                    </p>

                    <!-- 标签 -->
                    {product.data.tags.length > 0 && (
                      <div class="mb-4">
                        <div class="flex flex-wrap gap-2">
                          {product.data.tags.map((tag) => (
                            <span class="bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm font-medium">
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  <!-- 操作按钮 -->
                  <div class="flex flex-col gap-3 lg:w-48">
                    <a
                      href={`/products/${product.slug}`}
                      class="inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                    >
                      📖 了解详情
                    </a>
                    
                    {product.data.demo && (
                      <a
                        href={product.data.demo}
                        target="_blank"
                        rel="noopener noreferrer"
                        class="inline-flex items-center justify-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
                      >
                        🚀 立即体验
                      </a>
                    )}
                  </div>
                </div>
              </div>
            </article>
          );
        })}
      </div>
    </section>

    <!-- 标签云 -->
    <section class="mb-12">
      <div class="bg-white rounded-lg border border-gray-200 p-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center">
          🏷️ 产品标签
        </h2>
        <div class="flex flex-wrap justify-center gap-3">
          {allTags.map((tag) => {
            const count = products.filter(p => p.data.tags.includes(tag)).length;
            const size = count > 2 ? 'text-lg' : count > 1 ? 'text-base' : 'text-sm';
            const weight = count > 2 ? 'font-bold' : count > 1 ? 'font-semibold' : 'font-normal';
            
            return (
              <span class={`bg-gradient-to-r from-blue-50 to-purple-50 text-gray-700 px-4 py-2 rounded-lg ${size} ${weight} hover:from-blue-100 hover:to-purple-100 transition-colors cursor-pointer`}>
                {tag}
                <span class="text-xs text-gray-500 ml-1">({count})</span>
              </span>
            );
          })}
        </div>
      </div>
    </section>

    <!-- 产品理念 -->
    <section class="mb-12">
      <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center">
          💡 产品理念
        </h2>
        <div class="grid md:grid-cols-3 gap-6">
          <div class="text-center">
            <div class="text-4xl mb-3">🎯</div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">用户至上</h3>
            <p class="text-gray-600 text-sm">
              深入理解用户需求，打造真正有价值的产品体验
            </p>
          </div>
          <div class="text-center">
            <div class="text-4xl mb-3">⚡</div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">简洁高效</h3>
            <p class="text-gray-600 text-sm">
              追求简洁的设计和高效的功能，让复杂的事情变简单
            </p>
          </div>
          <div class="text-center">
            <div class="text-4xl mb-3">🌟</div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">持续创新</h3>
            <p class="text-gray-600 text-sm">
              保持对新技术的敏感度，不断探索和创新
            </p>
          </div>
        </div>
      </div>
    </section>
  </div>
</Layout>

<script>
  // 搜索功能
  const searchInput = document.getElementById('product-search') as HTMLInputElement;
  const productCards = document.querySelectorAll('.product-card') as NodeListOf<HTMLElement>;
  const filterBtns = document.querySelectorAll('.filter-btn') as NodeListOf<HTMLButtonElement>;

  // 搜索过滤
  searchInput?.addEventListener('input', (e) => {
    const query = (e.target as HTMLInputElement).value.toLowerCase();
    
    productCards.forEach(card => {
      const title = card.querySelector('h2')?.textContent?.toLowerCase() || '';
      const description = card.querySelector('p')?.textContent?.toLowerCase() || '';
      const tags = card.dataset.tags?.toLowerCase() || '';
      
      const matches = title.includes(query) || description.includes(query) || tags.includes(query);
      card.style.display = matches ? 'block' : 'none';
    });
  });

  // 标签筛选
  filterBtns.forEach(btn => {
    btn.addEventListener('click', () => {
      // 更新按钮状态
      filterBtns.forEach(b => b.classList.remove('active', 'bg-blue-100', 'text-blue-700', 'border-blue-300'));
      btn.classList.add('active', 'bg-blue-100', 'text-blue-700', 'border-blue-300');
      
      const tag = btn.dataset.tag;
      
      productCards.forEach(card => {
        const cardTags = card.dataset.tags || '';
        if (tag === 'all' || cardTags.includes(tag || '')) {
          card.style.display = 'block';
        } else {
          card.style.display = 'none';
        }
      });
    });
  });

  // 初始化第一个按钮为激活状态
  const firstBtn = filterBtns[0];
  if (firstBtn) {
    firstBtn.classList.add('active', 'bg-blue-100', 'text-blue-700', 'border-blue-300');
  }
</script>

<style>
  .filter-btn.active {
    background-color: #dbeafe;
    color: #1d4ed8;
    border-color: #93c5fd;
  }
  
  .product-card {
    transition: all 0.2s ease;
  }
  
  .product-card:hover {
    transform: translateY(-2px);
  }
</style>