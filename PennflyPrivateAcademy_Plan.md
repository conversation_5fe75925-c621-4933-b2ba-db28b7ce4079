# Pennfly Private Academy 网站规划文档

以下是一个完整的文档，为你的网站“Pennfly Private Academy”（PPA，域名：pennfly.com）提供详细的规划，涵盖品牌策略、内容结构、技术实现和推广方案，特别强调中英文双语配置。文档根据你的目标（发布个人研究、思考成果和数字资源收藏）以及你已有的技术背景（熟悉 VPS、Docker、Caddy 和 Astro）设计，确保全面且实用。

## 1. 概述

**网站名称**：

- 英文：Pennfly Private Academy (PPA)
- 中文：Pennfly 的私人研究院
- 域名：pennfly.com（已注册）

**网站目标**：  
打造一个中英文双语平台，用于：

- 发布个人研究成果（学术论文、报告）。
- 分享个人思考（随笔、评论）。
- 整理和展示数字资源（文献、数据集、工具、书单）。
- 吸引中英文受众，构建个人化知识分享空间。

**核心理念**：  
Pennfly Private Academy 是一个个人化的知识空间，灵感源自你的中文名字音译“Pennfly”，旨在分享研究、思想和精选资源，促进学术与创意的交流。

**目标受众**：

- 中文用户：对研究、思考和资源感兴趣的学术或知识爱好者。
- 英文用户：全球范围内的研究人员、学生或对你的主题感兴趣的读者。

**语言**：中英文双语，中文为主（你的写作语言），英文为辅助（摘要或概要）。

---

## 2. 品牌策略

### 2.1 名称与 Tagline

- **英文名称**：Pennfly Private Academy (PPA)
  - 简洁、国际化，体现学术与个人特色。
  - 缩写“PPA”易记，适合品牌传播。
- **中文名称**：Pennfly 的私人研究院
  - 与英文对应，保留中文名字音译的亲切感。
- **Tagline**：
  - 英文：PPA: Exploring Research, Ideas, and Digital Collections
  - 中文：PPA：研究、思想与数字资源的分享平台
  - Tagline 在首页、Logo 旁或社交媒体简介中使用，清晰传达网站定位。

### 2.2 Logo 与视觉设计

- **Logo 设计**：
  - 核心元素：缩写“PPA”，搭配简洁图形（如书、羽毛或灯泡，象征知识与灵感）。
  - 中文元素：融入你的中文名字首字母或书法风格，突出“Pennfly”的个人化背景。
  - 配色：深蓝（学术感）、白色（简洁）、金色（高端感）。
- **字体**：
  - 英文：Roboto 或 Poppins（无衬线，现代简洁）。
  - 中文：思源黑体或阿里巴巴普惠体（清晰、优雅）。
- **风格**：专业与亲切平衡，适合学术和创意内容，兼顾中英文视觉统一。

### 2.3 域名

- **主域名**：pennfly.com（已注册）。
- **子域名**（可选）：
  - zh.pennfly.com（中文页面）。
  - en.pennfly.com（英文页面）。
- **备用域名**（建议注册）：
  - ppa.academy
  - pennfly.academy
  - Pennfly 的私人研究院.cn（吸引中文用户）。

---

## 3. 网站内容结构（双语配置）

网站分为五个核心板块，支持中英文双语，确保内容对两种语言用户友好。

### 3.1 首页（Home / 首页）

- **目的**：展示网站定位，吸引用户浏览核心内容。
- **内容**：
  - **英文**：Welcome to Pennfly Private Academy – A hub for my research, reflections, and curated digital collections.
  - **中文**：欢迎体验 Pennfly 的私人研究院 – 分享研究、思想与精选数字资源的知识空间。
  - 包含：
    - 最新研究或文章摘要（中英文）。
    - 热门数字资源推荐。
    - 简短介绍（你的背景与网站使命）。
    - 语言切换按钮（中/英）。

### 3.2 研究成果（Research / 研究）

- **目的**：发布学术论文、研究报告或项目总结。
- **双语配置**：
  - 中文：完整文章（你的主要写作语言）。
  - 英文：摘要（Abstract，200-300 字）或简要翻译。
  - 示例：
    - 中文标题：人工智能在医疗领域的伦理挑战
    - 英文标题：Ethical Challenges of AI in Healthcare
    - 内容：中文全文（PDF 或网页）+ 英文摘要。
- **组织方式**：按主题（科技、哲学等）或时间线分类，支持搜索和标签过滤。

### 3.3 个人思考（Reflections / 思想）

- **目的**：分享随笔、评论或深度思考，吸引学术与非学术读者。
- **双语配置**：
  - 中文：完整文章。
  - 英文：概要（100-200 字）或关键段落翻译。
  - 示例：
    - 中文标题：对未来教育的思考：技术与人文
    - 英文标题：Reflections on Future Education: Technology and Humanity
    - 内容：中文全文 + 英文概要。
- **风格**：轻松、引人入胜，鼓励读者互动。

### 3.4 数字资源（Collections / 资源）

- **目的**：整理文献、数据集、工具链接或书单。
- **双语配置**：
  - 资源标题和简介提供中英文版本。
  - 示例：
    - 中文：推荐书单 – 数据科学入门
    - 英文：Recommended Reading – Introduction to Data Science
    - 内容：资源描述 + 下载链接或跳转。
- **组织方式**：按类别（技术、哲学、艺术等）分类，提供搜索功能。

### 3.5 关于我（About / 关于）

- **目的**：介绍你的背景、“Pennfly”的故事及网站使命。
- **双语配置**：
  - **英文**：Pennfly Private Academy is my personal platform to share research, ideas, and curated resources with the world. Inspired by my Chinese name, it’s a space for knowledge and inspiration.
  - **中文**：Pennfly 的私人研究院是我分享研究、思想和资源的个人平台，灵感源自我的中文名字，致力于打造知识与灵感的家园。
- **内容**：
  - 个人背景（学术经历、兴趣）。
  - “Pennfly”名字的由来（中文音译）。
  - 网站目标与愿景。

### 3.6 互动区域（Community / 社区）（可选）

- **目的**：鼓励用户讨论你的研究或分享想法。
- **双语配置**：
  - 支持中英文评论（通过 Disqus 或自定义评论系统）。
  - 可添加小型论坛，分类讨论研究、思考或资源。

---

## 4. 技术实现（双语支持）

基于你的技术背景（熟悉 VPS、Docker、Caddy、Astro），以下是建站方案，重点支持双语配置。

### 4.1 建站平台

- **推荐**：Astro（你熟悉，适合静态网站）。
  - 优点：轻量、快速，支持国际化（i18n），易于部署。
  - 双语支持：使用 Astro 的 i18n 功能实现中英文页面切换。
- **备选**：
  - WordPress（配合 WPML 或 Polylang 插件，适合动态内容）。
  - Notion（快速展示内容，适合初期测试）。

### 4.2 双语功能实现

- **URL 结构**：

  - 中文页面：pennfly.com/zh/research
  - 英文页面：pennfly.com/en/research

- **语言切换**：

  - 在导航栏添加中/英切换按钮。
  - 使用 Astro i18n 或 JavaScript 动态加载语言内容。

- **内容管理**：

  - 中文：存储完整文章（Markdown 或 CMS）。
  - 英文：使用 DeepL 生成摘要初稿，人工润色。

- **SEO 优化**：

  - 使用 hreflang 标签：

    ```html
    <link
      rel="alternate"
      hreflang="zh"
      href="https://pennfly.com/zh/research"
    />
    <link
      rel="alternate"
      hreflang="en"
      href="https://pennfly.com/en/research"
    />
    ```

  - 关键词：

    - 中文：Pennfly、私人研究院、研究、数字资源
    - 英文：Pennfly, Private Academy, research, digital collections

### 4.3 VPS 部署（Docker + Caddy）

- **Docker Compose 配置**（Astro 项目示例）：

  ```yaml
  version: "3.8"
  services:
    astro:
      image: node:20
      working_dir: /app
      volumes:
        - ./:/app
      command: npm run build && npm run preview
      ports:
        - "3000:3000"
    caddy:
      image: caddy:2
      ports:
        - "80:80"
        - "443:443"
      volumes:
        - ./Caddyfile:/etc/caddy/Caddyfile
        - caddy_data:/data
        - caddy_config:/config
  volumes:
    caddy_data:
    caddy_config:
  ```

- **Caddyfile 配置**（支持 HTTPS）：

  ```caddy
  pennfly.com {
      reverse_proxy astro:3000
      encode gzip
      header Content-Security-Policy "default-src 'self'; font-src 'self' https://fonts.googleapis.com;"
  }
  ```

- **部署步骤**：

  1. 在 VPS 上克隆 Astro 项目（pennfly.com 指向 VPS IP）。
  2. 配置 Astro 的 i18n（参考 Astro 文档：https://docs.astro.build/en/guides/internationalization/）。
  3. 运行 `docker-compose up -d` 部署。
  4. 使用 Caddy 自动配置 HTTPS。
  5. 定期备份（网站文件、数据库）。

### 4.4 文件结构（Astro 示例）

```plaintext
/pennfly
├── /src
│   ├── /pages
│   │   ├── /zh
│   │   │   ├── index.astro       # 中文首页
│   │   │   ├── research.astro    # 中文研究页面
│   │   │   ├── reflections.astro # 中文思考页面
│   │   │   ├── collections.astro # 中文资源页面
│   │   │   ├── about.astro       # 中文关于页面
│   │   ├── /en
│   │   │   ├── index.astro       # 英文首页
│   │   │   ├── research.astro    # 英文研究页面
│   │   │   ├── reflections.astro # 英文思考页面
│   │   │   ├── collections.astro # 英文资源页面
│   │   │   ├── about.astro       # 英文关于页面
│   ├── /content
│   │   ├── research              # 研究文章（Markdown）
│   │   ├── reflections           # 思考文章（Markdown）
│   │   ├── collections           # 资源元数据（JSON/Markdown）
├── Caddyfile                     # Caddy 配置文件
├── docker-compose.yml            # Docker 配置
```

---

## 5. 推广策略

### 5.1 X 平台

- **账号**：@Pennfly 或 @PPA
- **内容**：
  - 发布研究摘要、思考片段、资源推荐（中英文）。
  - 示例：
    - 英文：New post on Pennfly Private Academy: Blockchain in Finance. Visit pennfly.com! #Research #Blockchain
    - 中文：在 Pennfly 的私人研究院发布新文章：区块链在金融中的应用。访问 pennfly.com！#研究 #区块链
- **策略**：
  - 关注话题：#AI、#Research、#Knowledge、#科技、#研究。
  - 与学术或兴趣相近的用户互动。

### 5.2 其他渠道

- **中文**：
  - 知乎：回答相关问题，分享研究链接。
  - 微博/小红书：发布短篇思考或资源推荐。
- **英文**：
  - Medium：发布英文文章摘要。
  - LinkedIn：分享研究成果，吸引专业受众。
- **Newsletter**：
  - 使用 Substack 或 Buttondown，推送中英文内容更新（每月 1-2 次）。

### 5.3 社交媒体视觉

- **帖子模板**：设计统一的中英文模板，包含 PPA Logo、pennfly.com 链接。
- **频率**：每周 1-2 条，涵盖研究、思考或资源。

---

## 6. 实施计划

### 6.1 阶段 1：初期搭建（1-2 个月）

- 注册备用域名（ppa.academy）。
- 使用 Astro 搭建网站框架，配置双语页面。
- 准备 5-10 篇内容（3 篇研究、3 篇思考、4 个资源）。
- 部署到 VPS（Docker + Caddy）。
- 测试中英文页面，确保切换流畅。

### 6.2 阶段 2：内容填充（3-6 个月）

- 每月发布 2-3 篇研究或思考文章。
- 更新资源库，添加 10-20 个精选资源。
- 添加评论功能（Disqus 或自定义）。
- 优化 SEO（hreflang、关键词）。

### 6.3 阶段 3：推广与互动（6 个月后）

- 在 X、知乎、Medium 定期发布内容。
- 收集用户反馈，调整内容方向。
- 考虑推出 newsletter 或小型论坛。

---

## 7. 注意事项

- **内容翻译**：
  - 使用 DeepL 生成英文摘要，人工润色确保自然。
  - 保持英文内容简洁（摘要为主，避免全文翻译）。
- **备份**：
  - 每周备份网站文件和数据（参考你之前的 VPS 经验）。
  - 使用 Git 管理 Astro 项目代码。
- **用户体验**：
  - 确保网站加载速度快（使用 CDN，如 Cloudflare）。
  - 测试移动端适配（中英文页面）。

---

## 8. 总结

“Pennfly Private Academy” (PPA) 是一个兼具学术与个人特色的双语知识平台，依托 pennfly.com 域名，通过 Astro、Docker 和 Caddy 技术栈实现高效部署。网站以研究、思考和资源分享为核心，采用中英文双语配置，吸引全球受众。通过 X 平台和其他渠道推广，PPA 将成为你分享知识与灵感的独特空间。

如需进一步技术支持（Astro 配置代码、Caddy 优化）或内容润色（具体文章或 Tagline），可随时提供补充文档！

---

**备注**：如果你对“喷你福利院”有其他用途（比如子品牌或昵称），可以告诉我，我会帮你整合到规划中！😄

以下是一个补充文档，专注于为你分析和比较博客框架（Astro、Hugo、Typecho、WordPress），以帮助你选择最适合“Pennfly Private Academy”（PPA，域名：pennfly.com）双语网站（发布研究、思考成果和数字资源收藏）的框架。文档基于你的技术背景（熟悉 VPS、Docker、Caddy）和需求（中英文双语、轻量、易于维护），提供清晰的优劣对比和推荐。

---

# 博客框架选择分析：Astro vs. Hugo vs. Typecho vs. WordPress

## 1. 需求概述

**网站目标**：

- 打造中英文双语网站（Pennfly Private Academy），用于发布研究成果、个人思考和数字资源。
- 要求：支持双语切换、SEO 优化、轻量快速、易于部署和维护、适合你的技术背景（VPS、Docker、Caddy）。
- 内容类型：静态页面（研究、思考、资源）为主，可能需要动态功能（如评论）。

**技术背景**：

- 你熟悉 VPS 部署、Docker、Caddy，且有 Astro 博客项目经验。
- 需要框架支持 Markdown（便于写作）、国际化（i18n）以及与 Caddy 集成。

## 2. 框架对比

以下从双语支持、性能、易用性、部署、扩展性等方面对比 Astro、Hugo、Typecho 和 WordPress。

### 2.1 Astro

**概述**：静态网站生成器，基于 JavaScript，支持现代前端框架（如 React、Vue），以 Markdown 或组件编写内容。

**优点**：

- **双语支持**：内置 i18n 功能，支持多语言路由（如 /zh/research、/en/research），适合双语网站。
- **性能**：生成静态 HTML，加载速度极快，SEO 友好。
- **易用性**：
  - 支持 Markdown 编写研究和思考文章，便于你以中文为主。
  - 灵活的组件系统，适合自定义双语页面（如中英文切换按钮）。
- **部署**：
  - 与 Docker 和 Caddy 集成简单（你熟悉此流程）。
  - 支持静态部署到 VPS 或 CDN（如 Cloudflare）。
  - 示例 Docker 配置（参考上一文档）。
- **扩展性**：
  - 支持 React/Vue 组件，可添加动态功能（如评论系统）。
  - 生态较新，但快速增长，插件支持 SEO、i18n 等。
- **适合你的背景**：你已有 Astro 经验，学习曲线低，适合快速上手。

**缺点**：

- 动态功能（如评论、用户登录）需额外集成第三方服务（如 Disqus、Netlify CMS）。
- 社区和插件生态不如 WordPress 丰富。
- 初期配置 i18n 可能需要手动设置（如多语言路由）。

**适用场景**：

- 适合以静态内容为主的双语网站，需高性能和简单维护。
- 你的研究和思考文章可用 Markdown 管理，资源页面可通过 JSON 或 Markdown 组织。

### 2.2 Hugo

**概述**：静态网站生成器，基于 Go 语言，以速度快和轻量著称，使用 Markdown 编写内容。

**优点**：

- **双语支持**：支持多语言配置（通过 content/en、content/zh 文件夹），适合双语网站。
- **性能**：生成速度极快（Go 语言优势），静态 HTML 加载迅速，SEO 友好。
- **易用性**：
  - Markdown 编写，适合你的中文写作需求。
  - 主题丰富，快速切换中英文页面。
- **部署**：
  - 静态文件部署简单，与 Docker 和 Caddy 无缝集成。
  - 可直接在 VPS 上运行 Hugo 生成静态文件，Caddy 提供服务。
- **扩展性**：
  - 支持短代码（shortcodes）自定义内容格式。
  - 可集成评论系统（如 Disqus）或搜索功能（如 Algolia）。

**缺点**：

- 配置 i18n 比 Astro 稍复杂，需手动管理多语言内容文件夹。
- 学习曲线略陡（相比 Astro），需熟悉 Hugo 的模板语法。
- 动态功能有限，需依赖第三方服务。

**适用场景**：

- 适合追求极致性能的静态双语网站，内容以文章和资源为主。
- 如果你希望快速生成大量页面（如上百篇研究文章），Hugo 的速度优势明显。

### 2.3 Typecho

**概述**：轻量级 PHP 博客系统，适合小型博客，数据库驱动，支持动态内容。

**优点**：

- **双语支持**：通过插件（如 TeStore）支持多语言切换，但配置较复杂。
- **易用性**：
  - 管理后台简单，适合非技术用户管理内容。
  - 支持 Markdown（需插件），中文写作友好。
- **部署**：
  - 可通过 Docker 部署 PHP 环境，Caddy 反向代理（你熟悉）。
  - 数据库（MySQL）便于动态内容管理。
- **扩展性**：
  - 支持评论、分类、标签，适合互动功能。
  - 插件生态较小，但满足基本需求（如 SEO、评论）。

**缺点**：

- 双语支持需额外插件，配置不如 Astro/Hugo 原生。
- 依赖 PHP 和 MySQL，性能不如静态网站，维护成本略高（数据库备份）。
- 主题和插件生态有限，国际化支持较弱。

**适用场景**：

- 适合需要动态功能（如内置评论系统）的小型博客。
- 如果你更倾向于管理后台操作而非代码，Typecho 是轻量选择。

### 2.4 WordPress

**概述**：功能强大的 CMS，基于 PHP，数据库驱动，适合动态和复杂网站。

**优点**：

- **双语支持**：
  - 插件（如 WPML、Polylang）提供强大的多语言支持，适合专业双语网站。
  - 可轻松实现中英文页面切换和内容管理。
- **易用性**：
  - 图形化管理后台，适合非技术用户编辑研究、思考和资源。
  - 支持 Markdown（需插件，如 WP Editor.MD）。
- **扩展性**：
  - 丰富的主题和插件生态，支持评论、论坛、SEO（Yoast SEO）、资源下载等。
  - 可扩展为复杂平台（如添加用户登录、会员功能）。
- **部署**：
  - 可通过 Docker 部署（PHP + MySQL），Caddy 提供 HTTPS。
  - 社区支持丰富，易找到教程。

**缺点**：

- 性能较差（相比静态网站），需优化（如缓存插件 WP Super Cache）。
- 维护成本高（数据库、插件更新、潜在安全问题）。
- 学习曲线较高（WPML 配置复杂，需付费）。
- 对 VPS 资源要求较高（相比 Astro/Hugo）。

**适用场景**：

- 适合需要动态功能（如评论、用户互动）和复杂内容的网站。
- 如果你计划扩展网站（如添加论坛、课程），WordPress 更灵活。

## 3. 对比表格

| 框架          | 双语支持           | 性能         | 易用性               | 部署（VPS/Docker/Caddy） | 扩展性                 | 适合场景                    |
| ------------- | ------------------ | ------------ | -------------------- | ------------------------ | ---------------------- | --------------------------- |
| **Astro**     | 原生 i18n，简单    | 极快（静态） | Markdown，需代码基础 | 简单，熟悉 Astro         | 动态需第三方，组件灵活 | 静态双语网站，研究/资源为主 |
| **Hugo**      | 原生多语言，稍复杂 | 最快（静态） | Markdown，模板稍复杂 | 简单，静态文件           | 动态需第三方，主题丰富 | 大量静态内容，追求极致性能  |
| **Typecho**   | 插件支持，较弱     | 中等（动态） | 后台简单，中文友好   | 中等，需 PHP/MySQL       | 动态功能好，生态有限   | 小型动态博客，需评论功能    |
| **WordPress** | 插件强大，需配置   | 较慢（动态） | 后台易用，功能全面   | 复杂，需优化             | 极强，插件丰富         | 动态复杂网站，扩展需求高    |

## 4. 推荐选择：Astro

**推荐理由**：

- **双语支持**：Astro 的 i18n 功能原生支持中英文路由（如 /zh/research、/en/research），配置简单，适合你的双语需求。
- **性能**：静态生成，加载速度快，SEO 友好，适合研究和资源页面。
- **易用性**：你已有 Astro 经验，Markdown 写作符合你的中文写作习惯，学习曲线低。
- **部署**：与 Docker 和 Caddy 集成简单，符合你的 VPS 技术栈。
- **灵活性**：可通过 React/Vue 组件添加动态功能（如评论系统），满足未来扩展需求。
- **轻量维护**：静态网站无需数据库，维护成本低，备份简单（参考你之前的 VPS 经验）。

**Astro 配置示例（双语）**：

- **astro.config.mjs**：

  ```javascript
  import { defineConfig } from "astro/config";
  export default defineConfig({
    site: "https://pennfly.com",
    i18n: {
      defaultLocale: "zh",
      locales: ["zh", "en"],
      routing: {
        prefixDefaultLocale: true, // /zh/research, /en/research
      },
    },
  });
  ```

- **页面结构**：

  ```plaintext
  /src/pages
  ├── /zh
  │   ├── index.astro       # 中文首页
  │   ├── research.astro    # 中文研究
  │   ├── reflections.astro # 中文思考
  │   ├── collections.astro # 中文资源
  ├── /en
  │   ├── index.astro       # 英文首页
  │   ├── research.astro    # 英文研究
  │   ├── reflections.astro # 英文思考
  │   ├── collections.astro # 英文资源
  ```

- **语言切换组件**（示例）：

  ```astro
  <nav>
    <a href="/zh">中文</a> | <a href="/en">English</a>
  </nav>
  ```

**部署**（参考上一文档的 Docker Compose 和 Caddy 配置）。

## 5. 其他框架适用性

- **Hugo**：
  - **选择场景**：如果你的研究文章数量庞大（数百篇），且追求极致生成速度，Hugo 是 Astro 的强替代。
  - **注意**：学习 Hugo 模板需额外时间，双语配置稍复杂。
- **Typecho**：
  - **选择场景**：如果你更喜欢管理后台操作，且需要内置评论系统，Typecho 是轻量动态选择。
  - **注意**：双语支持较弱，需插件，性能不如静态框架。
- **WordPress**：
  - **选择场景**：如果未来计划扩展为复杂平台（如论坛、课程、会员系统），WordPress 更适合。
  - **注意**：维护成本高，性能需优化，不推荐初期使用。

## 6. 实施建议

1. **初期选择 Astro**：
   - 利用现有经验，快速搭建双语网站。
   - 实现首页、研究、思考、资源、关于五个板块（参考上一文档）。
   - 配置 i18n 和语言切换，测试 pennfly.com/zh 和 pennfly.com/en。
2. **内容准备**：
   - 准备 5-10 篇内容（3 篇研究、3 篇思考、4 个资源）。
   - 中文写全文，英文提供摘要（DeepL 辅助+人工润色）。
3. **动态功能**：
   - 初期用 Disqus 或 Commento 实现评论功能。
   - 未来可考虑 Hugo（性能）或 WordPress（扩展）迁移。
4. **SEO 与推广**：
   - 配置 hreflang 标签（参考上一文档）。
   - 在 X 发布中英文帖子，吸引双语用户（@Pennfly 或 @PPA）。

## 7. 结论

**Astro 是最佳选择**，因为它完美匹配你的需求（双语、静态、轻量、Markdown 写作）和技术背景（Astro、Docker、Caddy）。它提供高性能、简单部署和灵活的双语支持，适合快速上线“Pennfly Private Academy”。如果未来需要更多动态功能，可考虑 Typecho 或 WordPress 作为补充。

**下一步**：

- 开始 Astro 项目配置，参考以上代码。
- 准备初始内容（中英文）。
- 部署到 pennfly.com（Caddy + Docker）。
- 如需具体 Astro 配置代码、主题推荐或内容模板，请告知！

---

**备注**：文档避免了过多讨论，聚焦于提供完整方案。如需进一步调整（例如特定插件、主题或代码示例），可随时补充！

```
/pennfly
├── /src
│   ├── /pages
│   │   ├── /zh
│   │   │   ├── index.astro       # 中文首页
│   │   │   ├── research.astro    # 中文研究页面
│   │   │   ├── reflections.astro # 中文思考页面
│   │   │   ├── collections.astro # 中文资源页面
│   │   │   ├── about.astro       # 中文关于页面
│   │   ├── /en
│   │   │   ├── index.astro       # 英文首页
│   │   │   ├── research.astro    # 英文研究页面
│   │   │   ├── reflections.astro # 英文思考页面
│   │   │   ├── collections.astro # 英文资源页面
│   │   │   ├── about.astro       # 英文关于页面
│   ├── /content
│   │   ├── research              # 研究文章（Markdown）
│   │   ├── reflections           # 思考文章（Markdown）
│   │   ├── collections           # 资源元数据（JSON/Markdown）
│   ├── /components
│   │   ├── LanguageSwitch.astro  # 语言切换组件
│   ├── /public
│   │   ├── favicon.ico           # 网站图标
│   │   ├── logo.png              # PPA Logo
├── astro.config.mjs              # Astro 配置
├── Caddyfile                     # Caddy 配置
├── docker-compose.yml            # Docker 配置
```
