import { a as createComponent, m as maybeRenderHead, r as renderScript, d as renderTemplate, c as createAstro, h as renderComponent, b as addAttribute, k as renderSlot, n as renderHead, u as unescapeHTML } from "./vendor-astro.kctgsZae.js";
import "kleur/colors";
import "clsx";
/* empty css                         */
const $$SearchBox = createComponent(async ($$result, $$props, $$slots) => {
  return renderTemplate`${maybeRenderHead()}<div class="search-container" role="search"> <label for="search-input" class="sr-only">搜索文章</label> <div class="relative"> <input type="search" id="search-input" placeholder="搜索文章..." class="w-full rounded-lg border border-gray-300 px-4 py-2 pr-4 pl-10 focus:border-transparent focus:ring-2 focus:ring-blue-500" aria-label="搜索文章" aria-describedby="search-help" aria-expanded="false" aria-autocomplete="list" aria-controls="search-results" autocomplete="off"> <svg class="absolute top-2.5 left-3 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path> </svg> </div> <div id="search-help" class="sr-only">
输入至少2个字符开始搜索。使用上下箭头键导航结果，按回车键选择。
</div> <div id="search-results" class="mt-4 hidden" role="listbox" aria-label="搜索结果"> <div class="max-h-96 overflow-y-auto rounded-lg border border-gray-200 bg-white shadow-lg"> <!-- 搜索结果将在这里显示 --> </div> </div> <!-- 搜索状态提示 --> <div id="search-status" class="sr-only" aria-live="polite" aria-atomic="true"></div> </div> ${renderScript($$result, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/components/SearchBox.astro?astro&type=script&index=0&lang.ts")}`;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/components/SearchBox.astro", void 0);
const $$Astro$1 = createAstro("https://pennfly.com");
const $$SimpleNavigation = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$1, $$props, $$slots);
  Astro2.self = $$SimpleNavigation;
  const navigationItems = [
    { label: "首页", href: "/", icon: "🏠" },
    { label: "动态资讯", href: "/news", icon: "📰" },
    { label: "研究日志", href: "/logs", icon: "📔" },
    {
      label: "研究所",
      icon: "🏛️",
      children: [
        { label: "经济研究所", href: "/economics", icon: "💰" },
        { label: "哲学研究所", href: "/philosophy", icon: "🤔" },
        { label: "互联网研究所", href: "/internet", icon: "🌐" },
        { label: "AI研究所", href: "/ai", icon: "🤖" },
        { label: "未来研究所", href: "/future", icon: "🔮" }
      ]
    },
    { label: "产品发布", href: "/products", icon: "🚀" },
    { label: "关于", href: "/about", icon: "👤" }
  ];
  const currentPath = Astro2.url.pathname;
  function isActive(href) {
    if (href === "/") return currentPath === "/";
    return currentPath.startsWith(href);
  }
  return renderTemplate`${maybeRenderHead()}<header class="sticky top-0 z-50 border-b border-slate-700 shadow-lg" style="background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 50%, #2563eb 100%);"> <div class="container mx-auto px-6"> <div class="flex items-center py-3"> <div class="flex flex-1 items-center"> <!-- Logo --> <a href="/" class="flex flex-shrink-0 items-center space-x-2 transition-all duration-200 hover:opacity-90"> <img src="/ppa-logo.PNG?v=1" alt="Pennfly Private Academy" class="h-5 w-auto" width="20" height="20" loading="eager"> <div class="hidden lg:block"> <div class="text-base leading-tight font-bold text-white">Pennfly Private Academy</div> <div class="-mt-1 text-xs text-blue-100">私人研究院</div> </div> <!-- 移动端和中等屏幕简化标题 --> <div class="block lg:hidden"> <div class="text-sm font-bold text-white">PPA</div> </div> </a> <!-- 桌面端导航 --> <nav class="hidden items-center space-x-1 lg:flex"> ${navigationItems.map((item) => renderTemplate`<div class="group relative"> ${item.children ? renderTemplate`<div> <button class="flex items-center space-x-2 rounded-lg border border-white/20 bg-white/10 px-4 py-2 font-medium text-white transition-all duration-200 hover:bg-white/20"> <span class="text-base">${item.icon}</span> <span class="text-sm">${item.label}</span> <svg class="h-4 w-4 transition-transform duration-200 group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path> </svg> </button> <div class="invisible absolute top-full left-0 mt-2 w-56 rounded-xl border border-gray-200 bg-white/95 opacity-0 shadow-xl backdrop-blur-sm transition-all duration-300 group-hover:visible group-hover:opacity-100"> <div class="p-3"> ${item.children.map((child) => renderTemplate`<a${addAttribute(child.href, "href")}${addAttribute(`flex items-center space-x-3 rounded-lg p-3 transition-all duration-200 ${isActive(child.href) ? "bg-blue-50 text-blue-600 shadow-sm" : "text-slate-700 hover:bg-blue-50/50 hover:text-blue-600"}`, "class")}> <span class="text-lg">${child.icon}</span> <span class="font-medium">${child.label}</span> </a>`)} </div> </div> </div>` : renderTemplate`<a${addAttribute(item.href, "href")}${addAttribute(`flex items-center space-x-2 rounded-lg border px-4 py-2 font-medium transition-all duration-200 ${isActive(item.href) ? "border-white/30 bg-white/20 text-white" : "border-white/20 bg-white/10 text-white hover:border-white/30 hover:bg-white/20"}`, "class")}> <span class="text-base">${item.icon}</span> <span class="text-sm">${item.label}</span> </a>`} </div>`)} </nav> <!-- 右侧工具栏 --> <div class="flex items-center space-x-3"> <div class="hidden md:block"> ${renderComponent($$result, "SearchBox", $$SearchBox, {})} </div> <!-- 移动端菜单按钮 --> <button class="rounded-lg border border-white/20 bg-white/10 p-2 text-white transition-all duration-200 hover:bg-white/20 lg:hidden" id="mobile-menu-button"> <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path> </svg> </button> </div> </div> <!-- 移动端搜索 --> <div id="mobile-search" class="hidden pb-4 md:hidden"> ${renderComponent($$result, "SearchBox", $$SearchBox, {})} </div> </div> <!-- 移动端菜单 --> <div id="mobile-menu" class="hidden border-t border-gray-200 bg-white lg:hidden"> <div class="container mx-auto px-4 py-4"> <nav class="space-y-2"> ${navigationItems.map((item) => renderTemplate`<div> ${item.children ? renderTemplate`<div> <button class="mobile-dropdown-btn flex w-full items-center justify-between rounded-lg p-3 text-slate-700 transition-colors hover:bg-slate-50"> <div class="flex items-center space-x-2"> <span>${item.icon}</span> <span>${item.label}</span> </div> <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path> </svg> </button> <div class="mobile-dropdown-content mt-2 ml-4 hidden space-y-1"> ${item.children.map((child) => renderTemplate`<a${addAttribute(child.href, "href")}${addAttribute(`flex items-center space-x-2 rounded-lg p-2 transition-colors ${isActive(child.href) ? "bg-blue-50 text-blue-600" : "text-slate-600 hover:bg-slate-50"}`, "class")}> <span>${child.icon}</span> <span>${child.label}</span> </a>`)} </div> </div>` : renderTemplate`<a${addAttribute(item.href, "href")}${addAttribute(`flex items-center space-x-2 rounded-lg p-3 transition-colors ${isActive(item.href) ? "bg-blue-50 text-blue-600" : "text-slate-700 hover:bg-slate-50"}`, "class")}> <span>${item.icon}</span> <span>${item.label}</span> </a>`} </div>`)} </nav> </div> </div> </div> ${renderScript($$result, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/components/navigation/SimpleNavigation.astro?astro&type=script&index=0&lang.ts")} </header>`;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/components/navigation/SimpleNavigation.astro", void 0);
var __freeze = Object.freeze;
var __defProp = Object.defineProperty;
var __template = (cooked, raw) => __freeze(__defProp(cooked, "raw", { value: __freeze(cooked.slice()) }));
var _a;
const $$Astro = createAstro("https://pennfly.com");
const $$Layout = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Layout;
  const {
    title = "Pennfly Private Academy",
    description = "个人化私人研究院 · 跨领域学术探索 · 思想与创意的交流平台",
    // showAccessibilityTools 已删除
    canonicalUrl,
    ogImage,
    ogType = "website",
    keywords = [],
    author = "Pennfly",
    publishDate,
    updateDate,
    structuredData,
    noindex = false
  } = Astro2.props;
  const currentUrl = canonicalUrl || new URL(Astro2.url.pathname, Astro2.site).toString();
  const defaultOgImage = new URL("/images/og-default.jpg", Astro2.site).toString();
  const pageOgImage = ogImage || defaultOgImage;
  return renderTemplate`<html lang="zh-CN" data-astro-cid-sckkx6r4> <head><meta charset="UTF-8"><meta name="description"${addAttribute(description, "content")}><meta name="viewport" content="width=device-width, initial-scale=1.0"><link rel="icon" type="image/svg+xml" href="/favicon.svg"><meta name="generator"${addAttribute(Astro2.generator, "content")}><title>${title}</title><!-- SEO 基础标签 -->${keywords.length > 0 && renderTemplate`<meta name="keywords"${addAttribute(keywords.join(", "), "content")}>`}${author && renderTemplate`<meta name="author"${addAttribute(author, "content")}>`}${noindex && renderTemplate`<meta name="robots" content="noindex, nofollow">`}${!noindex && renderTemplate`<meta name="robots" content="index, follow, max-image-preview:large">`}<!-- Canonical URL -->${canonicalUrl && renderTemplate`<link rel="canonical"${addAttribute(canonicalUrl, "href")}>`}<!-- Open Graph 标签 --><meta property="og:title"${addAttribute(title, "content")}><meta property="og:description"${addAttribute(description, "content")}><meta property="og:type"${addAttribute(ogType, "content")}><meta property="og:url"${addAttribute(currentUrl, "content")}><meta property="og:image"${addAttribute(pageOgImage, "content")}><meta property="og:site_name" content="Pennfly Private Academy"><meta property="og:locale" content="zh_CN"><!-- Twitter Card 标签 --><meta name="twitter:card" content="summary_large_image"><meta name="twitter:title"${addAttribute(title, "content")}><meta name="twitter:description"${addAttribute(description, "content")}><meta name="twitter:image"${addAttribute(pageOgImage, "content")}><!-- 发布和更新日期 -->${publishDate && renderTemplate`<meta property="article:published_time"${addAttribute(publishDate.toISOString(), "content")}>`}${updateDate && renderTemplate`<meta property="article:modified_time"${addAttribute(updateDate.toISOString(), "content")}>`}${author && renderTemplate`<meta property="article:author"${addAttribute(author, "content")}>`}<!-- 结构化数据 -->${structuredData && renderTemplate(_a || (_a = __template(['<script type="application/ld+json">', "<\/script>"])), unescapeHTML(JSON.stringify(structuredData)))}<!-- 移动端主题色 --><meta name="theme-color" content="#3b82f6"><meta name="mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="default"><!-- 搜索引擎优化 --><meta name="googlebot" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1"><meta name="bingbot" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1"><!-- 额外的 head 内容 -->${renderSlot($$result, $$slots["head"])}<!-- 性能优化：DNS 预解析 --><link rel="dns-prefetch" href="//fonts.googleapis.com"><link rel="dns-prefetch" href="//cdn.jsdelivr.net"><!-- 性能优化：预加载关键资源 --><link rel="preload" href="/favicon.svg" as="image" type="image/svg+xml"><!-- CSS优化：异步加载非关键CSS --><link rel="preload" href="/src/styles/academic.css" as="style" onload="this.onload=null;this.rel='stylesheet'"><link rel="preload" href="/src/styles/accessibility.css" as="style" onload="this.onload=null;this.rel='stylesheet'"><link rel="preload" href="/src/styles/performance.css" as="style" onload="this.onload=null;this.rel='stylesheet'"><!-- 关键CSS内联 --><style>
      /* 关键CSS - 从critical.css内联 */
      *,*::before,*::after{box-sizing:border-box}
      html{font-family:'PingFang SC','Hiragino Sans GB','Microsoft YaHei',system-ui,sans-serif;scroll-behavior:smooth}
      body{margin:0;line-height:1.6;background-color:rgb(var(--color-background-primary));color:rgb(var(--color-foreground-primary));transition:background-color var(--transition-normal),color var(--transition-normal)}
      .container{width:100%;max-width:1280px;margin:0 auto;padding:0 1rem}
      .navbar{position:sticky;top:0;z-index:50;background-color:rgb(var(--color-background-elevated));border-bottom:1px solid rgb(var(--color-border-primary))}
      .main-content{min-height:calc(100vh - 4rem);padding:2rem 0}
      .loading{display:inline-block;width:1rem;height:1rem;border:2px solid rgb(var(--color-border-primary));border-radius:50%;border-top-color:rgb(var(--color-brand-primary));animation:spin 1s ease-in-out infinite}
      @keyframes spin{to{transform:rotate(360deg)}}
    </style><!-- RSS 订阅 --><link rel="alternate" type="application/rss+xml" title="Pennfly Private Academy RSS Feed" href="/rss.xml"><!-- 内容安全策略 --><meta http-equiv="Content-Security-Policy" content="
      default-src 'self'; 
      style-src 'self' 'unsafe-inline'; 
      font-src 'self' data:; 
      script-src 'self' 'unsafe-inline' 'unsafe-eval'; 
      img-src 'self' data: https:; 
      connect-src 'self'; 
      object-src 'none'; 
      base-uri 'self'; 
      form-action 'self';
    "><!-- 字体样式 -->${renderHead()}</head> <body class="min-h-screen bg-gray-50 text-gray-900" data-astro-cid-sckkx6r4> <!-- 跳转链接（屏幕阅读器和键盘用户） --> <div class="skip-links" data-astro-cid-sckkx6r4> <a href="#main-content" class="skip-link" data-astro-cid-sckkx6r4> 跳转到主内容 </a> <a href="#navigation" class="skip-link" data-astro-cid-sckkx6r4> 跳转到导航 </a> <a href="#footer" class="skip-link" data-astro-cid-sckkx6r4> 跳转到页脚 </a> </div> <!-- 导航栏 --> <div id="navigation" role="banner" data-astro-cid-sckkx6r4> ${renderComponent($$result, "SimpleNavigation", $$SimpleNavigation, { "data-astro-cid-sckkx6r4": true })} </div> <!-- 主要内容 --> <main id="main-content" class="container mx-auto max-w-6xl px-4 py-8" role="main" tabindex="-1" data-astro-cid-sckkx6r4> ${renderSlot($$result, $$slots["default"])} </main> <!-- 页脚 --> <footer id="footer" class="mt-12 border-t border-gray-200 bg-gray-100 py-8" role="contentinfo" data-astro-cid-sckkx6r4> <div class="container mx-auto px-4 text-center text-gray-600" data-astro-cid-sckkx6r4> <p data-astro-cid-sckkx6r4>&copy; ${(/* @__PURE__ */ new Date()).getFullYear()} Pennfly Private Academy. 保留所有权利。</p> </div> </footer> <!-- 可访问性工具已删除 --> <!-- 性能监控已删除 --> <!-- 返回顶部按钮 --> <button id="back-to-top" class="invisible fixed right-6 bottom-6 rounded-full bg-blue-600 p-3 text-white opacity-0 shadow-lg transition-all duration-300 hover:bg-blue-700" aria-label="返回页面顶部" title="返回顶部" data-astro-cid-sckkx6r4> <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true" data-astro-cid-sckkx6r4> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" data-astro-cid-sckkx6r4></path> </svg> </button>  ${renderScript($$result, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/layouts/Layout.astro?astro&type=script&index=0&lang.ts")}  </body></html>`;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/layouts/Layout.astro", void 0);
export {
  $$Layout as $
};
