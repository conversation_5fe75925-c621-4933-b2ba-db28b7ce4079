---
import type { InstituteConfig } from '../../utils/instituteConfig';

export interface Props {
  institute: InstituteConfig;
  relatedArticles: any[];
  title?: string;
}

const { institute, relatedArticles, title = '相关文章' } = Astro.props;
---

{
  relatedArticles.length > 0 && (
    <div class="related-articles">
      <h3 class="mb-6 text-xl font-bold text-gray-800">{title}</h3>
      <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {relatedArticles.map((article: any) => (
          <article class="group rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-all hover:shadow-md">
            <div class="mb-3">
              <h4 class="mb-2 font-semibold text-gray-800 group-hover:text-blue-600">
                <a href={`/${institute.id}/${article.slug}`} class="transition-colors">
                  {article.data.title.zh}
                </a>
              </h4>
              <p class="line-clamp-2 text-sm text-gray-600">{article.data.description.zh}</p>
            </div>

            <div class="mb-3 flex items-center justify-between text-xs text-gray-500">
              <span>{article.data.publishDate.toLocaleDateString('zh-CN')}</span>
              {article.data.readingTime && <span>{article.data.readingTime} 分钟</span>}
              {article.data.featured && (
                <span class="rounded bg-yellow-100 px-2 py-1 text-yellow-800">精选</span>
              )}
            </div>

            {article.data.tags.length > 0 && (
              <div class="flex flex-wrap gap-1">
                {article.data.tags.slice(0, 3).map((tag: string) => (
                  <span class="rounded bg-gray-100 px-2 py-1 text-xs text-gray-600">{tag}</span>
                ))}
                {article.data.tags.length > 3 && (
                  <span class="text-xs text-gray-500">+{article.data.tags.length - 3}</span>
                )}
              </div>
            )}
          </article>
        ))}
      </div>
    </div>
  )
}

<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
