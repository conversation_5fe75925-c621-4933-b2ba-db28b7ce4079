import { d as contentManager } from "../../../assets/utils.CcA_tyNa.js";
import { i } from "../../../assets/vendor-astro.kctgsZae.js";
const prerender = false;
const POST = async ({ request }) => {
  try {
    const body = await request.json();
    const { collection, slug, frontmatter, content } = body;
    if (!collection || !slug || !frontmatter || content === void 0) {
      return new Response(
        JSON.stringify({
          error: "Collection, slug, frontmatter, and content are required",
          code: "MISSING_REQUIRED_FIELDS"
        }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
    const validCollections = [
      "news",
      "logs",
      "research",
      "reflections",
      "economics",
      "philosophy",
      "internet",
      "ai",
      "future",
      "products"
    ];
    if (!validCollections.includes(collection)) {
      return new Response(
        JSON.stringify({
          error: "Invalid collection name",
          code: "INVALID_COLLECTION",
          validCollections
        }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
    const slugRegex = /^[a-zA-Z0-9_-]+$/;
    if (!slugRegex.test(slug)) {
      return new Response(
        JSON.stringify({
          error: "Invalid slug format. Only letters, numbers, hyphens, and underscores are allowed",
          code: "INVALID_SLUG"
        }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
    const existingContent = await contentManager.getContentById(`${collection}/${slug}`);
    if (existingContent) {
      return new Response(
        JSON.stringify({
          error: "Content with this slug already exists in the collection",
          code: "CONTENT_EXISTS"
        }),
        {
          status: 409,
          headers: { "Content-Type": "application/json" }
        }
      );
    }
    const newContent = await contentManager.createContent(collection, slug, frontmatter, content);
    const response = {
      id: newContent.id,
      collection: newContent.collection,
      slug: newContent.slug,
      title: newContent.title,
      description: newContent.description,
      publishDate: newContent.publishDate.toISOString(),
      updateDate: newContent.updateDate?.toISOString(),
      draft: newContent.draft,
      featured: newContent.featured,
      tags: newContent.tags,
      author: newContent.author,
      content: newContent.content
    };
    return new Response(JSON.stringify(response), {
      status: 201,
      headers: { "Content-Type": "application/json" }
    });
  } catch (error) {
    console.error("创建内容 API 错误:", error);
    return new Response(
      JSON.stringify({
        error: "Internal server error",
        code: "INTERNAL_ERROR"
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" }
      }
    );
  }
};
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({ __proto__: null, POST, prerender }, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  i as renderers
};
