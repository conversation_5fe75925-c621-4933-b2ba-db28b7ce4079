/**
 * Theme configurations for Pennfly Private Academy
 * Defines light, dark, and high contrast theme variants
 */

import type { Theme, ThemeConfig } from '../../types/theme';

// Base typography configuration (shared across themes)
const baseTypography = {
  fontFamily: {
    sans: 'Inter, PingFang SC, Hiragino Sans GB, Microsoft YaHei, WenQuanYi Micro Hei, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Helvetica Neue, Helvetica, Arial, sans-serif',
    serif: 'Crimson Text, Source Serif Pro, Noto Serif SC, Georgia, Times New Roman, serif',
    mono: 'JetBrains Mono, Fira Code, Source Code Pro, Consolas, Monaco, Courier New, monospace',
    // 数学字体已移除
  },
  fontSize: {
    xs: ['0.75rem', { lineHeight: '1rem', letterSpacing: '0.025em' }],
    sm: ['0.875rem', { lineHeight: '1.25rem', letterSpacing: '0.025em' }],
    base: ['1rem', { lineHeight: '1.5rem', letterSpacing: '0' }],
    lg: ['1.125rem', { lineHeight: '1.75rem', letterSpacing: '-0.025em' }],
    xl: ['1.25rem', { lineHeight: '1.75rem', letterSpacing: '-0.025em' }],
    '2xl': ['1.5rem', { lineHeight: '2rem', letterSpacing: '-0.025em' }],
    '3xl': ['1.875rem', { lineHeight: '2.25rem', letterSpacing: '-0.05em' }],
    '4xl': ['2.25rem', { lineHeight: '2.5rem', letterSpacing: '-0.05em' }],
  } as Record<string, [string, { lineHeight: string; letterSpacing?: string }]>,
  fontWeight: {
    thin: '100',
    extralight: '200',
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
    black: '900',
  },
};

// Base spacing configuration (shared across themes)
const baseSpacing = {
  scale: {
    0: '0px',
    px: '1px',
    0.5: '0.125rem',
    1: '0.25rem',
    1.5: '0.375rem',
    2: '0.5rem',
    2.5: '0.625rem',
    3: '0.75rem',
    3.5: '0.875rem',
    4: '1rem',
    5: '1.25rem',
    6: '1.5rem',
    7: '1.75rem',
    8: '2rem',
    9: '2.25rem',
    10: '2.5rem',
    11: '2.75rem',
    12: '3rem',
    14: '3.5rem',
    16: '4rem',
    20: '5rem',
    24: '6rem',
    28: '7rem',
    32: '8rem',
  },
  component: {
    padding: {
      xs: '0.5rem',
      sm: '0.75rem',
      md: '1rem',
      lg: '1.5rem',
      xl: '2rem',
    },
    margin: {
      xs: '0.5rem',
      sm: '0.75rem',
      md: '1rem',
      lg: '1.5rem',
      xl: '2rem',
    },
    gap: {
      xs: '0.25rem',
      sm: '0.5rem',
      md: '0.75rem',
      lg: '1rem',
      xl: '1.5rem',
    },
  },
};

// Base border radius configuration (shared across themes)
const baseBorderRadius = {
  none: '0px',
  sm: '0.125rem',
  md: '0.375rem',
  lg: '0.5rem',
  xl: '0.75rem',
  full: '9999px',
};

// Base animations configuration (shared across themes)
const baseAnimations = {
  duration: {
    fast: '150ms',
    normal: '300ms',
    slow: '500ms',
  },
  easing: {
    linear: 'linear',
    ease: 'ease',
    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
    easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
  },
};

// Light theme configuration
export const lightTheme: Theme = {
  name: 'Light',
  mode: 'light',
  variant: 'default',
  colors: {
    background: {
      primary: '#ffffff',
      secondary: '#f9fafb',
      tertiary: '#f3f4f6',
      elevated: '#ffffff',
    },
    foreground: {
      primary: '#111827',
      secondary: '#374151',
      tertiary: '#6b7280',
      inverse: '#ffffff',
    },
    brand: {
      primary: '#2563eb', // blue-600
      secondary: '#f59e0b', // amber-500
      accent: '#3b82f6', // blue-500
    },
    semantic: {
      success: '#10b981', // emerald-500
      warning: '#f59e0b', // amber-500
      error: '#ef4444', // red-500
      info: '#3b82f6', // blue-500
    },
    interactive: {
      default: '#2563eb',
      hover: '#1d4ed8',
      active: '#1e40af',
      disabled: '#9ca3af',
      focus: '#3b82f6',
    },
    border: {
      default: '#e5e7eb',
      subtle: '#f3f4f6',
      strong: '#d1d5db',
      interactive: '#2563eb',
    },
  },
  typography: baseTypography,
  spacing: baseSpacing,
  borderRadius: baseBorderRadius,
  shadows: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
    inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',
    focus: '0 0 0 3px rgb(59 130 246 / 0.5)',
    none: '0 0 #0000',
  },
  animations: baseAnimations,
};

// Dark theme configuration
export const darkTheme: Theme = {
  name: 'Dark',
  mode: 'dark',
  variant: 'default',
  colors: {
    background: {
      primary: '#111827', // gray-900
      secondary: '#1f2937', // gray-800
      tertiary: '#374151', // gray-700
      elevated: '#1f2937',
    },
    foreground: {
      primary: '#f9fafb', // gray-50
      secondary: '#e5e7eb', // gray-200
      tertiary: '#9ca3af', // gray-400
      inverse: '#111827',
    },
    brand: {
      primary: '#3b82f6', // blue-500 (brighter for dark mode)
      secondary: '#fbbf24', // amber-400 (brighter for dark mode)
      accent: '#60a5fa', // blue-400
    },
    semantic: {
      success: '#34d399', // emerald-400
      warning: '#fbbf24', // amber-400
      error: '#f87171', // red-400
      info: '#60a5fa', // blue-400
    },
    interactive: {
      default: '#3b82f6',
      hover: '#60a5fa',
      active: '#2563eb',
      disabled: '#6b7280',
      focus: '#60a5fa',
    },
    border: {
      default: '#374151',
      subtle: '#1f2937',
      strong: '#4b5563',
      interactive: '#3b82f6',
    },
  },
  typography: baseTypography,
  spacing: baseSpacing,
  borderRadius: baseBorderRadius,
  shadows: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.3)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.4)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.4), 0 8px 10px -6px rgb(0 0 0 / 0.4)',
    inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.3)',
    focus: '0 0 0 3px rgb(96 165 250 / 0.5)',
    none: '0 0 #0000',
  },
  animations: baseAnimations,
};

// High contrast theme configuration (based on dark theme with enhanced contrast)
export const highContrastTheme: Theme = {
  name: 'High Contrast',
  mode: 'dark',
  variant: 'high-contrast',
  colors: {
    background: {
      primary: '#000000',
      secondary: '#1a1a1a',
      tertiary: '#333333',
      elevated: '#1a1a1a',
    },
    foreground: {
      primary: '#ffffff',
      secondary: '#f0f0f0',
      tertiary: '#cccccc',
      inverse: '#000000',
    },
    brand: {
      primary: '#66b3ff', // High contrast blue
      secondary: '#ffcc00', // High contrast yellow
      accent: '#80d4ff', // High contrast light blue
    },
    semantic: {
      success: '#00ff88', // High contrast green
      warning: '#ffcc00', // High contrast yellow
      error: '#ff6666', // High contrast red
      info: '#66b3ff', // High contrast blue
    },
    interactive: {
      default: '#66b3ff',
      hover: '#80d4ff',
      active: '#4da6ff',
      disabled: '#666666',
      focus: '#80d4ff',
    },
    border: {
      default: '#666666',
      subtle: '#333333',
      strong: '#999999',
      interactive: '#66b3ff',
    },
  },
  typography: baseTypography,
  spacing: baseSpacing,
  borderRadius: baseBorderRadius,
  shadows: {
    sm: '0 1px 2px 0 rgb(255 255 255 / 0.1)',
    md: '0 4px 6px -1px rgb(255 255 255 / 0.15), 0 2px 4px -2px rgb(255 255 255 / 0.15)',
    lg: '0 10px 15px -3px rgb(255 255 255 / 0.2), 0 4px 6px -4px rgb(255 255 255 / 0.2)',
    xl: '0 20px 25px -5px rgb(255 255 255 / 0.25), 0 8px 10px -6px rgb(255 255 255 / 0.25)',
    inner: 'inset 0 2px 4px 0 rgb(255 255 255 / 0.1)',
    focus: '0 0 0 3px rgb(128 212 255 / 0.8)',
    none: '0 0 #0000',
  },
  animations: baseAnimations,
};

// Theme configuration
export const themeConfig: ThemeConfig = {
  defaultTheme: 'light',
  enableSystemTheme: true,
  storageKey: 'pennfly-theme',
  themes: {
    light: lightTheme,
    dark: darkTheme,
    highContrast: highContrastTheme,
  },
};

// Export individual themes for direct access
export { lightTheme as light, darkTheme as dark, highContrastTheme as highContrast };
