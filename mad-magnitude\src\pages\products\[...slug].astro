---
export const prerender = false;

import type { CollectionEntry } from 'astro:content';
import { getCollection, getEntry } from 'astro:content';
import Layout from '../../layouts/Layout.astro';
import { formatDate } from '../../utils/dateUtils';

// 在服务器端渲染模式下，直接从 URL 参数获取内容
const { slug } = Astro.params;

if (!slug) {
  return Astro.redirect('/products');
}

// 获取产品内容
const product = await getEntry('products', slug);

if (!product) {
  return Astro.redirect('/404');
}

const { Content } = await product.render();

const updateDate = product.data.updateDate || product.data.publishDate;

// 获取相关产品（相同标签）
const allProducts = await getCollection('products');
const relatedProducts = allProducts
  .filter(p => p.slug !== product.slug)
  .filter(p => {
    const commonTags = p.data.tags.some(tag => product.data.tags.includes(tag));
    return commonTags;
  })
  .slice(0, 3);
---

<Layout title={`${product.data.title.zh} - 产品发布中心`} description={product.data.description.zh}>
  <div class="mx-auto max-w-4xl">
    <!-- 返回链接 -->
    <nav class="mb-8">
      <a
        href="/products"
        class="inline-flex items-center text-blue-600 transition-colors hover:text-blue-800"
      >
        ← 返回产品发布中心
      </a>
    </nav>

    <!-- 产品头部 -->
    <header class="mb-12">
      <div class="rounded-lg border border-gray-200 bg-white p-8">
        <div class="flex flex-col gap-6 lg:flex-row">
          <!-- 基本信息 -->
          <div class="flex-1">
            <div class="mb-4 flex items-center gap-3">
              <h1 class="text-3xl font-bold text-gray-900">
                {product.data.title.zh}
              </h1>
              {
                product.data.featured && (
                  <span class="rounded-full bg-yellow-100 px-3 py-1 text-sm font-medium text-yellow-800">
                    ⭐ 精选产品
                  </span>
                )
              }
            </div>

            <div class="mb-6 flex items-center gap-4 text-sm text-gray-600">
              <span>发布于 {formatDate(product.data.publishDate)}</span>
              {product.data.updateDate && <span>更新于 {formatDate(updateDate)}</span>}
              <span>作者：{product.data.author}</span>
            </div>

            <p class="mb-6 text-lg leading-relaxed text-gray-700">
              {product.data.description.zh}
            </p>

            <!-- 标签 -->
            {
              product.data.tags.length > 0 && (
                <div>
                  <h3 class="mb-3 text-sm font-semibold text-gray-900">产品标签</h3>
                  <div class="flex flex-wrap gap-2">
                    {product.data.tags.map(tag => (
                      <span class="rounded-lg bg-blue-50 px-3 py-1 text-sm font-medium text-blue-700">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )
            }
          </div>

          <!-- 操作面板 -->
          <div class="lg:w-64">
            <div class="rounded-lg bg-gray-50 p-6">
              <h3 class="mb-4 text-lg font-semibold text-gray-900">产品链接</h3>
              <div class="space-y-3">
                {
                  product.data.demo && (
                    <a
                      href={product.data.demo}
                      target="_blank"
                      rel="noopener noreferrer"
                      class="inline-flex w-full items-center justify-center rounded-lg bg-green-600 px-4 py-3 font-medium text-white transition-colors hover:bg-green-700"
                    >
                      🚀 立即体验
                    </a>
                  )
                }

                <button
                  onclick="navigator.share ? navigator.share({title: document.title, url: window.location.href}) : navigator.clipboard.writeText(window.location.href).then(() => alert('链接已复制到剪贴板'))"
                  class="inline-flex w-full items-center justify-center rounded-lg border border-gray-300 px-4 py-3 font-medium text-gray-700 transition-colors hover:bg-gray-50"
                >
                  🔗 分享产品
                </button>
              </div>

              <!-- 产品信息 -->
              <div class="mt-6 border-t border-gray-200 pt-6">
                <h4 class="mb-3 text-sm font-semibold text-gray-900">产品信息</h4>
                <div class="space-y-2 text-sm">
                  <div class="flex justify-between">
                    <span class="text-gray-600">发布时间</span>
                    <span class="text-gray-900">{formatDate(product.data.publishDate)}</span>
                  </div>
                  {
                    product.data.updateDate && (
                      <div class="flex justify-between">
                        <span class="text-gray-600">最后更新</span>
                        <span class="text-gray-900">{formatDate(product.data.updateDate)}</span>
                      </div>
                    )
                  }
                  <div class="flex justify-between">
                    <span class="text-gray-600">开发者</span>
                    <span class="text-gray-900">{product.data.author}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- 产品内容 -->
    <main class="mb-12">
      <div class="rounded-lg border border-gray-200 bg-white p-8">
        <div class="prose prose-lg max-w-none">
          <Content />
        </div>
      </div>
    </main>

    <!-- 相关产品 -->
    {
      relatedProducts.length > 0 && (
        <section class="mb-12">
          <div class="rounded-lg border border-gray-200 bg-white p-8">
            <h2 class="mb-6 text-2xl font-bold text-gray-900">相关产品</h2>
            <div class="grid gap-6 md:grid-cols-3">
              {relatedProducts.map(relatedProduct => {
                return (
                  <article class="rounded-lg border border-gray-200 p-6 transition-shadow hover:shadow-md">
                    {relatedProduct.data.featured && (
                      <div class="mb-3">
                        <span class="rounded-full bg-yellow-100 px-2 py-1 text-xs font-medium text-yellow-800">
                          ⭐ 精选
                        </span>
                      </div>
                    )}

                    <h3 class="mb-2 text-lg font-semibold text-gray-900">
                      <a
                        href={`/products/${relatedProduct.slug}`}
                        class="transition-colors hover:text-blue-600"
                      >
                        {relatedProduct.data.title.zh}
                      </a>
                    </h3>

                    <p class="mb-4 line-clamp-3 text-sm text-gray-600">
                      {relatedProduct.data.description.zh}
                    </p>

                    <div class="flex flex-wrap gap-1">
                      {relatedProduct.data.tags.slice(0, 3).map(tag => (
                        <span class="rounded bg-gray-100 px-2 py-1 text-xs text-gray-600">
                          {tag}
                        </span>
                      ))}
                      {relatedProduct.data.tags.length > 3 && (
                        <span class="text-xs text-gray-500">
                          +{relatedProduct.data.tags.length - 3}
                        </span>
                      )}
                    </div>
                  </article>
                );
              })}
            </div>
          </div>
        </section>
      )
    }

    <!-- 返回顶部和导航 -->
    <div class="flex items-center justify-between">
      <a
        href="/products"
        class="inline-flex items-center rounded-lg bg-gray-600 px-6 py-3 text-white transition-colors hover:bg-gray-700"
      >
        ← 返回产品列表
      </a>

      <button
        onclick="window.scrollTo({top: 0, behavior: 'smooth'})"
        class="inline-flex items-center rounded-lg border border-gray-300 px-6 py-3 text-gray-700 transition-colors hover:bg-gray-50"
      >
        ↑ 返回顶部
      </button>
    </div>
  </div>
</Layout>

<style>
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
