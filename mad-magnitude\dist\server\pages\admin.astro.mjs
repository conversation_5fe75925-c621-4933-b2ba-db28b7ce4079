import { a as createComponent, h as renderComponent, d as renderTemplate, m as maybeRenderHead } from "../assets/vendor-astro.kctgsZae.js";
import { i } from "../assets/vendor-astro.kctgsZae.js";
import "kleur/colors";
import { e as getCollection } from "../assets/utils.CcA_tyNa.js";
import { $ as $$Layout } from "../assets/Layout.B3OpmcYc.js";
/* empty css                                */
const $$Index = createComponent(async ($$result, $$props, $$slots) => {
  const newsCount = (await getCollection("news")).length;
  const logsCount = (await getCollection("logs")).length;
  const economicsCount = (await getCollection("economics")).length;
  const philosophyCount = (await getCollection("philosophy")).length;
  const internetCount = (await getCollection("internet")).length;
  const aiCount = (await getCollection("ai")).length;
  const futureCount = (await getCollection("future")).length;
  const productsCount = (await getCollection("products")).length;
  const totalContent = newsCount + logsCount + economicsCount + philosophyCount + internetCount + aiCount + futureCount + productsCount;
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "管理后台 - Pennfly Private Academy", "data-astro-cid-u2h3djql": true }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<main class="min-h-screen bg-gray-50 py-8" data-astro-cid-u2h3djql> <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8" data-astro-cid-u2h3djql> <!-- 页面标题 --> <div class="mb-8" data-astro-cid-u2h3djql> <h1 class="text-3xl font-bold text-gray-900" data-astro-cid-u2h3djql>管理后台</h1> <p class="mt-2 text-gray-600" data-astro-cid-u2h3djql>Pennfly Private Academy 内容管理系统</p> </div> <!-- 统计卡片 --> <div class="mb-8 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4" data-astro-cid-u2h3djql> <div class="rounded-lg bg-white p-6 shadow" data-astro-cid-u2h3djql> <div class="flex items-center" data-astro-cid-u2h3djql> <div class="flex-shrink-0" data-astro-cid-u2h3djql> <div class="flex h-8 w-8 items-center justify-center rounded-full bg-blue-500" data-astro-cid-u2h3djql> <span class="text-sm font-medium text-white" data-astro-cid-u2h3djql>📊</span> </div> </div> <div class="ml-4" data-astro-cid-u2h3djql> <p class="text-sm font-medium text-gray-500" data-astro-cid-u2h3djql>总内容数</p> <p class="text-2xl font-semibold text-gray-900" data-astro-cid-u2h3djql>${totalContent}</p> </div> </div> </div> <div class="rounded-lg bg-white p-6 shadow" data-astro-cid-u2h3djql> <div class="flex items-center" data-astro-cid-u2h3djql> <div class="flex-shrink-0" data-astro-cid-u2h3djql> <div class="flex h-8 w-8 items-center justify-center rounded-full bg-green-500" data-astro-cid-u2h3djql> <span class="text-sm font-medium text-white" data-astro-cid-u2h3djql>📰</span> </div> </div> <div class="ml-4" data-astro-cid-u2h3djql> <p class="text-sm font-medium text-gray-500" data-astro-cid-u2h3djql>动态资讯</p> <p class="text-2xl font-semibold text-gray-900" data-astro-cid-u2h3djql>${newsCount}</p> </div> </div> </div> <div class="rounded-lg bg-white p-6 shadow" data-astro-cid-u2h3djql> <div class="flex items-center" data-astro-cid-u2h3djql> <div class="flex-shrink-0" data-astro-cid-u2h3djql> <div class="flex h-8 w-8 items-center justify-center rounded-full bg-purple-500" data-astro-cid-u2h3djql> <span class="text-sm font-medium text-white" data-astro-cid-u2h3djql>📔</span> </div> </div> <div class="ml-4" data-astro-cid-u2h3djql> <p class="text-sm font-medium text-gray-500" data-astro-cid-u2h3djql>研究日志</p> <p class="text-2xl font-semibold text-gray-900" data-astro-cid-u2h3djql>${logsCount}</p> </div> </div> </div> <div class="rounded-lg bg-white p-6 shadow" data-astro-cid-u2h3djql> <div class="flex items-center" data-astro-cid-u2h3djql> <div class="flex-shrink-0" data-astro-cid-u2h3djql> <div class="flex h-8 w-8 items-center justify-center rounded-full bg-orange-500" data-astro-cid-u2h3djql> <span class="text-sm font-medium text-white" data-astro-cid-u2h3djql>🛠️</span> </div> </div> <div class="ml-4" data-astro-cid-u2h3djql> <p class="text-sm font-medium text-gray-500" data-astro-cid-u2h3djql>开发项目</p> <p class="text-2xl font-semibold text-gray-900" data-astro-cid-u2h3djql>${productsCount}</p> </div> </div> </div> </div> <!-- 研究所统计 --> <div class="mb-8 rounded-lg bg-white shadow" data-astro-cid-u2h3djql> <div class="border-b border-gray-200 px-6 py-4" data-astro-cid-u2h3djql> <h2 class="text-lg font-medium text-gray-900" data-astro-cid-u2h3djql>研究所内容统计</h2> </div> <div class="p-6" data-astro-cid-u2h3djql> <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3" data-astro-cid-u2h3djql> <div class="flex items-center justify-between rounded-lg bg-gray-50 p-4" data-astro-cid-u2h3djql> <div class="flex items-center" data-astro-cid-u2h3djql> <span class="mr-3 text-2xl" data-astro-cid-u2h3djql>💰</span> <span class="font-medium" data-astro-cid-u2h3djql>经济研究所</span> </div> <span class="text-lg font-semibold text-blue-600" data-astro-cid-u2h3djql>${economicsCount}</span> </div> <div class="flex items-center justify-between rounded-lg bg-gray-50 p-4" data-astro-cid-u2h3djql> <div class="flex items-center" data-astro-cid-u2h3djql> <span class="mr-3 text-2xl" data-astro-cid-u2h3djql>🤔</span> <span class="font-medium" data-astro-cid-u2h3djql>哲学研究所</span> </div> <span class="text-lg font-semibold text-blue-600" data-astro-cid-u2h3djql>${philosophyCount}</span> </div> <div class="flex items-center justify-between rounded-lg bg-gray-50 p-4" data-astro-cid-u2h3djql> <div class="flex items-center" data-astro-cid-u2h3djql> <span class="mr-3 text-2xl" data-astro-cid-u2h3djql>🌐</span> <span class="font-medium" data-astro-cid-u2h3djql>互联网研究所</span> </div> <span class="text-lg font-semibold text-blue-600" data-astro-cid-u2h3djql>${internetCount}</span> </div> <div class="flex items-center justify-between rounded-lg bg-gray-50 p-4" data-astro-cid-u2h3djql> <div class="flex items-center" data-astro-cid-u2h3djql> <span class="mr-3 text-2xl" data-astro-cid-u2h3djql>🤖</span> <span class="font-medium" data-astro-cid-u2h3djql>AI研究所</span> </div> <span class="text-lg font-semibold text-blue-600" data-astro-cid-u2h3djql>${aiCount}</span> </div> <div class="flex items-center justify-between rounded-lg bg-gray-50 p-4" data-astro-cid-u2h3djql> <div class="flex items-center" data-astro-cid-u2h3djql> <span class="mr-3 text-2xl" data-astro-cid-u2h3djql>🔮</span> <span class="font-medium" data-astro-cid-u2h3djql>未来研究所</span> </div> <span class="text-lg font-semibold text-blue-600" data-astro-cid-u2h3djql>${futureCount}</span> </div> </div> </div> </div> <!-- 快速操作 --> <div class="rounded-lg bg-white shadow" data-astro-cid-u2h3djql> <div class="border-b border-gray-200 px-6 py-4" data-astro-cid-u2h3djql> <h2 class="text-lg font-medium text-gray-900" data-astro-cid-u2h3djql>快速操作</h2> </div> <div class="p-6" data-astro-cid-u2h3djql> <div class="grid grid-cols-1 gap-4 md:grid-cols-3" data-astro-cid-u2h3djql> <a href="/admin/content/create" class="text-decoration-none flex items-center justify-center rounded-lg border border-gray-300 px-4 py-3 transition-colors hover:bg-gray-50" data-astro-cid-u2h3djql> <span class="mr-2 text-xl" data-astro-cid-u2h3djql>➕</span> <span class="font-medium" data-astro-cid-u2h3djql>创建内容</span> </a> <a href="/admin/content" class="text-decoration-none flex items-center justify-center rounded-lg border border-gray-300 px-4 py-3 transition-colors hover:bg-gray-50" data-astro-cid-u2h3djql> <span class="mr-2 text-xl" data-astro-cid-u2h3djql>📝</span> <span class="font-medium" data-astro-cid-u2h3djql>内容管理</span> </a> <a href="/tags" class="text-decoration-none flex items-center justify-center rounded-lg border border-gray-300 px-4 py-3 transition-colors hover:bg-gray-50" data-astro-cid-u2h3djql> <span class="mr-2 text-xl" data-astro-cid-u2h3djql>🏷️</span> <span class="font-medium" data-astro-cid-u2h3djql>标签管理</span> </a> </div> </div> </div> <!-- 最近活动 --> <div class="mt-8 rounded-lg bg-white shadow" data-astro-cid-u2h3djql> <div class="border-b border-gray-200 px-6 py-4" data-astro-cid-u2h3djql> <h2 class="text-lg font-medium text-gray-900" data-astro-cid-u2h3djql>最近活动</h2> </div> <div class="p-6" data-astro-cid-u2h3djql> <div class="space-y-4" data-astro-cid-u2h3djql> <div class="flex items-center" data-astro-cid-u2h3djql> <div class="h-2 w-2 flex-shrink-0 rounded-full bg-green-400" data-astro-cid-u2h3djql></div> <div class="ml-4" data-astro-cid-u2h3djql> <p class="text-sm text-gray-900" data-astro-cid-u2h3djql>创建了新文章：数字经济发展趋势分析</p> <p class="text-xs text-gray-500" data-astro-cid-u2h3djql>2025年1月1日</p> </div> </div> <div class="flex items-center" data-astro-cid-u2h3djql> <div class="h-2 w-2 flex-shrink-0 rounded-full bg-blue-400" data-astro-cid-u2h3djql></div> <div class="ml-4" data-astro-cid-u2h3djql> <p class="text-sm text-gray-900" data-astro-cid-u2h3djql>添加了研究日志：研究院启动日志</p> <p class="text-xs text-gray-500" data-astro-cid-u2h3djql>2025年1月1日</p> </div> </div> <div class="flex items-center" data-astro-cid-u2h3djql> <div class="h-2 w-2 flex-shrink-0 rounded-full bg-purple-400" data-astro-cid-u2h3djql></div> <div class="ml-4" data-astro-cid-u2h3djql> <p class="text-sm text-gray-900" data-astro-cid-u2h3djql>发布了公告：欢迎来到 Pennfly Private Academy</p> <p class="text-xs text-gray-500" data-astro-cid-u2h3djql>2025年1月1日</p> </div> </div> </div> </div> </div> </div> </main> ` })} `;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/admin/index.astro", void 0);
const $$file = "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/admin/index.astro";
const $$url = "/admin";
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({ __proto__: null, default: $$Index, file: $$file, url: $$url }, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  i as renderers
};
