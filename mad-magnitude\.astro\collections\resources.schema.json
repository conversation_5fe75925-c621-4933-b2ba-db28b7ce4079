{"$ref": "#/definitions/resources", "definitions": {"resources": {"type": "object", "properties": {"title": {"type": "object", "properties": {"zh": {"type": "string"}, "en": {"type": "string"}}, "required": ["zh"], "additionalProperties": false}, "description": {"type": "object", "properties": {"zh": {"type": "string"}, "en": {"type": "string"}}, "required": ["zh"], "additionalProperties": false}, "category": {"type": "string", "enum": ["books", "videos", "software", "datasets", "papers"]}, "items": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "url": {"type": "string", "format": "uri"}, "description": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}, "default": []}, "rating": {"type": "number", "minimum": 1, "maximum": 5}, "notes": {"type": "string"}}, "required": ["name", "description"], "additionalProperties": false}}, "updateDate": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "string", "format": "date"}, {"type": "integer", "format": "unix-time"}]}, "$schema": {"type": "string"}}, "required": ["title", "description", "category", "items", "updateDate"], "additionalProperties": false}}, "$schema": "http://json-schema.org/draft-07/schema#"}