---
export const prerender = false;

import { getEntry } from 'astro:content';
import AcademicLayout from '../../layouts/AcademicLayout.astro';

// 在服务器端渲染模式下，直接从 URL 参数获取内容
const { slug } = Astro.params;

if (!slug) {
  return Astro.redirect('/future');
}

// 获取文章内容
const article = await getEntry('future', slug);

if (!article) {
  return Astro.redirect('/404');
}
const { Content } = await article.render();

const breadcrumbs = [
  { name: '研究院首页', href: '/' },
  { name: '未来研究所', href: '/future' },
  { name: article.data.title.zh, href: `/future/${article.slug}` },
];

const articleMeta = {
  publishDate: article.data.publishDate,
  updateDate: article.data.updateDate,
  readingTime: article.data.readingTime,
  tags: [
    ...article.data.tags,
    ...(article.data.timeHorizon
      ? [
          article.data.timeHorizon === 'short'
            ? '短期预测'
            : article.data.timeHorizon === 'medium'
              ? '中期预测'
              : '长期预测',
        ]
      : []),
    ...(article.data.confidence
      ? [
          article.data.confidence === 'low'
            ? '低信心度'
            : article.data.confidence === 'medium'
              ? '中信心度'
              : '高信心度',
        ]
      : []),
    ...(article.data.domains || []),
  ],
  author: article.data.author,
};
---

<AcademicLayout
  title={article.data.title.zh}
  description={article.data.description.zh}
  breadcrumbs={breadcrumbs}
  article={articleMeta}
  showToc={true}
  showProgress={true}
  enableMermaid={true}
>
  <Content />
</AcademicLayout>
