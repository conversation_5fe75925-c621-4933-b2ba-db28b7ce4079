{"$ref": "#/definitions/economics", "definitions": {"economics": {"type": "object", "properties": {"title": {"type": "object", "properties": {"zh": {"type": "string"}, "en": {"type": "string"}}, "required": ["zh"], "additionalProperties": false}, "description": {"type": "object", "properties": {"zh": {"type": "string"}, "en": {"type": "string"}}, "required": ["zh"], "additionalProperties": false}, "publishDate": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "string", "format": "date"}, {"type": "integer", "format": "unix-time"}]}, "updateDate": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "string", "format": "date"}, {"type": "integer", "format": "unix-time"}]}, "draft": {"type": "boolean", "default": false}, "featured": {"type": "boolean", "default": false}, "tags": {"type": "array", "items": {"type": "string"}, "default": []}, "author": {"type": "string", "default": "Pennfly"}, "readingTime": {"type": "number"}, "relatedContent": {"type": "array", "items": {"type": "string"}}, "summary": {"type": "string"}, "analysisType": {"type": "string", "enum": ["market", "policy", "theory", "data"]}, "dataSource": {"type": "string"}, "$schema": {"type": "string"}}, "required": ["title", "description", "publishDate"], "additionalProperties": false}}, "$schema": "http://json-schema.org/draft-07/schema#"}