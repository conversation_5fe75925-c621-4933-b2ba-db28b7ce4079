/**
 * 设计令牌 - 圆角系统
 * 定义统一的圆角规范
 */

export const borderRadius = {
  none: '0px',
  sm: '0.125rem', // 2px
  DEFAULT: '0.25rem', // 4px
  md: '0.375rem', // 6px
  lg: '0.5rem', // 8px
  xl: '0.75rem', // 12px
  '2xl': '1rem', // 16px
  '3xl': '1.5rem', // 24px
  full: '9999px', // 完全圆形
} as const;

// 语义化圆角定义
export const semanticRadius = {
  // 按钮圆角
  button: {
    sm: borderRadius.sm, // 小按钮
    md: borderRadius.DEFAULT, // 默认按钮
    lg: borderRadius.md, // 大按钮
    pill: borderRadius.full, // 胶囊按钮
  },

  // 卡片圆角
  card: {
    sm: borderRadius.md, // 小卡片
    md: borderRadius.lg, // 默认卡片
    lg: borderRadius.xl, // 大卡片
  },

  // 输入框圆角
  input: {
    sm: borderRadius.sm, // 小输入框
    md: borderRadius.DEFAULT, // 默认输入框
    lg: borderRadius.md, // 大输入框
  },

  // 图片圆角
  image: {
    sm: borderRadius.md, // 小图片
    md: borderRadius.lg, // 默认图片
    lg: borderRadius.xl, // 大图片
    avatar: borderRadius.full, // 头像
  },

  // 弹窗圆角
  modal: {
    sm: borderRadius.lg, // 小弹窗
    md: borderRadius.xl, // 默认弹窗
    lg: borderRadius['2xl'], // 大弹窗
  },
} as const;

// 导出类型定义
export type BorderRadiusToken = typeof borderRadius;
export type SemanticRadius = typeof semanticRadius;
