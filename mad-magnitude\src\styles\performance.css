/* 性能优化相关样式 */

/* 图片懒加载样式 */
img[data-src] {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

img.lazy-loading {
  opacity: 0.5;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

img.lazy-loaded {
  opacity: 1;
}

img.lazy-error {
  opacity: 0.3;
  background-color: #f5f5f5;
  position: relative;
}

img.lazy-error::after {
  content: '图片加载失败';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #999;
  font-size: 12px;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 内容加载骨架屏 */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
}

.skeleton-text {
  height: 1em;
  margin-bottom: 0.5em;
}

.skeleton-text:last-child {
  width: 60%;
}

.skeleton-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.skeleton-card {
  height: 200px;
  border-radius: 8px;
}

/* 优化字体加载 */
@font-face {
  font-family: 'PingFang SC';
  font-display: swap; /* 字体交换策略，提升性能 */
}

/* 关键渲染路径优化 */
.above-fold {
  /* 首屏内容优先级 */
  contain: layout style paint;
}

.below-fold {
  /* 非首屏内容延迟渲染 */
  content-visibility: auto;
  contain-intrinsic-size: 0 500px;
}

/* 滚动性能优化 */
.scroll-container {
  /* 启用硬件加速 */
  transform: translateZ(0);
  /* 优化滚动性能 */
  -webkit-overflow-scrolling: touch;
  /* 滚动捕捉 */
  scroll-behavior: smooth;
}

/* 动画性能优化 */
.gpu-accelerated {
  /* 强制启用硬件加速 */
  transform: translateZ(0);
  will-change: transform, opacity;
}

.will-change-auto {
  will-change: auto;
}

/* 减少重绘和回流 */
.no-reflow {
  /* 避免布局抖动 */
  contain: layout;
}

.no-repaint {
  /* 避免重绘 */
  contain: paint;
}

/* 图片容器优化 */
.image-container {
  /* 避免布局偏移 */
  position: relative;
  overflow: hidden;
}

.image-container::before {
  content: '';
  display: block;
  /* 根据图片比例设置 padding-top */
  padding-top: 56.25%; /* 16:9 比例 */
}

.image-container img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 响应式图片优化 */
.responsive-image {
  width: 100%;
  height: auto;
  /* 避免图片闪烁 */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* 预加载指示器 */
.preload-indicator {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #3b82f6, transparent);
  background-size: 200% 100%;
  animation: preload-progress 2s infinite;
  z-index: 9999;
  opacity: 0;
  transition: opacity 0.3s;
}

.preload-indicator.active {
  opacity: 1;
}

@keyframes preload-progress {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 内容可见性优化 */
.content-visibility-auto {
  content-visibility: auto;
  contain-intrinsic-size: 0 400px;
}

/* 虚拟滚动容器 */
.virtual-scroll {
  height: 400px;
  overflow-y: auto;
  contain: strict;
}

.virtual-item {
  contain: layout style paint;
}

/* 性能监控面板样式 */
.performance-panel {
  position: fixed;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  z-index: 10000;
  max-width: 300px;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s;
}

.performance-panel.visible {
  opacity: 1;
  pointer-events: auto;
}

.performance-panel h3 {
  margin: 0 0 5px 0;
  font-size: 14px;
  color: #4ade80;
}

.performance-panel .metric {
  display: flex;
  justify-content: space-between;
  margin-bottom: 3px;
}

.performance-panel .metric-value {
  color: #60a5fa;
}

.performance-panel .metric-good {
  color: #4ade80;
}

.performance-panel .metric-warning {
  color: #fbbf24;
}

.performance-panel .metric-error {
  color: #f87171;
}

/* 媒体查询优化 */
@media (max-width: 768px) {
  /* 移动端性能优化 */
  .mobile-optimized {
    /* 减少复杂动画 */
    animation-duration: 0.2s;
    /* 简化阴影 */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  /* 移动端图片优化 */
  .responsive-image {
    /* 移动端使用较低质量 */
    image-rendering: auto;
  }
}

@media (prefers-reduced-motion: reduce) {
  /* 尊重用户的动画偏好 */
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .skeleton,
  .lazy-loading,
  .preload-indicator {
    animation: none;
  }
}

/* 高对比度模式优化 */
@media (prefers-contrast: high) {
  .skeleton {
    background: #000;
  }

  .lazy-loading {
    background: #000;
  }
}

/* 打印优化 */
@media print {
  .performance-panel,
  .preload-indicator,
  .skeleton {
    display: none !important;
  }

  img[data-src] {
    opacity: 1;
  }
}
