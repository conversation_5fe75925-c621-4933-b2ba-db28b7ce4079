/**
 * Theme system type definitions for Pennfly Private Academy
 * Supports light, dark, and high contrast themes with comprehensive design tokens
 */

export type ThemeMode = 'light' | 'dark' | 'auto';
export type ThemeVariant = 'default' | 'high-contrast';

export interface ThemeColors {
  // Background colors
  background: {
    primary: string;
    secondary: string;
    tertiary: string;
    elevated: string;
  };

  // Foreground colors
  foreground: {
    primary: string;
    secondary: string;
    tertiary: string;
    inverse: string;
  };

  // Brand colors
  brand: {
    primary: string;
    secondary: string;
    accent: string;
  };

  // Semantic colors
  semantic: {
    success: string;
    warning: string;
    error: string;
    info: string;
  };

  // Interactive colors
  interactive: {
    default: string;
    hover: string;
    active: string;
    disabled: string;
    focus: string;
  };

  // Border colors
  border: {
    default: string;
    subtle: string;
    strong: string;
    interactive: string;
  };
}

export interface ThemeTypography {
  fontFamily: {
    sans: string;
    serif: string;
    mono: string;
    // math 字体已移除
  };
  fontSize: Record<string, [string, { lineHeight: string; letterSpacing?: string }]>;
  fontWeight: Record<string, string>;
}

export interface ThemeSpacing {
  scale: Record<string, string>;
  component: {
    padding: Record<string, string>;
    margin: Record<string, string>;
    gap: Record<string, string>;
  };
}

export interface ThemeBorderRadius {
  none: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  full: string;
}

export interface ThemeShadows {
  sm: string;
  md: string;
  lg: string;
  xl: string;
  inner: string;
  focus: string;
  none: string;
}

export interface ThemeAnimations {
  duration: {
    fast: string;
    normal: string;
    slow: string;
  };
  easing: {
    linear: string;
    ease: string;
    easeIn: string;
    easeOut: string;
    easeInOut: string;
  };
}

export interface Theme {
  name: string;
  mode: ThemeMode;
  variant: ThemeVariant;
  colors: ThemeColors;
  typography: ThemeTypography;
  spacing: ThemeSpacing;
  borderRadius: ThemeBorderRadius;
  shadows: ThemeShadows;
  animations: ThemeAnimations;
}

export interface ThemeConfig {
  defaultTheme: ThemeMode;
  enableSystemTheme: boolean;
  storageKey: string;
  themes: {
    light: Theme;
    dark: Theme;
    highContrast: Theme;
  };
}

export interface ThemeContextValue {
  theme: Theme;
  mode: ThemeMode;
  variant: ThemeVariant;
  setTheme: (mode: ThemeMode) => void;
  setVariant: (variant: ThemeVariant) => void;
  toggleTheme: () => void;
  systemTheme: ThemeMode;
  resolvedTheme: ThemeMode;
}

export interface ThemeProviderProps {
  defaultTheme?: ThemeMode;
  defaultVariant?: ThemeVariant;
  enableSystemTheme?: boolean;
  storageKey?: string;
  children?: any;
}

export interface ThemeToggleProps {
  size?: 'sm' | 'md' | 'lg';
  variant?: 'icon' | 'button' | 'dropdown';
  showLabel?: boolean;
  className?: string;
}

// CSS Custom Properties interface for theme variables
export interface ThemeCSSVariables {
  '--color-background-primary': string;
  '--color-background-secondary': string;
  '--color-background-tertiary': string;
  '--color-background-elevated': string;
  '--color-foreground-primary': string;
  '--color-foreground-secondary': string;
  '--color-foreground-tertiary': string;
  '--color-foreground-inverse': string;
  '--color-brand-primary': string;
  '--color-brand-secondary': string;
  '--color-brand-accent': string;
  '--color-semantic-success': string;
  '--color-semantic-warning': string;
  '--color-semantic-error': string;
  '--color-semantic-info': string;
  '--color-interactive-default': string;
  '--color-interactive-hover': string;
  '--color-interactive-active': string;
  '--color-interactive-disabled': string;
  '--color-interactive-focus': string;
  '--color-border-default': string;
  '--color-border-subtle': string;
  '--color-border-strong': string;
  '--color-border-interactive': string;
  '--shadow-sm': string;
  '--shadow-md': string;
  '--shadow-lg': string;
  '--shadow-xl': string;
  '--shadow-focus': string;
  '--border-radius-sm': string;
  '--border-radius-md': string;
  '--border-radius-lg': string;
  '--border-radius-xl': string;
  '--animation-duration-fast': string;
  '--animation-duration-normal': string;
  '--animation-duration-slow': string;
}
