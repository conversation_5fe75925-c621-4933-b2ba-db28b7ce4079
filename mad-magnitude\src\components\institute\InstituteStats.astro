---
export interface Props {
  stats: {
    total: number;
    featured: number;
    totalTags: number;
    averageReadingTime: number;
    latestDate: Date | null;
    oldestDate: Date | null;
  };
  popularTags: Array<{tag: string, count: number}>;
  showPopularTags?: boolean;
}

const { stats, popularTags, showPopularTags = true } = Astro.props;
---

<div class="institute-stats">
  <!-- 基础统计 -->
  <div class="mb-8 grid gap-6 md:grid-cols-2 lg:grid-cols-4">
    <div class="rounded-lg border border-gray-200 bg-white p-6 text-center shadow-sm">
      <div class="mb-2 text-3xl font-bold text-gray-800">{stats.total}</div>
      <div class="text-sm text-gray-600">研究文章</div>
    </div>
    
    <div class="rounded-lg border border-gray-200 bg-white p-6 text-center shadow-sm">
      <div class="mb-2 text-3xl font-bold text-gray-800">{stats.featured}</div>
      <div class="text-sm text-gray-600">精选文章</div>
    </div>
    
    <div class="rounded-lg border border-gray-200 bg-white p-6 text-center shadow-sm">
      <div class="mb-2 text-3xl font-bold text-gray-800">{stats.totalTags}</div>
      <div class="text-sm text-gray-600">研究标签</div>
    </div>
    
    <div class="rounded-lg border border-gray-200 bg-white p-6 text-center shadow-sm">
      <div class="mb-2 text-3xl font-bold text-gray-800">
        {Math.round(stats.averageReadingTime)}
      </div>
      <div class="text-sm text-gray-600">平均阅读时间(分钟)</div>
    </div>
  </div>

  <!-- 详细统计 -->
  <div class="mb-8 grid gap-6 md:grid-cols-2">
    <!-- 时间统计 -->
    <div class="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
      <h4 class="mb-4 text-lg font-semibold text-gray-800">发布时间统计</h4>
      <div class="space-y-3">
        {stats.latestDate && (
          <div class="flex justify-between">
            <span class="text-gray-600">最新文章:</span>
            <span class="font-medium text-gray-800">
              {stats.latestDate.toLocaleDateString('zh-CN')}
            </span>
          </div>
        )}
        {stats.oldestDate && (
          <div class="flex justify-between">
            <span class="text-gray-600">最早文章:</span>
            <span class="font-medium text-gray-800">
              {stats.oldestDate.toLocaleDateString('zh-CN')}
            </span>
          </div>
        )}
        {stats.latestDate && stats.oldestDate && (
          <div class="flex justify-between">
            <span class="text-gray-600">发布跨度:</span>
            <span class="font-medium text-gray-800">
              {Math.ceil((stats.latestDate.getTime() - stats.oldestDate.getTime()) / (1000 * 60 * 60 * 24))} 天
            </span>
          </div>
        )}
      </div>
    </div>

    <!-- 内容质量统计 -->
    <div class="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
      <h4 class="mb-4 text-lg font-semibold text-gray-800">内容质量</h4>
      <div class="space-y-3">
        <div class="flex justify-between">
          <span class="text-gray-600">精选比例:</span>
          <span class="font-medium text-gray-800">
            {stats.total > 0 ? Math.round((stats.featured / stats.total) * 100) : 0}%
          </span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-600">平均标签数:</span>
          <span class="font-medium text-gray-800">
            {stats.total > 0 ? Math.round(stats.totalTags / stats.total * 10) / 10 : 0}
          </span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-600">内容丰富度:</span>
          <span class="font-medium text-gray-800">
            {stats.averageReadingTime > 10 ? '深度' : stats.averageReadingTime > 5 ? '中等' : '简洁'}
          </span>
        </div>
      </div>
    </div>
  </div>

  <!-- 热门标签 -->
  {showPopularTags && popularTags.length > 0 && (
    <div class="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
      <h4 class="mb-4 text-lg font-semibold text-gray-800">热门研究标签</h4>
      <div class="flex flex-wrap gap-3">
        {popularTags.map(({tag, count}) => (
          <div class="group relative">
            <span class="inline-flex items-center gap-2 rounded-full bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-200">
              {tag}
              <span class="rounded-full bg-gray-300 px-2 py-1 text-xs text-gray-600">
                {count}
              </span>
            </span>
            <!-- 悬停提示 -->
            <div class="absolute bottom-full left-1/2 mb-2 hidden -translate-x-1/2 rounded bg-gray-800 px-2 py-1 text-xs text-white group-hover:block">
              {count} 篇文章
            </div>
          </div>
        ))}
      </div>
    </div>
  )}
</div>

<style>
  .institute-stats {
    /* 确保统计面板的响应式布局 */
  }
  
  /* 悬停效果 */
  .institute-stats .group:hover .group-hover\\:block {
    display: block;
  }
</style>