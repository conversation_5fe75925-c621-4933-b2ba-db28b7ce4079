/**
 * 单个内容 API
 * 获取、更新、删除单个内容项
 */
import type { APIRoute } from 'astro';
import { contentManager } from '../../../utils/contentManager';

export const prerender = false;

export const GET: APIRoute = async ({ params }) => {
  try {
    const { id } = params;

    if (!id) {
      return new Response(
        JSON.stringify({
          error: 'Content ID is required',
          code: 'MISSING_ID',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    // 解码 ID（处理 URL 编码）
    const decodedId = decodeURIComponent(id);
    const content = await contentManager.getContentById(decodedId);

    if (!content) {
      return new Response(
        JSON.stringify({
          error: 'Content not found',
          code: 'NOT_FOUND',
        }),
        {
          status: 404,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    const response = {
      id: content.id,
      collection: content.collection,
      slug: content.slug,
      title: content.title,
      description: content.description,
      publishDate: content.publishDate.toISOString(),
      updateDate: content.updateDate?.toISOString(),
      draft: content.draft,
      featured: content.featured,
      tags: content.tags,
      author: content.author,
      content: content.content,
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=300',
      },
    });
  } catch (error) {
    console.error('获取内容 API 错误:', error);

    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        code: 'INTERNAL_ERROR',
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      }
    );
  }
};

export const PUT: APIRoute = async ({ params, request }) => {
  try {
    const { id } = params;

    if (!id) {
      return new Response(
        JSON.stringify({
          error: 'Content ID is required',
          code: 'MISSING_ID',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    const body = await request.json();
    const { frontmatter, content } = body;

    if (!frontmatter || content === undefined) {
      return new Response(
        JSON.stringify({
          error: 'Frontmatter and content are required',
          code: 'MISSING_DATA',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    // 解码 ID
    const decodedId = decodeURIComponent(id);
    const updatedContent = await contentManager.updateContent(decodedId, frontmatter, content);

    if (!updatedContent) {
      return new Response(
        JSON.stringify({
          error: 'Content not found',
          code: 'NOT_FOUND',
        }),
        {
          status: 404,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    const response = {
      id: updatedContent.id,
      collection: updatedContent.collection,
      slug: updatedContent.slug,
      title: updatedContent.title,
      description: updatedContent.description,
      publishDate: updatedContent.publishDate.toISOString(),
      updateDate: updatedContent.updateDate?.toISOString(),
      draft: updatedContent.draft,
      featured: updatedContent.featured,
      tags: updatedContent.tags,
      author: updatedContent.author,
      content: updatedContent.content,
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('更新内容 API 错误:', error);

    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        code: 'INTERNAL_ERROR',
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      }
    );
  }
};

export const DELETE: APIRoute = async ({ params }) => {
  try {
    const { id } = params;

    if (!id) {
      return new Response(
        JSON.stringify({
          error: 'Content ID is required',
          code: 'MISSING_ID',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    // 解码 ID
    const decodedId = decodeURIComponent(id);
    const success = await contentManager.deleteContent(decodedId);

    if (!success) {
      return new Response(
        JSON.stringify({
          error: 'Content not found or could not be deleted',
          code: 'DELETE_FAILED',
        }),
        {
          status: 404,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Content deleted successfully',
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      }
    );
  } catch (error) {
    console.error('删除内容 API 错误:', error);

    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        code: 'INTERNAL_ERROR',
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      }
    );
  }
};
