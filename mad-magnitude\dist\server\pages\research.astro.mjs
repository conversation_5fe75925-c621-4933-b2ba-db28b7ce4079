import { a as createComponent, h as renderComponent, d as renderTemplate, m as maybeRenderHead, b as addAttribute } from "../assets/vendor-astro.kctgsZae.js";
import { i } from "../assets/vendor-astro.kctgsZae.js";
import "kleur/colors";
import { $ as $$Layout } from "../assets/Layout.BKd1ZXhO.js";
import { e as getCollection } from "../assets/utils.CcA_tyNa.js";
const $$Index = createComponent(async ($$result, $$props, $$slots) => {
  const research = await getCollection("research", ({ data }) => !data.draft);
  const sortedResearch = research.sort(
    (a, b) => new Date(b.data.publishDate).getTime() - new Date(a.data.publishDate).getTime()
  );
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "研究文章 - Pennfly Private Academy" }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<main class="container mx-auto px-4 py-8 max-w-6xl"> <!-- 页面头部 --> <header class="mb-12 text-center"> <h1 class="text-4xl font-bold mb-4 text-gray-800">研究文章</h1> <p class="text-lg text-gray-600">深度研究与学术思考的集合</p> </header> <!-- 文章列表 --> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"> ${sortedResearch.map((post) => renderTemplate`<article class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden"> <div class="p-6"> <h2 class="text-xl font-semibold mb-3 text-gray-800"> <a${addAttribute(`/research/${post.slug}`, "href")} class="hover:text-blue-600 transition-colors"> ${post.data.title.zh} </a> </h2> <p class="text-gray-600 mb-4 line-clamp-3">${post.data.description.zh}</p> <div class="flex items-center justify-between text-sm text-gray-500 mb-4"> <span>${post.data.publishDate.toLocaleDateString("zh-CN")}</span> <span>${post.data.author}</span> </div> <!-- 标签 --> <div class="flex flex-wrap gap-2"> ${post.data.tags.slice(0, 3).map((tag) => renderTemplate`<span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs"> ${tag} </span>`)} </div> </div> </article>`)} </div> ${sortedResearch.length === 0 && renderTemplate`<div class="text-center py-12"> <h2 class="text-2xl font-bold mb-4 text-gray-800">暂无研究文章</h2> <p class="text-gray-600">敬请期待更多精彩内容</p> </div>`} </main> ` })}`;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/research/index.astro", void 0);
const $$file = "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/research/index.astro";
const $$url = "/research";
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({ __proto__: null, default: $$Index, file: $$file, url: $$url }, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  i as renderers
};
