0000000000000000000000000000000000000000 20a773b9efb89ff3374140f625594ad5576c9831 weiwei929 <<EMAIL>> 1754886843 +0800	commit (initial): Initial commit from Astro
20a773b9efb89ff3374140f625594ad5576c9831 20a773b9efb89ff3374140f625594ad5576c9831 weiwei929 <<EMAIL>> 1754887007 +0800	checkout: moving from master to develop
20a773b9efb89ff3374140f625594ad5576c9831 abbc4feb7ceec7f4c4e8a9399e4b7008024f3b4f weiwei929 <<EMAIL>> 1754887102 +0800	commit: chore(tailwind): add Tailwind CSS integration
abbc4feb7ceec7f4c4e8a9399e4b7008024f3b4f 40968b5656505e8aeb7d699d2f7df4630dcc7c3b weiwei929 <<EMAIL>> 1754891208 +0800	commit: feat: 配置 Tailwind CSS v4 并验证样式生效
40968b5656505e8aeb7d699d2f7df4630dcc7c3b cc1a2dac02d85479d1c4922d5eed5cb3a83c9d77 weiwei929 <<EMAIL>> 1754898542 +0800	commit: chore: configure git hooks and commitlint
cc1a2dac02d85479d1c4922d5eed5cb3a83c9d77 cc1a2dac02d85479d1c4922d5eed5cb3a83c9d77 weiwei929 <<EMAIL>> 1754898603 +0800	reset: moving to HEAD
cc1a2dac02d85479d1c4922d5eed5cb3a83c9d77 cc1a2dac02d85479d1c4922d5eed5cb3a83c9d77 weiwei929 <<EMAIL>> 1754898603 +0800	reset: moving to HEAD
cc1a2dac02d85479d1c4922d5eed5cb3a83c9d77 026dadd2dea4d55d62fe46a91e5233b60c0b0ae4 weiwei929 <<EMAIL>> 1754898673 +0800	commit: test: add test files for git workflow validation
