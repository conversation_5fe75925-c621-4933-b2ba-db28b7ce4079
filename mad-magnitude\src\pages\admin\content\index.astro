---
/**
 * 内容管理主页面
 * 显示内容列表和管理功能
 */
import ContentList from '../../../components/admin/ContentList.astro';
import Layout from '../../../layouts/Layout.astro';
import { contentManager } from '../../../utils/contentManager';

// 获取内容统计
const stats = contentManager
  ? await contentManager.getContentStats()
  : {
      total: 0,
      published: 0,
      drafts: 0,
      featured: 0,
      byCollection: {},
      byAuthor: {},
      recentActivity: [],
    };

// 页面元数据
const title = '内容管理 - Pennfly Private Academy';
const description = '管理和编辑学院的所有内容';
---

<Layout title={title} description={description}>
  <main class="content-management-page">
    <div class="container">
      <!-- 页面头部 -->
      <header class="page-header">
        <div class="header-content">
          <h1 class="page-title">
            <span class="title-icon">📝</span>
            内容管理
          </h1>
          <p class="page-description">管理和编辑学院的所有内容，包括文章、研究报告、日志等</p>
        </div>
        <div class="header-actions">
          <a href="/admin/content/create" class="btn btn--primary">
            <span class="btn-icon">➕</span>
            新建内容
          </a>
          <a href="/admin" class="btn btn--secondary">
            <span class="btn-icon">🏠</span>
            返回后台
          </a>
        </div>
      </header>

      <!-- 统计概览 -->
      <section class="stats-overview">
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">📊</div>
            <div class="stat-content">
              <div class="stat-number">{stats.total}</div>
              <div class="stat-label">总内容数</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">✅</div>
            <div class="stat-content">
              <div class="stat-number">{stats.published}</div>
              <div class="stat-label">已发布</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">📝</div>
            <div class="stat-content">
              <div class="stat-number">{stats.drafts}</div>
              <div class="stat-label">草稿</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">⭐</div>
            <div class="stat-content">
              <div class="stat-number">{stats.featured}</div>
              <div class="stat-label">特色内容</div>
            </div>
          </div>
        </div>
      </section>

      <!-- 按集合统计 -->
      <section class="collection-stats">
        <h2 class="section-title">
          <span class="title-icon">📂</span>
          集合统计
        </h2>
        <div class="collection-grid">
          {
            Object.entries(stats.byCollection).map(([collection, count]) => (
              <div class="collection-card">
                <div class="collection-header">
                  <span class="collection-icon">
                    {collection === 'news' && '📰'}
                    {collection === 'logs' && '📔'}
                    {collection === 'research' && '📊'}
                    {collection === 'reflections' && '💭'}
                    {collection === 'economics' && '💰'}
                    {collection === 'philosophy' && '🤔'}
                    {collection === 'internet' && '🌐'}
                    {collection === 'ai' && '🤖'}
                    {collection === 'future' && '🔮'}
                    {collection === 'products' && '🛠️'}
                  </span>
                  <span class="collection-name">
                    {collection === 'news' && '动态资讯'}
                    {collection === 'logs' && '研究日志'}
                    {collection === 'research' && '研究报告'}
                    {collection === 'reflections' && '反思记录'}
                    {collection === 'economics' && '经济研究'}
                    {collection === 'philosophy' && '哲学研究'}
                    {collection === 'internet' && '互联网研究'}
                    {collection === 'ai' && 'AI研究'}
                    {collection === 'future' && '未来研究'}
                    {collection === 'products' && '产品发布'}
                  </span>
                </div>
                <div class="collection-count">{count}</div>
              </div>
            ))
          }
        </div>
      </section>

      <!-- 最近活动 -->
      <section class="recent-activity">
        <h2 class="section-title">
          <span class="title-icon">🕒</span>
          最近活动
        </h2>
        <div class="activity-list">
          {
            stats.recentActivity.slice(0, 10).map(activity => (
              <div class="activity-item">
                <div class="activity-icon">{activity.action === '创建' ? '➕' : '✏️'}</div>
                <div class="activity-content">
                  <div class="activity-text">
                    <span class="activity-action">{activity.action}</span>
                    了内容：
                    <span class="activity-title">{activity.content}</span>
                  </div>
                  <div class="activity-date">
                    {new Date(activity.date).toLocaleDateString('zh-CN', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                      hour: '2-digit',
                      minute: '2-digit',
                    })}
                  </div>
                </div>
              </div>
            ))
          }
        </div>
      </section>

      <!-- 内容列表 -->
      <section class="content-list-section">
        <h2 class="section-title">
          <span class="title-icon">📋</span>
          内容列表
        </h2>
        <ContentList showFilters={true} showActions={true} />
      </section>
    </div>
  </main>
</Layout>

<style>
  .content-management-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    padding: 2rem 0;
  }

  .container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  /* 页面头部 */
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding: 2rem;
    background: white;
    border-radius: 0.75rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .header-content {
    flex: 1;
  }

  .page-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 2rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.5rem;
  }

  .title-icon {
    font-size: 1.75rem;
  }

  .page-description {
    color: #64748b;
    font-size: 1rem;
    line-height: 1.6;
    max-width: 600px;
  }

  .header-actions {
    display: flex;
    gap: 0.75rem;
    flex-shrink: 0;
  }

  /* 按钮样式 */
  .btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
  }

  .btn--primary {
    background: #3b82f6;
    color: white;
  }

  .btn--primary:hover {
    background: #2563eb;
    transform: translateY(-1px);
  }

  .btn--secondary {
    background: #f1f5f9;
    color: #64748b;
    border: 1px solid #e2e8f0;
  }

  .btn--secondary:hover {
    background: #e2e8f0;
    color: #475569;
  }

  .btn-icon {
    font-size: 1rem;
  }

  /* 统计概览 */
  .stats-overview {
    margin-bottom: 2rem;
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }

  .stat-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: white;
    border-radius: 0.75rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
  }

  .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .stat-icon {
    font-size: 2rem;
    opacity: 0.8;
  }

  .stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #1e293b;
    line-height: 1;
  }

  .stat-label {
    font-size: 0.875rem;
    color: #64748b;
    margin-top: 0.25rem;
  }

  /* 章节样式 */
  .section-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
  }

  /* 集合统计 */
  .collection-stats {
    margin-bottom: 2rem;
  }

  .collection-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
  }

  .collection-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem;
    background: white;
    border-radius: 0.75rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
  }

  .collection-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .collection-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
  }

  .collection-icon {
    font-size: 2rem;
  }

  .collection-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    text-align: center;
  }

  .collection-count {
    font-size: 1.5rem;
    font-weight: 700;
    color: #3b82f6;
  }

  /* 最近活动 */
  .recent-activity {
    margin-bottom: 2rem;
  }

  .activity-list {
    background: white;
    border-radius: 0.75rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #f1f5f9;
    transition: background-color 0.2s ease;
  }

  .activity-item:hover {
    background: #f8fafc;
  }

  .activity-item:last-child {
    border-bottom: none;
  }

  .activity-icon {
    font-size: 1.25rem;
    opacity: 0.7;
  }

  .activity-content {
    flex: 1;
  }

  .activity-text {
    font-size: 0.875rem;
    color: #374151;
    margin-bottom: 0.25rem;
  }

  .activity-action {
    font-weight: 500;
    color: #3b82f6;
  }

  .activity-title {
    font-weight: 500;
    color: #1e293b;
  }

  .activity-date {
    font-size: 0.75rem;
    color: #64748b;
  }

  /* 内容列表部分 */
  .content-list-section {
    margin-bottom: 2rem;
  }

  /* 响应式设计 */
  @media (max-width: 1024px) {
    .page-header {
      flex-direction: column;
      gap: 1rem;
      align-items: stretch;
    }

    .header-actions {
      justify-content: center;
    }

    .stats-grid {
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .collection-grid {
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
  }

  @media (max-width: 768px) {
    .content-management-page {
      padding: 1rem 0;
    }

    .container {
      padding: 0 0.75rem;
    }

    .page-header {
      padding: 1.5rem;
    }

    .page-title {
      font-size: 1.75rem;
    }

    .stats-grid {
      grid-template-columns: 1fr;
    }

    .collection-grid {
      grid-template-columns: repeat(2, 1fr);
    }

    .activity-item {
      padding: 0.75rem 1rem;
    }
  }
</style>
