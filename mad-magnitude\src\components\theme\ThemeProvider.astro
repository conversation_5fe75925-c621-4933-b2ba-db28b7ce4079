---
/**
 * ThemeProvider component for Pennfly Private Academy
 * Provides theme context and manages theme state across the application
 */

import type { ThemeProviderProps } from '../../types/theme';
import { themeConfig } from '../../styles/tokens/themes';

interface Props extends ThemeProviderProps {
  class?: string;
}

const {
  defaultTheme = themeConfig.defaultTheme,
  defaultVariant = 'default',
  enableSystemTheme = themeConfig.enableSystemTheme,
  storageKey = themeConfig.storageKey,
  class: className = '',
  ...rest
} = Astro.props;

// Theme configurations are now statically defined in CSS for better performance
---

<!-- Theme CSS Variables -->
<style is:global>
  /* Light theme */
  :root[data-theme='light'] {
    /* Background colors */
    --color-background-primary: #ffffff;
    --color-background-secondary: #f9fafb;
    --color-background-tertiary: #f3f4f6;
    --color-background-elevated: #ffffff;

    /* Foreground colors */
    --color-foreground-primary: #111827;
    --color-foreground-secondary: #374151;
    --color-foreground-tertiary: #6b7280;
    --color-foreground-inverse: #ffffff;

    /* Brand colors */
    --color-brand-primary: #2563eb;
    --color-brand-secondary: #f59e0b;
    --color-brand-accent: #3b82f6;

    /* Semantic colors */
    --color-semantic-success: #10b981;
    --color-semantic-warning: #f59e0b;
    --color-semantic-error: #ef4444;
    --color-semantic-info: #3b82f6;

    /* Interactive colors */
    --color-interactive-default: #2563eb;
    --color-interactive-hover: #1d4ed8;
    --color-interactive-active: #1e40af;
    --color-interactive-disabled: #9ca3af;
    --color-interactive-focus: #3b82f6;

    /* Border colors */
    --color-border-default: #e5e7eb;
    --color-border-subtle: #f3f4f6;
    --color-border-strong: #d1d5db;
    --color-border-interactive: #2563eb;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-focus: 0 0 0 3px rgb(59 130 246 / 0.5);

    /* Border radius */
    --border-radius-sm: 0.125rem;
    --border-radius-md: 0.375rem;
    --border-radius-lg: 0.5rem;
    --border-radius-xl: 0.75rem;

    /* Animation durations */
    --animation-duration-fast: 150ms;
    --animation-duration-normal: 300ms;
    --animation-duration-slow: 500ms;
  }

  /* Dark theme */
  :root[data-theme='dark'] {
    /* Background colors */
    --color-background-primary: #111827;
    --color-background-secondary: #1f2937;
    --color-background-tertiary: #374151;
    --color-background-elevated: #1f2937;

    /* Foreground colors */
    --color-foreground-primary: #f9fafb;
    --color-foreground-secondary: #e5e7eb;
    --color-foreground-tertiary: #9ca3af;
    --color-foreground-inverse: #111827;

    /* Brand colors */
    --color-brand-primary: #3b82f6;
    --color-brand-secondary: #fbbf24;
    --color-brand-accent: #60a5fa;

    /* Semantic colors */
    --color-semantic-success: #34d399;
    --color-semantic-warning: #fbbf24;
    --color-semantic-error: #f87171;
    --color-semantic-info: #60a5fa;

    /* Interactive colors */
    --color-interactive-default: #3b82f6;
    --color-interactive-hover: #60a5fa;
    --color-interactive-active: #2563eb;
    --color-interactive-disabled: #6b7280;
    --color-interactive-focus: #60a5fa;

    /* Border colors */
    --color-border-default: #374151;
    --color-border-subtle: #1f2937;
    --color-border-strong: #4b5563;
    --color-border-interactive: #3b82f6;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.4);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.4), 0 8px 10px -6px rgb(0 0 0 / 0.4);
    --shadow-focus: 0 0 0 3px rgb(96 165 250 / 0.5);

    /* Border radius */
    --border-radius-sm: 0.125rem;
    --border-radius-md: 0.375rem;
    --border-radius-lg: 0.5rem;
    --border-radius-xl: 0.75rem;

    /* Animation durations */
    --animation-duration-fast: 150ms;
    --animation-duration-normal: 300ms;
    --animation-duration-slow: 500ms;
  }

  /* High contrast theme */
  :root[data-theme='highContrast'] {
    /* Background colors */
    --color-background-primary: #000000;
    --color-background-secondary: #1a1a1a;
    --color-background-tertiary: #333333;
    --color-background-elevated: #1a1a1a;

    /* Foreground colors */
    --color-foreground-primary: #ffffff;
    --color-foreground-secondary: #f0f0f0;
    --color-foreground-tertiary: #cccccc;
    --color-foreground-inverse: #000000;

    /* Brand colors */
    --color-brand-primary: #66b3ff;
    --color-brand-secondary: #ffcc00;
    --color-brand-accent: #80d4ff;

    /* Semantic colors */
    --color-semantic-success: #00ff88;
    --color-semantic-warning: #ffcc00;
    --color-semantic-error: #ff6666;
    --color-semantic-info: #66b3ff;

    /* Interactive colors */
    --color-interactive-default: #66b3ff;
    --color-interactive-hover: #80d4ff;
    --color-interactive-active: #4da6ff;
    --color-interactive-disabled: #666666;
    --color-interactive-focus: #80d4ff;

    /* Border colors */
    --color-border-default: #666666;
    --color-border-subtle: #333333;
    --color-border-strong: #999999;
    --color-border-interactive: #66b3ff;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(255 255 255 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(255 255 255 / 0.15), 0 2px 4px -2px rgb(255 255 255 / 0.15);
    --shadow-lg: 0 10px 15px -3px rgb(255 255 255 / 0.2), 0 4px 6px -4px rgb(255 255 255 / 0.2);
    --shadow-xl: 0 20px 25px -5px rgb(255 255 255 / 0.25), 0 8px 10px -6px rgb(255 255 255 / 0.25);
    --shadow-focus: 0 0 0 3px rgb(128 212 255 / 0.8);

    /* Border radius */
    --border-radius-sm: 0.125rem;
    --border-radius-md: 0.375rem;
    --border-radius-lg: 0.5rem;
    --border-radius-xl: 0.75rem;

    /* Animation durations */
    --animation-duration-fast: 150ms;
    --animation-duration-normal: 300ms;
    --animation-duration-slow: 500ms;
  }

  /* Default theme fallback */
  :root {
    --color-background-primary: #ffffff;
    --color-background-secondary: #f9fafb;
    --color-background-tertiary: #f3f4f6;
    --color-background-elevated: #ffffff;
    --color-foreground-primary: #111827;
    --color-foreground-secondary: #374151;
    --color-foreground-tertiary: #6b7280;
    --color-foreground-inverse: #ffffff;
    --color-brand-primary: #2563eb;
    --color-brand-secondary: #f59e0b;
    --color-brand-accent: #3b82f6;
    --color-semantic-success: #10b981;
    --color-semantic-warning: #f59e0b;
    --color-semantic-error: #ef4444;
    --color-semantic-info: #3b82f6;
    --color-interactive-default: #2563eb;
    --color-interactive-hover: #1d4ed8;
    --color-interactive-active: #1e40af;
    --color-interactive-disabled: #9ca3af;
    --color-interactive-focus: #3b82f6;
    --color-border-default: #e5e7eb;
    --color-border-subtle: #f3f4f6;
    --color-border-strong: #d1d5db;
    --color-border-interactive: #2563eb;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-focus: 0 0 0 3px rgb(59 130 246 / 0.5);
    --border-radius-sm: 0.125rem;
    --border-radius-md: 0.375rem;
    --border-radius-lg: 0.5rem;
    --border-radius-xl: 0.75rem;
    --animation-duration-fast: 150ms;
    --animation-duration-normal: 300ms;
    --animation-duration-slow: 500ms;
  }

  /* Smooth transitions for theme changes */
  * {
    transition:
      background-color var(--animation-duration-normal) ease,
      border-color var(--animation-duration-normal) ease,
      color var(--animation-duration-normal) ease,
      box-shadow var(--animation-duration-normal) ease;
  }

  /* Respect user's motion preferences */
  @media (prefers-reduced-motion: reduce) {
    * {
      transition: none !important;
      animation: none !important;
    }
  }
</style>

<!-- Theme Provider Container -->
<div
  class={`theme-provider ${className}`}
  data-theme={defaultTheme}
  data-variant={defaultVariant}
  data-enable-system={enableSystemTheme}
  data-storage-key={storageKey}
  {...rest}
>
  <slot />
</div>

<!-- Theme Management Script -->
<script>
  import type { ThemeMode, ThemeVariant } from '../../types/theme';

  class ThemeManager {
    private storageKey: string;
    private enableSystemTheme: boolean;
    private currentTheme: ThemeMode;
    private currentVariant: ThemeVariant;
    private systemTheme: ThemeMode;

    constructor() {
      const provider = document.querySelector('.theme-provider');
      this.storageKey = provider?.getAttribute('data-storage-key') || 'pennfly-theme';
      this.enableSystemTheme = provider?.getAttribute('data-enable-system') === 'true';
      this.currentTheme = (provider?.getAttribute('data-theme') as ThemeMode) || 'light';
      this.currentVariant = (provider?.getAttribute('data-variant') as ThemeVariant) || 'default';
      this.systemTheme = this.getSystemTheme();

      this.init();
    }

    private init(): void {
      // Load saved theme from localStorage
      const savedTheme = this.loadTheme();
      if (savedTheme) {
        this.currentTheme = savedTheme.mode;
        this.currentVariant = savedTheme.variant;
      }

      // Apply initial theme
      this.applyTheme();

      // Listen for system theme changes
      if (this.enableSystemTheme) {
        this.watchSystemTheme();
      }

      // Expose theme manager to global scope for components
      (window as any).themeManager = this;
    }

    private getSystemTheme(): ThemeMode {
      if (typeof window === 'undefined') return 'light';
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }

    private watchSystemTheme(): void {
      if (typeof window === 'undefined') return;

      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      mediaQuery.addEventListener('change', e => {
        this.systemTheme = e.matches ? 'dark' : 'light';
        if (this.currentTheme === 'auto') {
          this.applyTheme();
        }
      });
    }

    private loadTheme(): { mode: ThemeMode; variant: ThemeVariant } | null {
      if (typeof window === 'undefined') return null;

      try {
        const saved = localStorage.getItem(this.storageKey);
        return saved ? JSON.parse(saved) : null;
      } catch {
        return null;
      }
    }

    private saveTheme(): void {
      if (typeof window === 'undefined') return;

      try {
        localStorage.setItem(
          this.storageKey,
          JSON.stringify({
            mode: this.currentTheme,
            variant: this.currentVariant,
          })
        );
      } catch {
        // Ignore localStorage errors
      }
    }

    private applyTheme(): void {
      const resolvedTheme = this.getResolvedTheme();
      const themeAttribute =
        this.currentVariant === 'high-contrast' ? 'highContrast' : resolvedTheme;

      document.documentElement.setAttribute('data-theme', themeAttribute);
      document.documentElement.setAttribute('data-theme-mode', this.currentTheme);
      document.documentElement.setAttribute('data-theme-variant', this.currentVariant);

      // Update meta theme-color for mobile browsers
      this.updateMetaThemeColor(resolvedTheme);

      // Dispatch theme change event
      window.dispatchEvent(
        new CustomEvent('themechange', {
          detail: {
            theme: resolvedTheme,
            mode: this.currentTheme,
            variant: this.currentVariant,
            systemTheme: this.systemTheme,
          },
        })
      );
    }

    private updateMetaThemeColor(theme: ThemeMode): void {
      const metaThemeColor = document.querySelector('meta[name="theme-color"]');
      if (metaThemeColor) {
        const color = theme === 'dark' ? '#111827' : '#ffffff';
        metaThemeColor.setAttribute('content', color);
      }
    }

    private getResolvedTheme(): ThemeMode {
      if (this.currentTheme === 'auto') {
        return this.systemTheme;
      }
      return this.currentTheme;
    }

    // Public API methods
    public setTheme(mode: ThemeMode): void {
      this.currentTheme = mode;
      this.saveTheme();
      this.applyTheme();
    }

    public setVariant(variant: ThemeVariant): void {
      this.currentVariant = variant;
      this.saveTheme();
      this.applyTheme();
    }

    public toggleTheme(): void {
      const themes: ThemeMode[] = ['light', 'dark'];
      const currentIndex = themes.indexOf(this.getResolvedTheme());
      const nextIndex = (currentIndex + 1) % themes.length;
      this.setTheme(themes[nextIndex]);
    }

    public getTheme(): ThemeMode {
      return this.currentTheme;
    }

    public getVariant(): ThemeVariant {
      return this.currentVariant;
    }

    public getResolvedThemePublic(): ThemeMode {
      return this.getResolvedTheme();
    }

    public getSystemThemePublic(): ThemeMode {
      return this.systemTheme;
    }
  }

  // Initialize theme manager when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => new ThemeManager());
  } else {
    new ThemeManager();
  }
</script>
