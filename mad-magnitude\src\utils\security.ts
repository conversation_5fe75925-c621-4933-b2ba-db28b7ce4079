/**
 * Security utilities for HTTP headers, input validation, and content sanitization
 */

import { getEnvConfig } from './env.js';

/**
 * Security headers configuration
 */
export interface SecurityHeaders {
  'Content-Security-Policy'?: string;
  'Content-Security-Policy-Report-Only'?: string;
  'X-Content-Type-Options': string;
  'X-Frame-Options': string;
  'X-XSS-Protection': string;
  'Referrer-Policy': string;
  'Strict-Transport-Security'?: string;
  'Permissions-Policy'?: string;
}

/**
 * Generate secure HTTP headers
 */
export function getSecurityHeaders(): SecurityHeaders {
  const config = getEnvConfig();
  const isDev = config.NODE_ENV === 'development';
  
  const headers: SecurityHeaders = {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
  };
  
  // Content Security Policy
  if (config.CSP_ENABLED) {
    const cspDirectives = [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline'", // Allow inline scripts for Astro
      "style-src 'self' 'unsafe-inline'", // Allow inline styles for Tailwind
      "img-src 'self' data: https:",
      "font-src 'self' data:",
      "connect-src 'self'",
      "media-src 'self'",
      "object-src 'none'",
      "child-src 'none'",
      "worker-src 'self'",
      "frame-ancestors 'none'",
      "form-action 'self'",
      "base-uri 'self'",
      "manifest-src 'self'",
    ];
    
    // Add development-specific CSP rules
    if (isDev) {
      cspDirectives.push("connect-src 'self' ws: wss:");
    }
    
    const cspValue = cspDirectives.join('; ');
    
    if (config.CSP_REPORT_ONLY) {
      headers['Content-Security-Policy-Report-Only'] = cspValue;
    } else {
      headers['Content-Security-Policy'] = cspValue;
    }
  }
  
  // HSTS for production
  if (!isDev) {
    headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains; preload';
  }
  
  // Permissions Policy
  headers['Permissions-Policy'] = [
    'camera=()',
    'microphone=()',
    'geolocation=()',
    'interest-cohort=()',
    'payment=()',
    'usb=()',
  ].join(', ');
  
  return headers;
}

/**
 * HTML sanitization patterns
 */
const DANGEROUS_HTML_PATTERNS = [
  /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
  /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
  /<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi,
  /<embed\b[^<]*>/gi,
  /<form\b[^<]*(?:(?!<\/form>)<[^<]*)*<\/form>/gi,
  /javascript:/gi,
  /vbscript:/gi,
  /data:text\/html/gi,
  /\son\w+\s*=\s*["'][^"']*["']/gi, // Event handlers like onclick="...", onload='...'
  /\son\w+\s*=\s*[^"'\s>]+/gi, // Event handlers without quotes
];

/**
 * Sanitize HTML content to prevent XSS attacks
 */
export function sanitizeHtml(html: string): string {
  if (!html || typeof html !== 'string') {
    return '';
  }
  
  let sanitized = html;
  
  // Remove dangerous HTML patterns
  DANGEROUS_HTML_PATTERNS.forEach(pattern => {
    sanitized = sanitized.replace(pattern, '');
  });
  
  // Additional specific cleanups
  // Remove event handlers more aggressively
  sanitized = sanitized.replace(/\s+on\w+\s*=\s*["'][^"']*["']/gi, '');
  sanitized = sanitized.replace(/\s+on\w+\s*=\s*[^"'\s>]+/gi, '');
  
  // Clean up javascript: protocols in attributes
  sanitized = sanitized.replace(/(href|src|action)\s*=\s*["']javascript:[^"']*["']/gi, '$1=""');
  sanitized = sanitized.replace(/(href|src|action)\s*=\s*javascript:[^"'\s>]*/gi, '$1=""');
  
  return sanitized;
}

/**
 * Sanitize text input to prevent injection attacks
 */
export function sanitizeText(text: string): string {
  if (!text || typeof text !== 'string') {
    return '';
  }
  
  return text
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/vbscript:/gi, '') // Remove vbscript: protocol
    .replace(/data:/gi, '') // Remove data: protocol
    .trim();
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  if (!email || typeof email !== 'string') {
    return false;
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 254;
}

/**
 * Validate URL format
 */
export function isValidUrl(url: string): boolean {
  if (!url || typeof url !== 'string') {
    return false;
  }
  
  try {
    const urlObj = new URL(url);
    return ['http:', 'https:'].includes(urlObj.protocol);
  } catch {
    return false;
  }
}

/**
 * Validate and sanitize form input
 */
export interface FormValidationResult {
  isValid: boolean;
  sanitizedValue: string;
  errors: string[];
}

export function validateFormInput(
  value: string,
  options: {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    type?: 'text' | 'email' | 'url';
  } = {}
): FormValidationResult {
  const errors: string[] = [];
  let sanitizedValue = '';
  
  // Basic sanitization
  if (typeof value === 'string') {
    sanitizedValue = sanitizeText(value);
  }
  
  // Required validation
  if (options.required && !sanitizedValue) {
    errors.push('This field is required');
  }
  
  // Length validation
  if (sanitizedValue) {
    if (options.minLength && sanitizedValue.length < options.minLength) {
      errors.push(`Minimum length is ${options.minLength} characters`);
    }
    
    if (options.maxLength && sanitizedValue.length > options.maxLength) {
      errors.push(`Maximum length is ${options.maxLength} characters`);
      sanitizedValue = sanitizedValue.substring(0, options.maxLength);
    }
  }
  
  // Pattern validation
  if (sanitizedValue && options.pattern && !options.pattern.test(sanitizedValue)) {
    errors.push('Invalid format');
  }
  
  // Type-specific validation
  if (sanitizedValue && options.type) {
    switch (options.type) {
      case 'email':
        if (!isValidEmail(sanitizedValue)) {
          errors.push('Invalid email format');
        }
        break;
      case 'url':
        if (!isValidUrl(sanitizedValue)) {
          errors.push('Invalid URL format');
        }
        break;
    }
  }
  
  return {
    isValid: errors.length === 0,
    sanitizedValue,
    errors,
  };
}

/**
 * Rate limiting utility (simple in-memory implementation)
 */
class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  
  constructor(
    private maxRequests: number = 100,
    private windowMs: number = 15 * 60 * 1000 // 15 minutes
  ) {}
  
  isAllowed(identifier: string): boolean {
    const now = Date.now();
    const windowStart = now - this.windowMs;
    
    // Get existing requests for this identifier
    const requests = this.requests.get(identifier) || [];
    
    // Filter out old requests
    const recentRequests = requests.filter(time => time > windowStart);
    
    // Check if limit exceeded
    if (recentRequests.length >= this.maxRequests) {
      return false;
    }
    
    // Add current request
    recentRequests.push(now);
    this.requests.set(identifier, recentRequests);
    
    return true;
  }
  
  reset(identifier: string): void {
    this.requests.delete(identifier);
  }
  
  cleanup(): void {
    const now = Date.now();
    const windowStart = now - this.windowMs;
    
    for (const [identifier, requests] of this.requests.entries()) {
      const recentRequests = requests.filter(time => time > windowStart);
      
      if (recentRequests.length === 0) {
        this.requests.delete(identifier);
      } else {
        this.requests.set(identifier, recentRequests);
      }
    }
  }
}

// Global rate limiter instance
export const rateLimiter = new RateLimiter();

// Cleanup rate limiter every 5 minutes
if (typeof setInterval !== 'undefined') {
  setInterval(() => {
    rateLimiter.cleanup();
  }, 5 * 60 * 1000);
}

/**
 * Generate a secure random token
 */
export function generateSecureToken(length: number = 32): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  
  // Use crypto.getRandomValues if available (browser/modern Node.js)
  if (typeof crypto !== 'undefined' && crypto?.getRandomValues) {
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);
    
    for (let i = 0; i < length; i++) {
      const randomValue = array[i];
      if (randomValue !== undefined) {
        result += chars[randomValue % chars.length];
      }
    }
  } else {
    // Fallback to Math.random (less secure)
    for (let i = 0; i < length; i++) {
      result += chars[Math.floor(Math.random() * chars.length)];
    }
  }
  
  return result;
}

/**
 * Hash a string using a simple hash function (for non-cryptographic purposes)
 */
export function simpleHash(str: string): string {
  let hash = 0;
  
  if (str.length === 0) return hash.toString();
  
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  
  return Math.abs(hash).toString(36);
}

/**
 * Security middleware for Astro API routes
 */
export function createSecurityMiddleware() {
  return {
    /**
     * Apply security headers to response
     */
    applyHeaders: (response: Response): Response => {
      const headers = getSecurityHeaders();
      
      Object.entries(headers).forEach(([key, value]) => {
        if (value && typeof value === 'string') {
          response.headers.set(key, value);
        }
      });
      
      return response;
    },
    
    /**
     * Validate request rate limiting
     */
    checkRateLimit: (request: Request): boolean => {
      const clientIP = request.headers.get('x-forwarded-for') || 
                      request.headers.get('x-real-ip') || 
                      'unknown';
      
      return rateLimiter.isAllowed(clientIP);
    },
    
    /**
     * Validate request origin
     */
    validateOrigin: (request: Request): boolean => {
      const origin = request.headers.get('origin');
      const referer = request.headers.get('referer');
      const config = getEnvConfig();
      
      if (!origin && !referer) {
        return false; // Require origin or referer
      }
      
      const allowedOrigins = [config.SITE_URL];
      
      if (config.NODE_ENV === 'development') {
        allowedOrigins.push(`http://${config.DEV_HOST}:${config.DEV_PORT}`);
        allowedOrigins.push('http://localhost:4321');
      }
      
      if (origin) {
        return allowedOrigins.some(allowed => origin.startsWith(allowed));
      }
      
      if (referer) {
        return allowedOrigins.some(allowed => referer.startsWith(allowed));
      }
      
      return false;
    },
  };
}