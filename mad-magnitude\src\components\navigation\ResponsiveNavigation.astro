---
import SearchBox from '../SearchBox.astro';

// 导航菜单配置
const navigationItems = [
  {
    label: '首页',
    href: '/',
    icon: '🏠',
  },
  {
    label: '动态资讯',
    href: '/news',
    icon: '📰',
  },
  {
    label: '研究日志',
    href: '/logs',
    icon: '📔',
  },
  {
    label: '研究所',
    icon: '🏛️',
    children: [
      {
        label: '经济研究所',
        href: '/economics',
        icon: '💰',
        description: '经济分析与市场洞察',
      },
      {
        label: '哲学研究所',
        href: '/philosophy',
        icon: '🤔',
        description: '思想探讨与理论研究',
      },
      {
        label: '互联网研究所',
        href: '/internet',
        icon: '🌐',
        description: '行业分析与趋势预测',
      },
      {
        label: 'AI研究所',
        href: '/ai',
        icon: '🤖',
        description: 'AI技术与应用研究',
      },
      {
        label: '未来研究所',
        href: '/future',
        icon: '🔮',
        description: '前瞻性思考与趋势判断',
      },
    ],
  },
  {
    label: '产品发布',
    href: '/products',
    icon: '🚀',
  },
  {
    label: '关于',
    href: '/about',
    icon: '👤',
  },
];

// 获取当前路径
const currentPath = Astro.url.pathname;
---

<header class="sticky top-0 z-50 border-b border-gray-200 bg-white shadow-sm">
  <div class="container mx-auto px-4">
    <!-- 主导航栏 -->
    <div class="flex items-center justify-between py-4">
      <!-- Logo -->
      <a href="/" class="flex items-center space-x-3 transition-opacity hover:opacity-80">
        <img 
          src="/ppa-logo.PNG" 
          alt="Pennfly Private Academy" 
          class="h-8 w-auto" 
          width="32" 
          height="32"
          loading="eager"
          decoding="async"
        />
        <!-- 响应式标题 -->
        <div class="hidden sm:block">
          <div class="text-xl font-bold text-slate-800">Pennfly Private Academy</div>
          <div class="text-xs text-slate-600">私人研究院</div>
        </div>
        <!-- 移动端简化标题 -->
        <div class="block sm:hidden">
          <div class="text-base font-bold text-slate-800">PPA</div>
        </div>
      </a>

      <!-- 桌面端导航 -->
      <nav class="hidden items-center space-x-1 lg:flex">
        {
          navigationItems.map(item => (
            <div class="group relative">
              {item.children ? (
                <div>
                  <button
                    class="flex items-center space-x-1 rounded-lg px-3 py-2 text-slate-700 transition-colors hover:bg-slate-50 hover:text-blue-600"
                    aria-haspopup="true"
                    aria-expanded="false"
                  >
                    <span class="text-sm">{item.icon}</span>
                    <span class="font-medium">{item.label}</span>
                    <svg
                      class="h-4 w-4 transition-transform group-hover:rotate-180"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                  </button>

                  <div
                    class="invisible absolute top-full left-0 mt-1 w-64 rounded-lg border border-gray-200 bg-white opacity-0 shadow-lg transition-all duration-200 group-hover:visible group-hover:opacity-100"
                    role="menu"
                  >
                    <div class="p-2">
                      {item.children.map(child => (
                        <a
                          href={child.href}
                          class={`flex items-start space-x-3 rounded-lg p-3 transition-colors hover:bg-slate-50 ${
                            currentPath.startsWith(child.href)
                              ? 'bg-blue-50 text-blue-600'
                              : 'text-slate-700'
                          }`}
                          role="menuitem"
                        >
                          <span class="text-lg" aria-hidden="true">
                            {child.icon}
                          </span>
                          <div>
                            <div class="font-medium">{child.label}</div>
                            <div class="mt-1 text-xs text-slate-600">{child.description}</div>
                          </div>
                        </a>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <a
                  href={item.href}
                  class={`flex items-center space-x-1 rounded-lg px-3 py-2 transition-colors ${
                    currentPath === item.href ||
                    (item.href !== '/' && currentPath.startsWith(item.href))
                      ? 'bg-blue-50 text-blue-600'
                      : 'text-slate-700 hover:bg-slate-50 hover:text-blue-600'
                  }`}
                >
                  <span class="text-sm" aria-hidden="true">
                    {item.icon}
                  </span>
                  <span class="font-medium">{item.label}</span>
                </a>
              )}
            </div>
          ))
        }
      </nav>

      <!-- 右侧操作区 -->
      <div class="flex items-center space-x-2 sm:space-x-4">
        <!-- 搜索框 - 响应式 -->
        <div class="hidden md:block">
          <SearchBox />
        </div>

        <!-- 移动端搜索按钮 -->
        <button
          class="p-2 text-slate-600 transition-colors hover:text-blue-600 md:hidden"
          aria-label="搜索"
          id="mobile-search-btn"
        >
          <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
        </button>

        <!-- 移动端菜单按钮 -->
        <button
          class="p-2 text-slate-600 transition-colors hover:text-blue-600 lg:hidden"
          aria-label="打开菜单"
          id="mobile-menu-btn"
        >
          <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- 移动端搜索栏 -->
    <div class="hidden border-t border-gray-200 pb-4 md:hidden" id="mobile-search">
      <div class="pt-4">
        <SearchBox />
      </div>
    </div>
  </div>

  <!-- 移动端菜单 -->
  <div class="hidden border-t border-gray-200 bg-white lg:hidden" id="mobile-menu">
    <div class="container mx-auto px-4 py-4">
      <nav class="space-y-2">
        {
          navigationItems.map(item => (
            <div>
              {item.children ? (
                <div>
                  <button
                    class="mobile-dropdown-btn flex w-full items-center justify-between rounded-lg px-3 py-2 text-left text-slate-700 transition-colors hover:bg-slate-50"
                    aria-expanded="false"
                  >
                    <div class="flex items-center space-x-2">
                      <span class="text-sm" aria-hidden="true">
                        {item.icon}
                      </span>
                      <span class="font-medium">{item.label}</span>
                    </div>
                    <svg
                      class="h-4 w-4 transition-transform"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                  </button>

                  <div class="mobile-dropdown-content mt-2 ml-4 hidden space-y-1">
                    {item.children.map(child => (
                      <a
                        href={child.href}
                        class={`flex items-center space-x-2 rounded-lg px-3 py-2 transition-colors ${
                          currentPath.startsWith(child.href)
                            ? 'bg-blue-50 text-blue-600'
                            : 'text-slate-600 hover:bg-slate-50'
                        }`}
                      >
                        <span class="text-sm" aria-hidden="true">
                          {child.icon}
                        </span>
                        <span class="text-sm">{child.label}</span>
                      </a>
                    ))}
                  </div>
                </div>
              ) : (
                <a
                  href={item.href}
                  class={`flex items-center space-x-2 rounded-lg px-3 py-2 transition-colors ${
                    currentPath === item.href ||
                    (item.href !== '/' && currentPath.startsWith(item.href))
                      ? 'bg-blue-50 text-blue-600'
                      : 'text-slate-700 hover:bg-slate-50'
                  }`}
                >
                  <span class="text-sm" aria-hidden="true">
                    {item.icon}
                  </span>
                  <span class="font-medium">{item.label}</span>
                </a>
              )}
            </div>
          ))
        }
      </nav>
    </div>
  </div>
</header>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    // 移动端菜单切换
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const mobileMenu = document.getElementById('mobile-menu');

    mobileMenuBtn?.addEventListener('click', () => {
      const isHidden = mobileMenu?.classList.contains('hidden');

      if (isHidden) {
        mobileMenu?.classList.remove('hidden');
        mobileMenuBtn.setAttribute('aria-label', '关闭菜单');
      } else {
        mobileMenu?.classList.add('hidden');
        mobileMenuBtn.setAttribute('aria-label', '打开菜单');
      }
    });

    // 移动端搜索切换
    const mobileSearchBtn = document.getElementById('mobile-search-btn');
    const mobileSearch = document.getElementById('mobile-search');

    mobileSearchBtn?.addEventListener('click', () => {
      const isHidden = mobileSearch?.classList.contains('hidden');

      if (isHidden) {
        mobileSearch?.classList.remove('hidden');
        mobileSearchBtn.setAttribute('aria-label', '关闭搜索');
      } else {
        mobileSearch?.classList.add('hidden');
        mobileSearchBtn.setAttribute('aria-label', '搜索');
      }
    });

    // 移动端下拉菜单
    const dropdownBtns = document.querySelectorAll('.mobile-dropdown-btn');
    dropdownBtns.forEach(btn => {
      btn.addEventListener('click', () => {
        const content = btn.nextElementSibling;
        const icon = btn.querySelector('svg');
        const isHidden = content?.classList.contains('hidden');

        if (isHidden) {
          content?.classList.remove('hidden');
          icon?.classList.add('rotate-180');
          btn.setAttribute('aria-expanded', 'true');
        } else {
          content?.classList.add('hidden');
          icon?.classList.remove('rotate-180');
          btn.setAttribute('aria-expanded', 'false');
        }
      });
    });

    // 点击外部关闭菜单
    document.addEventListener('click', (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (!target?.closest('header')) {
        mobileMenu?.classList.add('hidden');
        mobileSearch?.classList.add('hidden');
        mobileMenuBtn?.setAttribute('aria-label', '打开菜单');
        mobileSearchBtn?.setAttribute('aria-label', '搜索');
      }
    });
  });
</script>

<style>
  /* 确保下拉菜单在悬停时保持可见 */
  .group:hover .group-hover\:opacity-100 {
    opacity: 1;
  }

  .group:hover .group-hover\:visible {
    visibility: visible;
  }
</style>
