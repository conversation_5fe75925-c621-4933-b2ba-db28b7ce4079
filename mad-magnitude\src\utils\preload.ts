/**
 * 资源预加载工具
 */

export interface PreloadResource {
  href: string;
  as: 'script' | 'style' | 'image' | 'font' | 'fetch' | 'document';
  type?: string;
  crossorigin?: 'anonymous' | 'use-credentials';
  media?: string;
}

export class ResourcePreloader {
  private preloadedResources = new Set<string>();

  /**
   * 预加载单个资源
   */
  preload(resource: PreloadResource): void {
    if (this.preloadedResources.has(resource.href)) {
      return;
    }

    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = resource.href;
    link.as = resource.as;

    if (resource.type) {
      link.type = resource.type;
    }

    if (resource.crossorigin) {
      link.crossOrigin = resource.crossorigin;
    }

    if (resource.media) {
      link.media = resource.media;
    }

    document.head.appendChild(link);
    this.preloadedResources.add(resource.href);
  }

  /**
   * 预加载多个资源
   */
  preloadMultiple(resources: PreloadResource[]): void {
    resources.forEach(resource => this.preload(resource));
  }

  /**
   * 预加载关键 CSS
   */
  preloadCriticalCSS(href: string): void {
    this.preload({
      href,
      as: 'style',
      type: 'text/css',
    });
  }

  /**
   * 预加载字体
   */
  preloadFont(href: string, type: string = 'font/woff2'): void {
    this.preload({
      href,
      as: 'font',
      type,
      crossorigin: 'anonymous',
    });
  }

  /**
   * 预加载图片
   */
  preloadImage(href: string): void {
    this.preload({
      href,
      as: 'image',
    });
  }

  /**
   * 预加载 JavaScript
   */
  preloadScript(href: string): void {
    this.preload({
      href,
      as: 'script',
      type: 'text/javascript',
    });
  }

  /**
   * 预连接到外部域名
   */
  preconnect(href: string, crossorigin: boolean = false): void {
    const link = document.createElement('link');
    link.rel = 'preconnect';
    link.href = href;

    if (crossorigin) {
      link.crossOrigin = 'anonymous';
    }

    document.head.appendChild(link);
  }

  /**
   * DNS 预解析
   */
  dnsPrefetch(href: string): void {
    const link = document.createElement('link');
    link.rel = 'dns-prefetch';
    link.href = href;
    document.head.appendChild(link);
  }

  /**
   * 预取资源（低优先级）
   */
  prefetch(href: string): void {
    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = href;
    document.head.appendChild(link);
  }

  /**
   * 获取已预加载的资源列表
   */
  getPreloadedResources(): string[] {
    return Array.from(this.preloadedResources);
  }

  /**
   * 清理预加载资源记录
   */
  clear(): void {
    this.preloadedResources.clear();
  }
}

// 全局预加载器实例
export const globalPreloader = new ResourcePreloader();

// 预加载关键资源的配置
export const criticalResources: PreloadResource[] = [
  // 关键 CSS
  {
    href: '/assets/styles/critical.css',
    as: 'style',
    type: 'text/css',
  },
  // Logo 图片
  {
    href: '/ppa-logo.PNG',
    as: 'image',
  },
  // 关键字体
  {
    href: '/fonts/main.woff2',
    as: 'font',
    type: 'font/woff2',
    crossorigin: 'anonymous',
  },
];

// 自动预加载关键资源
export function initCriticalResourcePreload(): void {
  if (typeof window !== 'undefined') {
    // 预连接到常用的外部域名
    globalPreloader.preconnect('https://fonts.googleapis.com');
    globalPreloader.preconnect('https://fonts.gstatic.com', true);
    globalPreloader.preconnect('https://cdn.jsdelivr.net');

    // DNS 预解析
    globalPreloader.dnsPrefetch('//unpkg.com');
    globalPreloader.dnsPrefetch('//cdnjs.cloudflare.com');

    // 预加载关键资源
    globalPreloader.preloadMultiple(criticalResources);
  }
}

// 根据页面类型预加载特定资源
export function preloadPageResources(pageType: string): void {
  const pageResources: { [key: string]: PreloadResource[] } = {
    home: [
      { href: '/assets/home.js', as: 'script' },
      { href: '/assets/home.css', as: 'style' },
    ],
    article: [
      { href: '/assets/article.js', as: 'script' },
      { href: '/assets/academic.css', as: 'style' },
    ],
    search: [
      { href: '/search-index.json', as: 'fetch' },
      { href: '/assets/search.js', as: 'script' },
    ],
  };

  const resources = pageResources[pageType];
  if (resources) {
    globalPreloader.preloadMultiple(resources);
  }
}
