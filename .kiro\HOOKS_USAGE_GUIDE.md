# Agent Hooks 使用指南

## 🎯 什么是 Agent Hooks？

Agent Hooks 是 Kiro 的自动化助手功能，可以在特定事件发生时（如保存文件）或手动触发时，自动运行预定义的任务来帮助你改进代码质量、检查问题、优化性能等。

## 📋 已配置的 Hooks

### 🔄 自动触发的 Hooks

这些 hooks 会在你保存特定类型的文件时自动弹出提示：

#### 1. 文档同步器 (`docs-sync-hook`)

- **触发时机**: 保存项目文件时
- **功能**: 自动更新项目文档和 README
- **使用场景**: 代码或配置变更时

#### 2. 内容验证器 (`content-validator`)

- **触发时机**: 保存 `mad-magnitude/src/content/**/*.md` 文件时
- **功能**: 检查内容格式、frontmatter、标签、语法等
- **使用场景**: 创建或编辑博客文章、研究报告时

#### 3. 组件优化器 (`component-optimizer`)

- **触发时机**: 保存 `mad-magnitude/src/components/**/*.astro` 文件时
- **功能**: 检查 TypeScript 类型、性能、可访问性
- **使用场景**: 开发或修改 Astro 组件时

#### 4. SEO 优化器 (`seo-optimizer`)

- **触发时机**: 保存 `mad-magnitude/src/pages/**/*.astro` 文件时
- **功能**: 检查页面 SEO 设置、元标签、结构等
- **使用场景**: 创建或修改页面时

### 🔧 手动触发的 Hooks

这些 hooks 需要你在 Kiro 界面中点击按钮来运行：

#### 5. 构建分析器 (`build-analyzer`)

- **按钮名称**: "分析构建结果"
- **功能**: 分析构建性能和优化建议
- **使用场景**: 发布前性能检查

#### 6. 可访问性检查器 (`accessibility-checker`)

- **按钮名称**: "检查可访问性"
- **功能**: 全面检查 WCAG 合规性
- **使用场景**: 确保网站无障碍访问

#### 7. 内容创建助手 (`content-creator`)

- **按钮名称**: "创建新内容"
- **功能**: 帮助创建符合规范的新内容
- **使用场景**: 需要创建新文章或内容时

## 🚀 如何使用 Hooks

### 方法一：自动触发

1. 编辑相关文件（如 `.md` 或 `.astro` 文件）
2. 保存文件 (Ctrl+S)
3. Kiro 会弹出通知询问是否运行相应的 hook
4. 点击"运行"或"跳过"

### 方法二：手动触发

1. 在 Kiro 界面中找到 "Agent Hooks" 面板
2. 点击相应的按钮（如"分析构建结果"）
3. 等待 hook 执行完成
4. 查看结果和建议

### 方法三：命令面板

1. 按 `Ctrl+Shift+P` 打开命令面板
2. 搜索 "Kiro Hook" 或具体的 hook 名称
3. 选择要运行的 hook

## 📝 实际使用示例

### 示例 1: 内容验证

当你保存一个内容文件时：

```markdown
---
title:
  zh: "我的新文章"
description:
  zh: "这是一篇关于..."
publishDate: 2025-01-14
tags: ["技术", "教程"]
---

# 我的新文章

内容...
```

内容验证器会检查：

- frontmatter 格式是否正确
- 必需字段是否存在
- 标签是否合适
- markdown 语法是否正确

### 示例 2: 组件优化

当你保存一个组件文件时：

```astro
---
interface Props {
  title: string;
  description?: string;
}

const { title, description } = Astro.props;
---

<div class="component">
  <h2>{title}</h2>
  {description && <p>{description}</p>}
</div>
```

组件优化器会检查：

- TypeScript 类型是否完整
- 是否有可访问性问题
- 性能是否可以优化
- 代码质量如何

### 示例 3: 构建分析

点击"分析构建结果"按钮后，会检查：

- 构建产物大小
- 性能指标
- 优化建议
- 质量评估

## 🎯 建议的使用流程

### 日常开发

1. **编写代码** → 保存文件 → **自动 hook 检查** → 修复问题
2. **定期运行** 手动 hooks 检查整体状况
3. **发布前** 运行所有质量检查 hooks

### 内容创建

1. 使用 "内容创建助手" 生成模板
2. 编写内容并保存
3. "内容验证器" 自动检查质量
4. 根据建议修改和优化

### 性能优化

1. 开发完成后运行 "构建分析器"
2. 根据分析结果优化代码
3. 重新分析确认改进效果

## ⚙️ 自定义配置

如果你想修改 hook 的行为：

1. **禁用某个 hook**: 编辑对应的 `.kiro.hook` 文件，设置 `"enabled": false`
2. **修改触发条件**: 编辑 `"when"` 部分
3. **调整提示内容**: 修改 `"then"` 部分的 `"prompt"`

## 🆘 常见问题

**Q: Hook 没有触发怎么办？**
A: 检查文件路径是否匹配 hook 的 patterns，确保 hook 是启用状态

**Q: 可以同时运行多个 hook 吗？**
A: 可以，但建议一个一个处理，避免混乱

**Q: Hook 的建议是否必须采纳？**
A: 不是，这些只是建议，你可以根据实际情况选择是否采纳

**Q: 如何查看 hook 的执行历史？**
A: 在 Kiro 的历史面板中可以查看之前的执行记录

## 🎉 开始使用

现在你可以：

1. 尝试编辑一个内容文件并保存，体验自动触发的 hook
2. 点击 "分析构建结果" 按钮，查看项目性能状态
3. 根据建议逐步改进代码质量

记住，这些 hooks 是你的助手，帮助你提高开发效率和代码质量！
