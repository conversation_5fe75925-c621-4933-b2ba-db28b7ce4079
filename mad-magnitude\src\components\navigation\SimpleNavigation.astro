---
import SearchBox from '../SearchBox.astro';

// 简化的导航配置
const navigationItems = [
  { label: '首页', href: '/', icon: '🏠' },
  { label: '动态资讯', href: '/news', icon: '📰' },
  { label: '研究日志', href: '/logs', icon: '📔' },
  {
    label: '研究所',
    icon: '🏛️',
    children: [
      { label: '经济研究所', href: '/economics', icon: '💰' },
      { label: '哲学研究所', href: '/philosophy', icon: '🤔' },
      { label: '互联网研究所', href: '/internet', icon: '🌐' },
      { label: 'AI研究所', href: '/ai', icon: '🤖' },
      { label: '未来研究所', href: '/future', icon: '🔮' },
    ],
  },
  { label: '产品发布', href: '/products', icon: '🚀' },
  { label: '关于', href: '/about', icon: '👤' },
];

const currentPath = Astro.url.pathname;

function isActive(href: string): boolean {
  if (href === '/') return currentPath === '/';
  return currentPath.startsWith(href);
}
---

<header
  class="sticky top-0 z-50 border-b border-slate-700 shadow-lg"
  style="background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 50%, #2563eb 100%);"
>
  <div class="container mx-auto px-6">
    <div class="flex items-center py-3">
      <div class="flex flex-1 items-center">
        <!-- Logo -->
        <a
          href="/"
          class="flex flex-shrink-0 items-center space-x-2 transition-all duration-200 hover:opacity-90"
        >
          <img
            src="/ppa-logo.PNG?v=1"
            alt="Pennfly Private Academy"
            class="h-5 w-auto"
            width="20"
            height="20"
            loading="eager"
          />
          <div class="hidden lg:block">
            <div class="text-base leading-tight font-bold text-white">Pennfly Private Academy</div>
            <div class="-mt-1 text-xs text-blue-100">私人研究院</div>
          </div>
          <!-- 移动端和中等屏幕简化标题 -->
          <div class="block lg:hidden">
            <div class="text-sm font-bold text-white">PPA</div>
          </div>
        </a>

        <!-- 桌面端导航 -->
        <nav class="hidden items-center space-x-1 lg:flex">
          {
            navigationItems.map(item => (
              <div class="group relative">
                {item.children ? (
                  <div>
                    <button class="flex items-center space-x-2 rounded-lg border border-white/20 bg-white/10 px-4 py-2 font-medium text-white transition-all duration-200 hover:bg-white/20">
                      <span class="text-base">{item.icon}</span>
                      <span class="text-sm">{item.label}</span>
                      <svg
                        class="h-4 w-4 transition-transform duration-200 group-hover:rotate-180"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M19 9l-7 7-7-7"
                        />
                      </svg>
                    </button>

                    <div class="invisible absolute top-full left-0 mt-2 w-56 rounded-xl border border-gray-200 bg-white/95 opacity-0 shadow-xl backdrop-blur-sm transition-all duration-300 group-hover:visible group-hover:opacity-100">
                      <div class="p-3">
                        {item.children.map(child => (
                          <a
                            href={child.href}
                            class={`flex items-center space-x-3 rounded-lg p-3 transition-all duration-200 ${
                              isActive(child.href)
                                ? 'bg-blue-50 text-blue-600 shadow-sm'
                                : 'text-slate-700 hover:bg-blue-50/50 hover:text-blue-600'
                            }`}
                          >
                            <span class="text-lg">{child.icon}</span>
                            <span class="font-medium">{child.label}</span>
                          </a>
                        ))}
                      </div>
                    </div>
                  </div>
                ) : (
                  <a
                    href={item.href}
                    class={`flex items-center space-x-2 rounded-lg border px-4 py-2 font-medium transition-all duration-200 ${
                      isActive(item.href)
                        ? 'border-white/30 bg-white/20 text-white'
                        : 'border-white/20 bg-white/10 text-white hover:border-white/30 hover:bg-white/20'
                    }`}
                  >
                    <span class="text-base">{item.icon}</span>
                    <span class="text-sm">{item.label}</span>
                  </a>
                )}
              </div>
            ))
          }
        </nav>

        <!-- 右侧工具栏 -->
        <div class="flex items-center space-x-3">
          <div class="hidden md:block">
            <SearchBox />
          </div>

          <!-- 移动端菜单按钮 -->
          <button
            class="rounded-lg border border-white/20 bg-white/10 p-2 text-white transition-all duration-200 hover:bg-white/20 lg:hidden"
            id="mobile-menu-button"
          >
            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- 移动端搜索 -->
      <div id="mobile-search" class="hidden pb-4 md:hidden">
        <SearchBox />
      </div>
    </div>

    <!-- 移动端菜单 -->
    <div id="mobile-menu" class="hidden border-t border-gray-200 bg-white lg:hidden">
      <div class="container mx-auto px-4 py-4">
        <nav class="space-y-2">
          {
            navigationItems.map(item => (
              <div>
                {item.children ? (
                  <div>
                    <button class="mobile-dropdown-btn flex w-full items-center justify-between rounded-lg p-3 text-slate-700 transition-colors hover:bg-slate-50">
                      <div class="flex items-center space-x-2">
                        <span>{item.icon}</span>
                        <span>{item.label}</span>
                      </div>
                      <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M19 9l-7 7-7-7"
                        />
                      </svg>
                    </button>

                    <div class="mobile-dropdown-content mt-2 ml-4 hidden space-y-1">
                      {item.children.map(child => (
                        <a
                          href={child.href}
                          class={`flex items-center space-x-2 rounded-lg p-2 transition-colors ${
                            isActive(child.href)
                              ? 'bg-blue-50 text-blue-600'
                              : 'text-slate-600 hover:bg-slate-50'
                          }`}
                        >
                          <span>{child.icon}</span>
                          <span>{child.label}</span>
                        </a>
                      ))}
                    </div>
                  </div>
                ) : (
                  <a
                    href={item.href}
                    class={`flex items-center space-x-2 rounded-lg p-3 transition-colors ${
                      isActive(item.href)
                        ? 'bg-blue-50 text-blue-600'
                        : 'text-slate-700 hover:bg-slate-50'
                    }`}
                  >
                    <span>{item.icon}</span>
                    <span>{item.label}</span>
                  </a>
                )}
              </div>
            ))
          }
        </nav>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const mobileMenuBtn = document.getElementById('mobile-menu-button');
      const mobileMenu = document.getElementById('mobile-menu');
      const mobileSearch = document.getElementById('mobile-search');

      // 移动端菜单切换
      mobileMenuBtn?.addEventListener('click', () => {
        const isHidden = mobileMenu?.classList.contains('hidden');

        if (isHidden) {
          mobileMenu?.classList.remove('hidden');
          mobileSearch?.classList.remove('hidden');
        } else {
          mobileMenu?.classList.add('hidden');
          mobileSearch?.classList.add('hidden');
        }
      });

      // 移动端下拉菜单
      document.querySelectorAll('.mobile-dropdown-btn').forEach(btn => {
        btn.addEventListener('click', () => {
          const content = btn.nextElementSibling;
          const isHidden = content?.classList.contains('hidden');

          if (isHidden) {
            content?.classList.remove('hidden');
          } else {
            content?.classList.add('hidden');
          }
        });
      });

      // 点击外部关闭菜单
      document.addEventListener('click', e => {
        const target = e.target as HTMLElement;
        if (!target?.closest('header')) {
          mobileMenu?.classList.add('hidden');
          mobileSearch?.classList.add('hidden');
        }
      });
    });
  </script>
</header>
