---
title:
  zh: '测试内容验证功能'
  en: 'Test Content Validation'
description:
  zh: '这是一个用于测试内容验证 hook 的示例文章'
  en: 'This is a sample article for testing content validation hook'
publishDate: 2025-01-14
updateDate: 2025-01-14
draft: true
featured: false
tags: ['测试', '内容管理', '自动化']
author: 'Pennfly'
type: 'research'
relatedInstitute: ['ai']
---

# 测试内容验证功能

这是一个测试文章，用于验证内容验证 hook 是否正常工作。

## 主要内容

这篇文章包含了：

1. **完整的 frontmatter** - 包含所有必需字段
2. **合适的标签** - 使用了项目推荐的标签
3. **正确的格式** - 遵循 markdown 语法规范
4. **数学公式测试** - $E = mc^2$
5. **代码块测试**：

```typescript
interface TestInterface {
  id: string;
  name: string;
}
```

## 图表测试

```mermaid
graph TD
    A[开始] --> B[内容验证]
    B --> C[格式检查]
    C --> D[质量评估]
    D --> E[完成]
```

这个文件应该能通过所有的验证检查。
