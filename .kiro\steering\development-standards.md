---
inclusion: fileMatch
fileMatchPattern: "mad-magnitude/src/**/*"
---

# 开发标准和最佳实践

## 代码质量标准

### TypeScript 规范

- 必须使用严格模式 (`strict: true`)
- 所有函数参数和返回值必须有类型注解
- 避免使用 `any` 类型，使用 `unknown` 替代
- 使用接口定义复杂对象类型
- 导出的组件必须有 Props 接口定义

### Astro 组件规范

```astro
---
// 1. 导入语句
import Layout from '../layouts/Layout.astro';
import { getCollection } from 'astro:content';

// 2. 类型定义
interface Props {
  title: string;
  description?: string;
}

// 3. Props 解构
const { title, description } = Astro.props;

// 4. 数据获取和处理
const posts = await getCollection('blog');

// 5. 服务端逻辑
export const prerender = false; // 如果需要动态渲染
---

<!-- HTML 模板 -->
<Layout title={title}>
  <main>
    <!-- 内容 -->
  </main>
</Layout>

<style>
  /* 组件样式 */
</style>
```

### 样式规范

- 优先使用 Tailwind CSS 类
- 自定义样式使用 CSS 模块或 scoped styles
- 响应式设计: mobile-first 方法
- 使用语义化的 CSS 类名
- 支持暗色模式和高对比度模式

## 内容管理规范

### Content Collections 结构

```typescript
// src/content/config.ts
const baseSchema = z.object({
  title: z.object({
    zh: z.string(),
    en: z.string().optional(),
  }),
  description: z.object({
    zh: z.string(),
    en: z.string().optional(),
  }),
  publishDate: z.date(),
  updateDate: z.date().optional(),
  draft: z.boolean().default(false),
  featured: z.boolean().default(false),
  tags: z.array(z.string()).default([]),
  author: z.string().default("Pennfly"),
});
```

### 内容文件规范

```markdown
---
title:
  zh: "中文标题"
  en: "English Title"
description:
  zh: "中文描述"
  en: "English Description"
publishDate: 2025-01-14
updateDate: 2025-01-14
draft: false
featured: false
tags: ["标签1", "标签2"]
author: "Pennfly"
---

# 内容标题

内容正文...
```

## 性能优化规范

### 图片处理

- 使用 Astro 的 `<Image>` 组件
- 提供 alt 文本
- 使用适当的尺寸和格式
- 启用懒加载

### 代码分割

- 大型组件使用动态导入
- 第三方库按需加载
- 使用 Astro 的岛屿架构

### 缓存策略

- 静态资源设置长期缓存
- HTML 文件设置短期缓存
- 使用版本化的资源文件名

## 可访问性要求

### WCAG 2.1 AA 标准

- 所有图片必须有 alt 属性
- 颜色对比度 ≥ 4.5:1
- 支持键盘导航
- 提供跳转链接
- 使用语义化 HTML

### 屏幕阅读器支持

- 使用适当的 ARIA 标签
- 提供页面标题和描述
- 使用正确的标题层级
- 表单元素有关联的标签

## 测试规范

### 单元测试

- 使用 Vitest 框架
- 测试覆盖率 ≥ 80%
- 测试文件命名: `*.test.ts`
- 测试关键业务逻辑

### 集成测试

- 测试组件渲染
- 测试数据获取
- 测试用户交互

## 安全规范

### 内容安全

- 清理用户输入
- 使用 CSP 头
- 避免 XSS 攻击
- 验证外部链接

### 依赖管理

- 定期更新依赖
- 使用 npm audit 检查漏洞
- 锁定依赖版本

## Git 工作流

### 分支策略

- `main`: 生产分支
- `develop`: 开发分支
- `feature/*`: 功能分支
- `hotfix/*`: 紧急修复分支

### 提交规范

```
type(scope): description

body

footer
```

类型:

- `feat`: 新功能
- `fix`: 修复
- `docs`: 文档
- `style`: 格式
- `refactor`: 重构
- `test`: 测试
- `chore`: 构建/工具

## 部署规范

### 构建检查

- TypeScript 编译无错误
- ESLint 检查通过
- 测试全部通过
- 构建产物大小合理

### 环境配置

- 开发环境: 热重载、详细错误
- 生产环境: 压缩、优化、缓存
