---
/**
 * 标签总览页面
 * 展示所有标签的统计和分类信息
 */
import Layout from '../../layouts/Layout.astro';
import TagCloud from '../../components/tags/TagCloud.astro';
import TagStats from '../../components/tags/TagStats.astro';
import { globalTagManager } from '../../utils/tagManager';

// 获取标签统计数据
const tagStats = await globalTagManager.getTagStats();

// 页面元数据
const title = '标签总览 - Pennfly Private Academy';
const description = `探索 ${tagStats.totalTags} 个标签，涵盖技术、经济、哲学等多个领域的学术内容分类。`;
---

<Layout title={title} description={description}>
  <main class="tags-overview-page">
    <!-- 页面头部 -->
    <header class="page-header">
      <div class="container">
        <div class="header-content">
          <h1 class="page-title">
            <span class="title-icon">🏷️</span>
            标签总览
          </h1>
          <p class="page-description">
            探索我们的内容标签体系，发现感兴趣的主题和领域。 目前共有 <strong
              >{tagStats.totalTags}</strong
            > 个标签， 累计使用 <strong>{tagStats.totalUniqueContent}</strong> 次。
          </p>
        </div>
      </div>
    </header>

    <div class="container">
      <div class="content-grid">
        <!-- 标签统计 -->
        <section class="stats-section">
          <TagStats showCategories={true} showTrends={true} />
        </section>

        <!-- 标签云 -->
        <section class="cloud-section">
          <div class="section-header">
            <h2 class="section-title">
              <span class="title-icon">☁️</span>
              热门标签云
            </h2>
            <p class="section-description">标签大小反映使用频率，点击标签查看相关内容</p>
          </div>
          <TagCloud maxTags={50} showCount={true} size="medium" interactive={true} />
        </section>

        <!-- 分类标签列表 -->
        <section class="categories-section">
          <div class="section-header">
            <h2 class="section-title">
              <span class="title-icon">📂</span>
              分类标签
            </h2>
            <p class="section-description">按主题分类浏览所有标签</p>
          </div>
          <div class="categories-grid">
            {
              Object.entries(tagStats.tagsByCategory).map(([category, tags]) => (
                <div class="category-section">
                  <h3 class="category-title">
                    <span class="category-icon">
                      {category === 'technology' && '🔬'}
                      {category === 'economics' && '💰'}
                      {category === 'philosophy' && '🤔'}
                      {category === 'society' && '🏛️'}
                      {category === 'research' && '📊'}
                      {category === 'tools' && '🛠️'}
                      {category === 'general' && '📝'}
                    </span>
                    {category === 'technology' && '技术'}
                    {category === 'economics' && '经济'}
                    {category === 'philosophy' && '哲学'}
                    {category === 'society' && '社会'}
                    {category === 'research' && '研究'}
                    {category === 'tools' && '工具'}
                    {category === 'general' && '通用'}
                    <span class="category-count">({tags.length})</span>
                  </h3>
                  <div class="category-tags">
                    {tags.slice(0, 12).map(tag => (
                      <a
                        href={`/tags/${encodeURIComponent(tag.name)}`}
                        class="category-tag"
                        style={`--tag-color: ${tag.color}`}
                        title={`${tag.name} (${tag.count} 篇内容)`}
                      >
                        <span class="tag-name">{tag.name}</span>
                        <span class="tag-count">({tag.count})</span>
                      </a>
                    ))}
                    {tags.length > 12 && (
                      <button class="show-more-btn" data-category={category}>
                        查看更多 ({tags.length - 12})
                      </button>
                    )}
                  </div>
                </div>
              ))
            }
          </div>
        </section>

        <!-- 快速导航 -->
        <aside class="quick-nav">
          <h3 class="nav-title">快速导航</h3>
          <nav class="nav-links">
            <a href="#stats" class="nav-link">📊 统计概览</a>
            <a href="#cloud" class="nav-link">☁️ 标签云</a>
            <a href="#categories" class="nav-link">📂 分类浏览</a>
            <a href="/search" class="nav-link">🔍 搜索内容</a>
          </nav>
          <div class="nav-tips">
            <h4 class="tips-title">💡 使用提示</h4>
            <ul class="tips-list">
              <li>点击标签查看相关内容</li>
              <li>标签大小反映使用频率</li>
              <li>可按分类浏览标签</li>
              <li>支持标签组合筛选</li>
            </ul>
          </div>
        </aside>
      </div>
    </div>
  </main>
</Layout>

<style>
  .tags-overview-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  /* 页面头部 */
  .page-header {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    color: white;
    padding: 3rem 0;
    margin-bottom: 2rem;
  }

  .header-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
  }

  .page-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
  }

  .title-icon {
    font-size: 2rem;
  }

  .page-description {
    font-size: 1.125rem;
    line-height: 1.6;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
  }

  /* 内容网格 */
  .content-grid {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 2rem;
    margin-bottom: 3rem;
  }

  /* 章节样式 */
  .section-header {
    margin-bottom: 1.5rem;
  }

  .section-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.5rem;
  }

  .section-description {
    color: #64748b;
    line-height: 1.5;
  }

  /* 统计部分 */
  .stats-section {
    margin-bottom: 2rem;
  }

  /* 标签云部分 */
  .cloud-section {
    margin-bottom: 2rem;
  }

  /* 分类部分 */
  .categories-grid {
    display: grid;
    gap: 2rem;
  }

  .category-section {
    background: white;
    border-radius: 0.75rem;
    border: 1px solid #e2e8f0;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .category-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
  }

  .category-icon {
    font-size: 1.5rem;
  }

  .category-count {
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 400;
  }

  .category-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .category-tag {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.375rem 0.75rem;
    background: rgba(var(--tag-color-rgb, 59, 130, 246), 0.1);
    color: var(--tag-color, #3b82f6);
    border: 1px solid rgba(var(--tag-color-rgb, 59, 130, 246), 0.2);
    border-radius: 0.375rem;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .category-tag:hover {
    background: rgba(var(--tag-color-rgb, 59, 130, 246), 0.15);
    border-color: var(--tag-color, #3b82f6);
    transform: translateY(-1px);
  }

  .tag-name {
    line-height: 1;
  }

  .tag-count {
    font-size: 0.75rem;
    opacity: 0.7;
  }

  .show-more-btn {
    padding: 0.375rem 0.75rem;
    background: #f1f5f9;
    color: #64748b;
    border: 1px dashed #cbd5e1;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .show-more-btn:hover {
    background: #e2e8f0;
    color: #475569;
  }

  /* 快速导航 */
  .quick-nav {
    background: white;
    border-radius: 0.75rem;
    border: 1px solid #e2e8f0;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    height: fit-content;
    position: sticky;
    top: 2rem;
  }

  .nav-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
  }

  .nav-links {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
  }

  .nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    color: #64748b;
    text-decoration: none;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
    font-size: 0.875rem;
  }

  .nav-link:hover {
    background: #f1f5f9;
    color: #334155;
  }

  .nav-tips {
    border-top: 1px solid #e2e8f0;
    padding-top: 1rem;
  }

  .tips-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.75rem;
  }

  .tips-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .tips-list li {
    font-size: 0.75rem;
    color: #64748b;
    line-height: 1.5;
    margin-bottom: 0.25rem;
    padding-left: 1rem;
    position: relative;
  }

  .tips-list li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: #3b82f6;
  }

  /* 响应式设计 */
  @media (max-width: 1024px) {
    .content-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .quick-nav {
      position: static;
      order: -1;
    }

    .nav-links {
      flex-direction: row;
      flex-wrap: wrap;
    }
  }

  @media (max-width: 768px) {
    .page-header {
      padding: 2rem 0;
    }

    .page-title {
      font-size: 2rem;
    }

    .container {
      padding: 0 0.75rem;
    }

    .category-section {
      padding: 1rem;
    }

    .quick-nav {
      padding: 1rem;
    }
  }
</style>

<script>
  // 标签颜色处理
  document.addEventListener('DOMContentLoaded', () => {
    const categoryTags = document.querySelectorAll('.category-tag[style*="--tag-color"]');
    categoryTags.forEach(tag => {
      const htmlTag = tag as HTMLElement;
      const colorValue = htmlTag.style.getPropertyValue('--tag-color');
      if (colorValue) {
        const hex = colorValue.replace('#', '');
        const r = parseInt(hex.substr(0, 2), 16);
        const g = parseInt(hex.substr(2, 2), 16);
        const b = parseInt(hex.substr(4, 2), 16);
        htmlTag.style.setProperty('--tag-color-rgb', `${r}, ${g}, ${b}`);
      }
    });

    // 显示更多按钮功能
    const showMoreBtns = document.querySelectorAll('.show-more-btn');
    showMoreBtns.forEach(btn => {
      const htmlBtn = btn as HTMLElement;
      htmlBtn.addEventListener('click', () => {
        const category = htmlBtn.dataset.category;
        // 这里可以实现展开更多标签的功能
        console.log(`Show more tags for category: ${category}`);
      });
    });

    // 平滑滚动到锚点
    const navLinks = document.querySelectorAll('.nav-link[href^="#"]');
    navLinks.forEach(link => {
      const htmlLink = link as HTMLAnchorElement;
      htmlLink.addEventListener('click', (e: Event) => {
        e.preventDefault();
        const targetId = htmlLink.getAttribute('href')?.substring(1);
        if (targetId) {
          const targetElement = document.getElementById(targetId);
          if (targetElement) {
            targetElement.scrollIntoView({ behavior: 'smooth' });
          }
        }
      });
    });
  });
</script>
