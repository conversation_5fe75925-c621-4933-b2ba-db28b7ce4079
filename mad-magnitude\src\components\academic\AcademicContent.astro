---
export interface Props {
  showToc?: boolean;
  showProgress?: boolean;
  enableMermaid?: boolean;
  class?: string;
}

const { 
  showToc = true, 
  showProgress = true, 
  enableMermaid = false,
  class: className = ""
} = Astro.props;
---

<div class={`academic-content prose prose-lg max-w-none ${className}`}>
  <!-- 阅读进度指示器 -->
  {showProgress && (
    <div class="reading-progress">
      <div class="progress-bar" id="reading-progress-bar"></div>
    </div>
  )}
  
  <!-- 目录 -->
  {showToc && (
    <div class="table-of-contents" id="table-of-contents">
      <h3>目录</h3>
      <div id="toc-content">
        <!-- 目录内容将通过JavaScript生成 -->
      </div>
    </div>
  )}
  
  <!-- 文章内容 -->
  <div class="content-body">
    <slot />
  </div>
</div>

<!-- 学术样式 -->
<style>
  @import '../../styles/academic.css';
</style>

<script define:vars={{ showToc, showProgress, enableMermaid }}>
  // 学术内容增强脚本
  class AcademicContentEnhancer {
    constructor() {
      this.showToc = showToc;
      this.showProgress = showProgress;
      this.enableMermaid = enableMermaid;
      this.init();
    }

    init() {
      if (this.showToc) {
        this.generateTableOfContents();
      }
      
      if (this.showProgress) {
        this.initReadingProgress();
      }
      
      if (this.enableMermaid) {
        this.initMermaid();
      }
      
      this.enhanceCodeBlocks();
      this.addCopyButtons();
      this.initSmoothScrolling();
    }

    // 生成目录
    generateTableOfContents() {
      const headings = document.querySelectorAll('.content-body h1, .content-body h2, .content-body h3, .content-body h4');
      const tocContent = document.getElementById('toc-content');
      const tocContainer = document.getElementById('table-of-contents');
      
      if (!headings.length || !tocContent) {
        if (tocContainer) {
          tocContainer.style.display = 'none';
        }
        return;
      }

      const tocList = document.createElement('ul');
      tocList.className = 'toc-list';

      headings.forEach((heading, index) => {
        // 为标题添加ID
        if (!heading.id) {
          heading.id = `heading-${index}`;
        }

        const li = document.createElement('li');
        const a = document.createElement('a');
        a.href = `#${heading.id}`;
        a.textContent = heading.textContent;
        a.className = `toc-level-${heading.tagName.toLowerCase()}`;
        
        li.appendChild(a);
        tocList.appendChild(li);
      });

      tocContent.appendChild(tocList);
    }

    // 阅读进度指示器
    initReadingProgress() {
      const progressBar = document.getElementById('reading-progress-bar');
      if (!progressBar) return;

      const updateProgress = () => {
        const scrollTop = window.pageYOffset;
        const docHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / docHeight) * 100;
        progressBar.style.width = `${Math.min(scrollPercent, 100)}%`;
      };

      window.addEventListener('scroll', updateProgress);
      updateProgress(); // 初始化
    }

    // Mermaid 图表支持
    async initMermaid() {
      try {
        const mermaidBlocks = document.querySelectorAll('pre code.language-mermaid');
        if (mermaidBlocks.length === 0) return;

        // 动态导入 Mermaid
        const mermaid = await import('mermaid');
        mermaid.default.initialize({
          startOnLoad: false,
          theme: 'default',
          securityLevel: 'loose',
        });

        mermaidBlocks.forEach((block, index) => {
          const code = block.textContent;
          const container = document.createElement('div');
          container.className = 'mermaid-container';
          container.id = `mermaid-${index}`;
          
          // 替换代码块
          block.parentElement.replaceWith(container);
          
          // 渲染图表
          mermaid.default.render(`mermaid-svg-${index}`, code).then(({ svg }) => {
            container.innerHTML = svg;
          });
        });
      } catch (error) {
        console.warn('Mermaid initialization failed:', error);
      }
    }

    // 增强代码块
    enhanceCodeBlocks() {
      const codeBlocks = document.querySelectorAll('pre code');
      
      codeBlocks.forEach((block) => {
        const pre = block.parentElement;
        if (!pre) return;

        // 添加语言标签
        const className = block.className;
        const languageMatch = className.match(/language-(\w+)/);
        if (languageMatch) {
          pre.setAttribute('data-language', languageMatch[1]);
        }

        // 添加行号（可选）
        if (block.textContent && block.textContent.split('\n').length > 5) {
          this.addLineNumbers(pre, block);
        }
      });
    }

    // 添加行号
    addLineNumbers(pre, code) {
      const lines = code.textContent.split('\n');
      const lineNumbersWrapper = document.createElement('span');
      lineNumbersWrapper.className = 'line-numbers-rows';
      
      lines.forEach((_, index) => {
        const lineNumber = document.createElement('span');
        lineNumber.textContent = (index + 1).toString();
        lineNumbersWrapper.appendChild(lineNumber);
      });
      
      pre.classList.add('line-numbers');
      pre.appendChild(lineNumbersWrapper);
    }

    // 添加复制按钮
    addCopyButtons() {
      const codeBlocks = document.querySelectorAll('pre');
      
      codeBlocks.forEach((pre) => {
        const button = document.createElement('button');
        button.className = 'copy-button absolute top-2 right-2 bg-gray-700 text-white px-2 py-1 rounded text-xs hover:bg-gray-600 transition-colors';
        button.textContent = '复制';
        button.style.position = 'absolute';
        button.style.top = '8px';
        button.style.right = '8px';
        
        button.addEventListener('click', async () => {
          const code = pre.querySelector('code');
          if (code) {
            try {
              await navigator.clipboard.writeText(code.textContent);
              button.textContent = '已复制';
              setTimeout(() => {
                button.textContent = '复制';
              }, 2000);
            } catch (err) {
              console.error('复制失败:', err);
            }
          }
        });
        
        pre.style.position = 'relative';
        pre.appendChild(button);
      });
    }

    // 平滑滚动
    initSmoothScrolling() {
      const links = document.querySelectorAll('a[href^="#"]');
      
      links.forEach(link => {
        link.addEventListener('click', (e) => {
          e.preventDefault();
          const targetId = link.getAttribute('href').substring(1);
          const targetElement = document.getElementById(targetId);
          
          if (targetElement) {
            targetElement.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }
        });
      });
    }
  }

  // 初始化学术内容增强
  document.addEventListener('DOMContentLoaded', () => {
    new AcademicContentEnhancer();
  });
</script>