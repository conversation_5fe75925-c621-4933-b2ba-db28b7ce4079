/// <reference types="astro/client" />

// Astro 环境变量类型声明
interface ImportMetaEnv {
  readonly DEV: boolean;
  readonly PROD: boolean;
  readonly SSR: boolean;
  readonly MODE: string;
  readonly BASE_URL: string;
  readonly SITE?: string;
  // 添加自定义环境变量
  readonly PUBLIC_SITE_URL?: string;
  readonly PUBLIC_ANALYTICS_ID?: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

// 扩展全局类型以支持 Astro
declare global {
  // Astro 组件类型
  namespace astroHTML.JSX {
    interface IntrinsicElements {
      [elemName: string]: any;
    }
  }
}

// 导出空对象以使此文件成为模块
export {};
