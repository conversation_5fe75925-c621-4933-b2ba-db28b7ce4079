---
inclusion: always
---

# Pennfly Private Academy 项目上下文

## 项目概述

这是一个基于 Astro 的学术研究平台，采用静态站点生成器架构，专注于展示跨领域研究成果。项目已达到生产就绪状态 (v1.0.0)。

## 技术栈

- **框架**: Astro 5.x (静态站点生成器)
- **语言**: TypeScript (严格模式)
- **样式**: Tailwind CSS
- **内容**: Astro Content Collections
- **数学公式**: KaTeX
- **图表**: Mermaid
- **代码高亮**: Shiki
- **测试**: Vitest
- **代码质量**: ESLint + Prettier
- **Git Hooks**: Husky

## 项目架构

```
mad-magnitude/src/
├── components/          # 可复用组件
│   ├── admin/          # 管理后台组件
│   ├── common/         # 通用组件
│   ├── forms/          # 表单组件
│   ├── navigation/     # 导航组件
│   └── tags/           # 标签相关组件
├── content/            # 内容集合
│   ├── news/           # 动态资讯
│   ├── logs/           # 研究日志
│   ├── research/       # 研究报告
│   ├── ai/             # AI研究所
│   ├── economics/      # 经济研究所
│   ├── philosophy/     # 哲学研究所
│   ├── internet/       # 互联网研究所
│   ├── future/         # 未来研究所
│   └── products/       # 产品发布
├── layouts/            # 页面布局
├── pages/              # 页面路由
├── utils/              # 工具函数
└── styles/             # 全局样式
```

## 内容管理规范

### 内容类型

- **动态资讯** (news): 研究动态、公告、思考、里程碑
- **研究日志** (logs): 日常研究记录
- **研究报告** (research): 正式研究成果
- **研究所内容**: 各专业领域的深度内容
- **产品发布** (products): 项目和工具发布

### 文件命名规范

- 使用 kebab-case 命名
- 文件名应简洁且描述性强
- 日期格式: YYYY-MM-DD-title

### 内容结构

所有内容都使用 frontmatter + markdown 格式，支持：

- 多语言标题和描述 (zh/en)
- 标签系统
- 发布/更新日期
- 草稿状态
- 特色内容标记
- 作者信息

## 开发规范

### 代码风格

- 使用 TypeScript 严格模式
- 遵循 ESLint 规则
- 使用 Prettier 格式化
- 组件使用 PascalCase
- 文件使用 kebab-case

### 提交规范

- 使用 Conventional Commits
- 通过 commitlint 验证
- 支持的类型: feat, fix, docs, style, refactor, test, chore

### 性能要求

- 构建产物 < 3MB
- 页面加载时间 < 2s
- 图片优化 (WebP/AVIF)
- 代码分割和懒加载

## 当前状态

- **版本**: v1.0.0 (生产就绪)
- **页面数**: 32 个静态页面
- **构建状态**: ✅ 无错误
- **测试状态**: ✅ 通过
- **部署状态**: 🚀 就绪

## 下一版本计划 (v1.1.0)

1. **搜索功能** - 集成 Fuse.js
2. **标签系统** - 统一标签管理
3. **SEO 优化** - 结构化数据
4. **国际化** - 多语言支持
