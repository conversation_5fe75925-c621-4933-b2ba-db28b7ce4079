---
import SimpleNavigation from '../components/navigation/SimpleNavigation.astro';
// 只导入关键CSS，其他样式按需加载
import '../styles/global.css';

export interface Props {
  title?: string;
  description?: string;
  // showAccessibilityTools 已删除
  canonicalUrl?: string;
  ogImage?: string;
  ogType?: 'website' | 'article' | 'profile';
  keywords?: string[];
  author?: string;
  publishDate?: Date;
  updateDate?: Date;
  structuredData?: Record<string, any>;
  noindex?: boolean;
}

const {
  title = 'Pennfly Private Academy',
  description = '个人化私人研究院 · 跨领域学术探索 · 思想与创意的交流平台',
  // showAccessibilityTools 已删除
  canonicalUrl,
  ogImage,
  ogType = 'website',
  keywords = [],
  author = 'Pennfly',
  publishDate,
  updateDate,
  structuredData,
  noindex = false,
} = Astro.props;

// 生成完整的页面 URL
const currentUrl = canonicalUrl || new URL(Astro.url.pathname, Astro.site).toString();
const defaultOgImage = new URL('/images/og-default.jpg', Astro.site).toString();
const pageOgImage = ogImage || defaultOgImage;
---

<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="description" content={description} />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />
    <title>{title}</title>

    <!-- SEO 基础标签 -->
    {keywords.length > 0 && <meta name="keywords" content={keywords.join(', ')} />}
    {author && <meta name="author" content={author} />}
    {noindex && <meta name="robots" content="noindex, nofollow" />}
    {!noindex && <meta name="robots" content="index, follow, max-image-preview:large" />}

    <!-- Canonical URL -->
    {canonicalUrl && <link rel="canonical" href={canonicalUrl} />}

    <!-- Open Graph 标签 -->
    <meta property="og:title" content={title} />
    <meta property="og:description" content={description} />
    <meta property="og:type" content={ogType} />
    <meta property="og:url" content={currentUrl} />
    <meta property="og:image" content={pageOgImage} />
    <meta property="og:site_name" content="Pennfly Private Academy" />
    <meta property="og:locale" content="zh_CN" />

    <!-- Twitter Card 标签 -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content={title} />
    <meta name="twitter:description" content={description} />
    <meta name="twitter:image" content={pageOgImage} />

    <!-- 发布和更新日期 -->
    {publishDate && <meta property="article:published_time" content={publishDate.toISOString()} />}
    {updateDate && <meta property="article:modified_time" content={updateDate.toISOString()} />}
    {author && <meta property="article:author" content={author} />}

    <!-- 结构化数据 -->
    {
      structuredData && (
        <script type="application/ld+json" is:inline set:html={JSON.stringify(structuredData)} />
      )
    }

    <!-- 移动端主题色 -->
    <meta name="theme-color" content="#3b82f6" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />

    <!-- 搜索引擎优化 -->
    <meta
      name="googlebot"
      content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1"
    />
    <meta
      name="bingbot"
      content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1"
    />

    <!-- 额外的 head 内容 -->
    <slot name="head" />

    <!-- 性能优化：DNS 预解析 -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com" />
    <link rel="dns-prefetch" href="//cdn.jsdelivr.net" />

    <!-- 性能优化：预加载关键资源 -->
    <link rel="preload" href="/favicon.svg" as="image" type="image/svg+xml" />

    <!-- CSS优化：异步加载非关键CSS -->
    <link rel="preload" href="/src/styles/academic.css" as="style" onload="this.onload=null;this.rel='stylesheet'" />
    <link rel="preload" href="/src/styles/accessibility.css" as="style" onload="this.onload=null;this.rel='stylesheet'" />
    <link rel="preload" href="/src/styles/performance.css" as="style" onload="this.onload=null;this.rel='stylesheet'" />

    <!-- 关键CSS内联 -->
    <style is:inline>
      /* 关键CSS - 从critical.css内联 */
      *,*::before,*::after{box-sizing:border-box}
      html{font-family:'PingFang SC','Hiragino Sans GB','Microsoft YaHei',system-ui,sans-serif;scroll-behavior:smooth}
      body{margin:0;line-height:1.6;background-color:rgb(var(--color-background-primary));color:rgb(var(--color-foreground-primary));transition:background-color var(--transition-normal),color var(--transition-normal)}
      .container{width:100%;max-width:1280px;margin:0 auto;padding:0 1rem}
      .navbar{position:sticky;top:0;z-index:50;background-color:rgb(var(--color-background-elevated));border-bottom:1px solid rgb(var(--color-border-primary))}
      .main-content{min-height:calc(100vh - 4rem);padding:2rem 0}
      .loading{display:inline-block;width:1rem;height:1rem;border:2px solid rgb(var(--color-border-primary));border-radius:50%;border-top-color:rgb(var(--color-brand-primary));animation:spin 1s ease-in-out infinite}
      @keyframes spin{to{transform:rotate(360deg)}}
    </style>

    <!-- RSS 订阅 -->
    <link
      rel="alternate"
      type="application/rss+xml"
      title="Pennfly Private Academy RSS Feed"
      href="/rss.xml"
    />

    <!-- 内容安全策略 -->
    <meta
      http-equiv="Content-Security-Policy"
      content="
      default-src 'self'; 
      style-src 'self' 'unsafe-inline'; 
      font-src 'self' data:; 
      script-src 'self' 'unsafe-inline' 'unsafe-eval'; 
      img-src 'self' data: https:; 
      connect-src 'self'; 
      object-src 'none'; 
      base-uri 'self'; 
      form-action 'self';
    "
    />

    <!-- 字体样式 -->
    <style>
      body {
        font-family:
          'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei',
          'Helvetica Neue', Arial, sans-serif;
      }
    </style>
  </head>
  <body class="min-h-screen bg-gray-50 text-gray-900">
    <!-- 跳转链接（屏幕阅读器和键盘用户） -->
    <div class="skip-links">
      <a href="#main-content" class="skip-link"> 跳转到主内容 </a>
      <a href="#navigation" class="skip-link"> 跳转到导航 </a>
      <a href="#footer" class="skip-link"> 跳转到页脚 </a>
    </div>

    <!-- 导航栏 -->
    <div id="navigation" role="banner">
      <SimpleNavigation />
    </div>

    <!-- 主要内容 -->
    <main id="main-content" class="container mx-auto max-w-6xl px-4 py-8" role="main" tabindex="-1">
      <slot />
    </main>

    <!-- 页脚 -->
    <footer id="footer" class="mt-12 border-t border-gray-200 bg-gray-100 py-8" role="contentinfo">
      <div class="container mx-auto px-4 text-center text-gray-600">
        <p>&copy; {new Date().getFullYear()} Pennfly Private Academy. 保留所有权利。</p>
      </div>
    </footer>

    <!-- 可访问性工具已删除 -->

    <!-- 性能监控已删除 -->

    <!-- 返回顶部按钮 -->
    <button
      id="back-to-top"
      class="invisible fixed right-6 bottom-6 rounded-full bg-blue-600 p-3 text-white opacity-0 shadow-lg transition-all duration-300 hover:bg-blue-700"
      aria-label="返回页面顶部"
      title="返回顶部"
    >
      <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
      </svg>
    </button>
  </body>

  <style>
    /* 跳转链接样式 */
    .skip-links {
      position: absolute;
      top: -100px;
      left: 0;
      z-index: 1000;
    }

    .skip-link {
      position: absolute;
      top: 0;
      left: 0;
      background: #1f2937;
      color: #fff;
      padding: 8px 16px;
      text-decoration: none;
      border-radius: 0 0 4px 0;
      transform: translateY(-100%);
      transition: transform 0.2s;
      font-weight: 500;
    }

    .skip-link:focus {
      transform: translateY(0);
    }

    .skip-link:hover {
      background: #374151;
    }

    /* 返回顶部按钮 */
    #back-to-top.visible {
      opacity: 1;
      visibility: visible;
    }

    /* 确保焦点可见性 */
    *:focus-visible {
      outline: 2px solid #3b82f6;
      outline-offset: 2px;
    }

    /* 确保所有交互元素都有足够的点击区域 */
    button,
    a,
    input,
    select,
    textarea {
      min-height: 44px;
    }

    /* 改善文本可读性 */
    body {
      line-height: 1.6;
    }

    /* 高对比度模式支持 */
    @media (prefers-contrast: high) {
      body {
        background-color: #ffffff;
        color: #000000;
      }

      .bg-gray-50 {
        background-color: #ffffff !important;
      }

      .text-gray-900 {
        color: #000000 !important;
      }
    }

    /* 减少动画模式支持 */
    @media (prefers-reduced-motion: reduce) {
      *,
      *::before,
      *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
      }
    }
  </style>

  <script>
    // 性能优化功能
    document.addEventListener('DOMContentLoaded', () => {
      // 性能监控已删除

      // 初始化图片懒加载
      initImageLazyLoading();

      // 预加载关键资源
      preloadCriticalResources();
      const backToTopButton = document.getElementById('back-to-top');

      // 显示/隐藏返回顶部按钮
      function toggleBackToTop() {
        if (window.scrollY > 300) {
          backToTopButton?.classList.add('visible');
        } else {
          backToTopButton?.classList.remove('visible');
        }
      }

      // 监听滚动事件
      window.addEventListener('scroll', toggleBackToTop);

      // 点击返回顶部
      backToTopButton?.addEventListener('click', () => {
        window.scrollTo({
          top: 0,
          behavior: 'smooth',
        });
      });

      // 键盘支持：按 Home 键返回顶部
      document.addEventListener('keydown', e => {
        if (e.key === 'Home' && e.ctrlKey) {
          e.preventDefault();
          window.scrollTo({
            top: 0,
            behavior: 'smooth',
          });
        }
      });

      // 跳转链接功能
      document.querySelectorAll('.skip-link').forEach(link => {
        link.addEventListener('click', e => {
          e.preventDefault();
          const targetId = link.getAttribute('href')?.substring(1);
          const target = document.getElementById(targetId || '');
          if (target) {
            target.focus();
            target.scrollIntoView({ behavior: 'smooth' });
          }
        });
      });
    });

    // 性能监控已删除 - 对静态内容网站不必要

    // 图片懒加载
    function initImageLazyLoading() {
      const images = document.querySelectorAll('img[data-src]');

      if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver(
          entries => {
            entries.forEach(entry => {
              if (entry.isIntersecting) {
                const img = entry.target as HTMLImageElement;
                if (img.dataset.src) {
                  img.src = img.dataset.src;
                }
                img.classList.remove('lazy-loading');
                img.classList.add('lazy-loaded');
                imageObserver.unobserve(img);
              }
            });
          },
          {
            rootMargin: '50px',
          }
        );

        images.forEach(img => {
          img.classList.add('lazy-loading');
          imageObserver.observe(img);
        });
      } else {
        // 降级处理
        images.forEach(img => {
          const imgElement = img as HTMLImageElement;
          if (imgElement.dataset.src) {
            imgElement.src = imgElement.dataset.src;
          }
          imgElement.classList.add('lazy-loaded');
        });
      }
    }

    // 预加载关键资源
    function preloadCriticalResources() {
      // 预连接到外部域名
      const preconnectDomains = [
        'https://fonts.googleapis.com',
        'https://fonts.gstatic.com',
        'https://cdn.jsdelivr.net',
      ];

      preconnectDomains.forEach(domain => {
        const link = document.createElement('link');
        link.rel = 'preconnect';
        link.href = domain;
        link.crossOrigin = 'anonymous';
        document.head.appendChild(link);
      });

      // 预加载下一页可能需要的资源
      const currentPath = window.location.pathname;
      if (currentPath === '/') {
        // 首页预加载常用页面
        prefetchPage('/news');
        prefetchPage('/research');
      }
    }

    function prefetchPage(href: string) {
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.href = href;
      document.head.appendChild(link);
    }
  </script>

  <style>
    html,
    body {
      margin: 0;
      width: 100%;
      height: 100%;
    }
  </style>
</html>
