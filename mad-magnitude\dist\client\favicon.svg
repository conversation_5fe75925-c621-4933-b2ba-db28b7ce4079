<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 外圈金色边框 -->
  <circle cx="32" cy="32" r="30" fill="url(#goldGradient)" stroke="#B45309" stroke-width="2"/>
  
  <!-- 内圈深色背景 -->
  <circle cx="32" cy="32" r="26" fill="url(#blueGradient)"/>
  
  <!-- 装饰性内圈 -->
  <circle cx="32" cy="32" r="22" fill="none" stroke="url(#lightGold)" stroke-width="1" opacity="0.6"/>
  
  <!-- PPA 文字 -->
  <text x="32" y="38" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#F59E0B">PPA</text>
  
  <!-- 装饰性元素 - 顶部小圆点 -->
  <circle cx="32" cy="12" r="2" fill="#F59E0B" opacity="0.8"/>
  
  <!-- 装饰性元素 - 底部小线条 -->
  <line x1="26" y1="48" x2="38" y2="48" stroke="#F59E0B" stroke-width="2" stroke-linecap="round" opacity="0.6"/>
  
  <!-- 渐变定义 -->
  <defs>
    <!-- 金色渐变 -->
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FCD34D;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#F59E0B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D97706;stop-opacity:1" />
    </linearGradient>
    
    <!-- 蓝色渐变 -->
    <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E3A8A;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#1E40AF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1D4ED8;stop-opacity:1" />
    </linearGradient>
    
    <!-- 浅金色 -->
    <linearGradient id="lightGold" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FEF3C7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FCD34D;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 128 128">
    <path d="M50.4 78.5a75.1 75.1 0 0 0-28.5 6.9l24.2-65.7c.7-2 1.9-3.2 3.4-3.2h29c1.5 0 2.7 1.2 3.4 3.2l24.2 65.7s-11.6-7-28.5-7L67 45.5c-.4-1.7-1.6-2.8-2.9-2.8-1.3 0-2.5 1.1-2.9 2.7L50.4 78.5Zm-1.1 28.2Zm-4.2-20.2c-2 6.6-.6 15.8 4.2 20.2a17.5 17.5 0 0 1 .2-.7 5.5 5.5 0 0 1 5.7-4.5c2.8.1 4.3 1.5 4.7 4.7.2 1.1.2 2.3.2 3.5v.4c0 2.7.7 5.2 2.2 7.4a13 13 0 0 0 5.7 4.9v-.3l-.2-.3c-1.8-5.6-.5-9.5 4.4-12.8l1.5-1a73 73 0 0 0 3.2-2.2 16 16 0 0 0 6.8-11.4c.3-2 .1-4-.6-6l-.8.6-1.6 1a37 37 0 0 1-22.4 2.7c-5-.7-9.7-2-13.2-6.2Z" />
    <style>
        path { fill: #000; }
        @media (prefers-color-scheme: dark) {
            path { fill: #FFF; }
        }
    </style>
</svg>
<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 外圈金色边框 -->
  <circle cx="32" cy="32" r="30" fill="url(#goldGradient)" stroke="#B45309" stroke-width="2"/>
  
  <!-- 内圈深色背景 -->
  <circle cx="32" cy="32" r="26" fill="url(#blueGradient)"/>
  
  <!-- 装饰性内圈 -->
  <circle cx="32" cy="32" r="22" fill="none" stroke="url(#lightGold)" stroke-width="1" opacity="0.6"/>
  
  <!-- PPA 文字 -->
  <text x="32" y="38" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#F59E0B">PPA</text>
  
  <!-- 装饰性元素 - 顶部小圆点 -->
  <circle cx="32" cy="12" r="2" fill="#F59E0B" opacity="0.8"/>
  
  <!-- 装饰性元素 - 底部小线条 -->
  <line x1="26" y1="48" x2="38" y2="48" stroke="#F59E0B" stroke-width="2" stroke-linecap="round" opacity="0.6"/>
  
  <!-- 渐变定义 -->
  <defs>
    <!-- 金色渐变 -->
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FCD34D;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#F59E0B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D97706;stop-opacity:1" />
    </linearGradient>
    
    <!-- 蓝色渐变 -->
    <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E3A8A;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#1E40AF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1D4ED8;stop-opacity:1" />
    </linearGradient>
    
    <!-- 浅金色 -->
    <linearGradient id="lightGold" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FEF3C7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FCD34D;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 128 128">
    <path d="M50.4 78.5a75.1 75.1 0 0 0-28.5 6.9l24.2-65.7c.7-2 1.9-3.2 3.4-3.2h29c1.5 0 2.7 1.2 3.4 3.2l24.2 65.7s-11.6-7-28.5-7L67 45.5c-.4-1.7-1.6-2.8-2.9-2.8-1.3 0-2.5 1.1-2.9 2.7L50.4 78.5Zm-1.1 28.2Zm-4.2-20.2c-2 6.6-.6 15.8 4.2 20.2a17.5 17.5 0 0 1 .2-.7 5.5 5.5 0 0 1 5.7-4.5c2.8.1 4.3 1.5 4.7 4.7.2 1.1.2 2.3.2 3.5v.4c0 2.7.7 5.2 2.2 7.4a13 13 0 0 0 5.7 4.9v-.3l-.2-.3c-1.8-5.6-.5-9.5 4.4-12.8l1.5-1a73 73 0 0 0 3.2-2.2 16 16 0 0 0 6.8-11.4c.3-2 .1-4-.6-6l-.8.6-1.6 1a37 37 0 0 1-22.4 2.7c-5-.7-9.7-2-13.2-6.2Z" />
    <style>
        path { fill: #000; }
        @media (prefers-color-scheme: dark) {
            path { fill: #FFF; }
        }
    </style>
</svg>
<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 外圈金色边框 -->
  <circle cx="32" cy="32" r="30" fill="url(#goldGradient)" stroke="#B45309" stroke-width="2"/>
  
  <!-- 内圈深色背景 -->
  <circle cx="32" cy="32" r="26" fill="url(#blueGradient)"/>
  
  <!-- 装饰性内圈 -->
  <circle cx="32" cy="32" r="22" fill="none" stroke="url(#lightGold)" stroke-width="1" opacity="0.6"/>
  
  <!-- PPA 文字 -->
  <text x="32" y="38" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#F59E0B">PPA</text>
  
  <!-- 装饰性元素 - 顶部小圆点 -->
  <circle cx="32" cy="12" r="2" fill="#F59E0B" opacity="0.8"/>
  
  <!-- 装饰性元素 - 底部小线条 -->
  <line x1="26" y1="48" x2="38" y2="48" stroke="#F59E0B" stroke-width="2" stroke-linecap="round" opacity="0.6"/>
  
  <!-- 渐变定义 -->
  <defs>
    <!-- 金色渐变 -->
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FCD34D;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#F59E0B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D97706;stop-opacity:1" />
    </linearGradient>
    
    <!-- 蓝色渐变 -->
    <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E3A8A;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#1E40AF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1D4ED8;stop-opacity:1" />
    </linearGradient>
    
    <!-- 浅金色 -->
    <linearGradient id="lightGold" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FEF3C7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FCD34D;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 128 128">
    <path d="M50.4 78.5a75.1 75.1 0 0 0-28.5 6.9l24.2-65.7c.7-2 1.9-3.2 3.4-3.2h29c1.5 0 2.7 1.2 3.4 3.2l24.2 65.7s-11.6-7-28.5-7L67 45.5c-.4-1.7-1.6-2.8-2.9-2.8-1.3 0-2.5 1.1-2.9 2.7L50.4 78.5Zm-1.1 28.2Zm-4.2-20.2c-2 6.6-.6 15.8 4.2 20.2a17.5 17.5 0 0 1 .2-.7 5.5 5.5 0 0 1 5.7-4.5c2.8.1 4.3 1.5 4.7 4.7.2 1.1.2 2.3.2 3.5v.4c0 2.7.7 5.2 2.2 7.4a13 13 0 0 0 5.7 4.9v-.3l-.2-.3c-1.8-5.6-.5-9.5 4.4-12.8l1.5-1a73 73 0 0 0 3.2-2.2 16 16 0 0 0 6.8-11.4c.3-2 .1-4-.6-6l-.8.6-1.6 1a37 37 0 0 1-22.4 2.7c-5-.7-9.7-2-13.2-6.2Z" />
    <style>
        path { fill: #000; }
        @media (prefers-color-scheme: dark) {
            path { fill: #FFF; }
        }
    </style>
</svg>
<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 外圈金色边框 -->
  <circle cx="32" cy="32" r="30" fill="url(#goldGradient)" stroke="#B45309" stroke-width="2"/>
  
  <!-- 内圈深色背景 -->
  <circle cx="32" cy="32" r="26" fill="url(#blueGradient)"/>
  
  <!-- 装饰性内圈 -->
  <circle cx="32" cy="32" r="22" fill="none" stroke="url(#lightGold)" stroke-width="1" opacity="0.6"/>
  
  <!-- PPA 文字 -->
  <text x="32" y="38" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#F59E0B">PPA</text>
  
  <!-- 装饰性元素 - 顶部小圆点 -->
  <circle cx="32" cy="12" r="2" fill="#F59E0B" opacity="0.8"/>
  
  <!-- 装饰性元素 - 底部小线条 -->
  <line x1="26" y1="48" x2="38" y2="48" stroke="#F59E0B" stroke-width="2" stroke-linecap="round" opacity="0.6"/>
  
  <!-- 渐变定义 -->
  <defs>
    <!-- 金色渐变 -->
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FCD34D;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#F59E0B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D97706;stop-opacity:1" />
    </linearGradient>
    
    <!-- 蓝色渐变 -->
    <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E3A8A;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#1E40AF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1D4ED8;stop-opacity:1" />
    </linearGradient>
    
    <!-- 浅金色 -->
    <linearGradient id="lightGold" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FEF3C7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FCD34D;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 128 128">
    <path d="M50.4 78.5a75.1 75.1 0 0 0-28.5 6.9l24.2-65.7c.7-2 1.9-3.2 3.4-3.2h29c1.5 0 2.7 1.2 3.4 3.2l24.2 65.7s-11.6-7-28.5-7L67 45.5c-.4-1.7-1.6-2.8-2.9-2.8-1.3 0-2.5 1.1-2.9 2.7L50.4 78.5Zm-1.1 28.2Zm-4.2-20.2c-2 6.6-.6 15.8 4.2 20.2a17.5 17.5 0 0 1 .2-.7 5.5 5.5 0 0 1 5.7-4.5c2.8.1 4.3 1.5 4.7 4.7.2 1.1.2 2.3.2 3.5v.4c0 2.7.7 5.2 2.2 7.4a13 13 0 0 0 5.7 4.9v-.3l-.2-.3c-1.8-5.6-.5-9.5 4.4-12.8l1.5-1a73 73 0 0 0 3.2-2.2 16 16 0 0 0 6.8-11.4c.3-2 .1-4-.6-6l-.8.6-1.6 1a37 37 0 0 1-22.4 2.7c-5-.7-9.7-2-13.2-6.2Z" />
    <style>
        path { fill: #000; }
        @media (prefers-color-scheme: dark) {
            path { fill: #FFF; }
        }
    </style>
</svg>
<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 外圈金色边框 -->
  <circle cx="32" cy="32" r="30" fill="url(#goldGradient)" stroke="#B45309" stroke-width="2"/>
  
  <!-- 内圈深色背景 -->
  <circle cx="32" cy="32" r="26" fill="url(#blueGradient)"/>
  
  <!-- 装饰性内圈 -->
  <circle cx="32" cy="32" r="22" fill="none" stroke="url(#lightGold)" stroke-width="1" opacity="0.6"/>
  
  <!-- PPA 文字 -->
  <text x="32" y="38" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#F59E0B">PPA</text>
  
  <!-- 装饰性元素 - 顶部小圆点 -->
  <circle cx="32" cy="12" r="2" fill="#F59E0B" opacity="0.8"/>
  
  <!-- 装饰性元素 - 底部小线条 -->
  <line x1="26" y1="48" x2="38" y2="48" stroke="#F59E0B" stroke-width="2" stroke-linecap="round" opacity="0.6"/>
  
  <!-- 渐变定义 -->
  <defs>
    <!-- 金色渐变 -->
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FCD34D;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#F59E0B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D97706;stop-opacity:1" />
    </linearGradient>
    
    <!-- 蓝色渐变 -->
    <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E3A8A;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#1E40AF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1D4ED8;stop-opacity:1" />
    </linearGradient>
    
    <!-- 浅金色 -->
    <linearGradient id="lightGold" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FEF3C7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FCD34D;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 128 128">
    <path d="M50.4 78.5a75.1 75.1 0 0 0-28.5 6.9l24.2-65.7c.7-2 1.9-3.2 3.4-3.2h29c1.5 0 2.7 1.2 3.4 3.2l24.2 65.7s-11.6-7-28.5-7L67 45.5c-.4-1.7-1.6-2.8-2.9-2.8-1.3 0-2.5 1.1-2.9 2.7L50.4 78.5Zm-1.1 28.2Zm-4.2-20.2c-2 6.6-.6 15.8 4.2 20.2a17.5 17.5 0 0 1 .2-.7 5.5 5.5 0 0 1 5.7-4.5c2.8.1 4.3 1.5 4.7 4.7.2 1.1.2 2.3.2 3.5v.4c0 2.7.7 5.2 2.2 7.4a13 13 0 0 0 5.7 4.9v-.3l-.2-.3c-1.8-5.6-.5-9.5 4.4-12.8l1.5-1a73 73 0 0 0 3.2-2.2 16 16 0 0 0 6.8-11.4c.3-2 .1-4-.6-6l-.8.6-1.6 1a37 37 0 0 1-22.4 2.7c-5-.7-9.7-2-13.2-6.2Z" />
    <style>
        path { fill: #000; }
        @media (prefers-color-scheme: dark) {
            path { fill: #FFF; }
        }
    </style>
</svg>
<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 外圈金色边框 -->
  <circle cx="32" cy="32" r="30" fill="url(#goldGradient)" stroke="#B45309" stroke-width="2"/>
  
  <!-- 内圈深色背景 -->
  <circle cx="32" cy="32" r="26" fill="url(#blueGradient)"/>
  
  <!-- 装饰性内圈 -->
  <circle cx="32" cy="32" r="22" fill="none" stroke="url(#lightGold)" stroke-width="1" opacity="0.6"/>
  
  <!-- PPA 文字 -->
  <text x="32" y="38" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#F59E0B">PPA</text>
  
  <!-- 装饰性元素 - 顶部小圆点 -->
  <circle cx="32" cy="12" r="2" fill="#F59E0B" opacity="0.8"/>
  
  <!-- 装饰性元素 - 底部小线条 -->
  <line x1="26" y1="48" x2="38" y2="48" stroke="#F59E0B" stroke-width="2" stroke-linecap="round" opacity="0.6"/>
  
  <!-- 渐变定义 -->
  <defs>
    <!-- 金色渐变 -->
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FCD34D;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#F59E0B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D97706;stop-opacity:1" />
    </linearGradient>
    
    <!-- 蓝色渐变 -->
    <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E3A8A;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#1E40AF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1D4ED8;stop-opacity:1" />
    </linearGradient>
    
    <!-- 浅金色 -->
    <linearGradient id="lightGold" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FEF3C7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FCD34D;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 128 128">
    <path d="M50.4 78.5a75.1 75.1 0 0 0-28.5 6.9l24.2-65.7c.7-2 1.9-3.2 3.4-3.2h29c1.5 0 2.7 1.2 3.4 3.2l24.2 65.7s-11.6-7-28.5-7L67 45.5c-.4-1.7-1.6-2.8-2.9-2.8-1.3 0-2.5 1.1-2.9 2.7L50.4 78.5Zm-1.1 28.2Zm-4.2-20.2c-2 6.6-.6 15.8 4.2 20.2a17.5 17.5 0 0 1 .2-.7 5.5 5.5 0 0 1 5.7-4.5c2.8.1 4.3 1.5 4.7 4.7.2 1.1.2 2.3.2 3.5v.4c0 2.7.7 5.2 2.2 7.4a13 13 0 0 0 5.7 4.9v-.3l-.2-.3c-1.8-5.6-.5-9.5 4.4-12.8l1.5-1a73 73 0 0 0 3.2-2.2 16 16 0 0 0 6.8-11.4c.3-2 .1-4-.6-6l-.8.6-1.6 1a37 37 0 0 1-22.4 2.7c-5-.7-9.7-2-13.2-6.2Z" />
    <style>
        path { fill: #000; }
        @media (prefers-color-scheme: dark) {
            path { fill: #FFF; }
        }
    </style>
</svg>
<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 外圈金色边框 -->
  <circle cx="32" cy="32" r="30" fill="url(#goldGradient)" stroke="#B45309" stroke-width="2"/>
  
  <!-- 内圈深色背景 -->
  <circle cx="32" cy="32" r="26" fill="url(#blueGradient)"/>
  
  <!-- 装饰性内圈 -->
  <circle cx="32" cy="32" r="22" fill="none" stroke="url(#lightGold)" stroke-width="1" opacity="0.6"/>
  
  <!-- PPA 文字 -->
  <text x="32" y="38" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#F59E0B">PPA</text>
  
  <!-- 装饰性元素 - 顶部小圆点 -->
  <circle cx="32" cy="12" r="2" fill="#F59E0B" opacity="0.8"/>
  
  <!-- 装饰性元素 - 底部小线条 -->
  <line x1="26" y1="48" x2="38" y2="48" stroke="#F59E0B" stroke-width="2" stroke-linecap="round" opacity="0.6"/>
  
  <!-- 渐变定义 -->
  <defs>
    <!-- 金色渐变 -->
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FCD34D;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#F59E0B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D97706;stop-opacity:1" />
    </linearGradient>
    
    <!-- 蓝色渐变 -->
    <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E3A8A;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#1E40AF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1D4ED8;stop-opacity:1" />
    </linearGradient>
    
    <!-- 浅金色 -->
    <linearGradient id="lightGold" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FEF3C7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FCD34D;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 128 128">
    <path d="M50.4 78.5a75.1 75.1 0 0 0-28.5 6.9l24.2-65.7c.7-2 1.9-3.2 3.4-3.2h29c1.5 0 2.7 1.2 3.4 3.2l24.2 65.7s-11.6-7-28.5-7L67 45.5c-.4-1.7-1.6-2.8-2.9-2.8-1.3 0-2.5 1.1-2.9 2.7L50.4 78.5Zm-1.1 28.2Zm-4.2-20.2c-2 6.6-.6 15.8 4.2 20.2a17.5 17.5 0 0 1 .2-.7 5.5 5.5 0 0 1 5.7-4.5c2.8.1 4.3 1.5 4.7 4.7.2 1.1.2 2.3.2 3.5v.4c0 2.7.7 5.2 2.2 7.4a13 13 0 0 0 5.7 4.9v-.3l-.2-.3c-1.8-5.6-.5-9.5 4.4-12.8l1.5-1a73 73 0 0 0 3.2-2.2 16 16 0 0 0 6.8-11.4c.3-2 .1-4-.6-6l-.8.6-1.6 1a37 37 0 0 1-22.4 2.7c-5-.7-9.7-2-13.2-6.2Z" />
    <style>
        path { fill: #000; }
        @media (prefers-color-scheme: dark) {
            path { fill: #FFF; }
        }
    </style>
</svg>
<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 外圈金色边框 -->
  <circle cx="32" cy="32" r="30" fill="url(#goldGradient)" stroke="#B45309" stroke-width="2"/>
  
  <!-- 内圈深色背景 -->
  <circle cx="32" cy="32" r="26" fill="url(#blueGradient)"/>
  
  <!-- 装饰性内圈 -->
  <circle cx="32" cy="32" r="22" fill="none" stroke="url(#lightGold)" stroke-width="1" opacity="0.6"/>
  
  <!-- PPA 文字 -->
  <text x="32" y="38" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#F59E0B">PPA</text>
  
  <!-- 装饰性元素 - 顶部小圆点 -->
  <circle cx="32" cy="12" r="2" fill="#F59E0B" opacity="0.8"/>
  
  <!-- 装饰性元素 - 底部小线条 -->
  <line x1="26" y1="48" x2="38" y2="48" stroke="#F59E0B" stroke-width="2" stroke-linecap="round" opacity="0.6"/>
  
  <!-- 渐变定义 -->
  <defs>
    <!-- 金色渐变 -->
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FCD34D;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#F59E0B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D97706;stop-opacity:1" />
    </linearGradient>
    
    <!-- 蓝色渐变 -->
    <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E3A8A;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#1E40AF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1D4ED8;stop-opacity:1" />
    </linearGradient>
    
    <!-- 浅金色 -->
    <linearGradient id="lightGold" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FEF3C7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FCD34D;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 128 128">
    <path d="M50.4 78.5a75.1 75.1 0 0 0-28.5 6.9l24.2-65.7c.7-2 1.9-3.2 3.4-3.2h29c1.5 0 2.7 1.2 3.4 3.2l24.2 65.7s-11.6-7-28.5-7L67 45.5c-.4-1.7-1.6-2.8-2.9-2.8-1.3 0-2.5 1.1-2.9 2.7L50.4 78.5Zm-1.1 28.2Zm-4.2-20.2c-2 6.6-.6 15.8 4.2 20.2a17.5 17.5 0 0 1 .2-.7 5.5 5.5 0 0 1 5.7-4.5c2.8.1 4.3 1.5 4.7 4.7.2 1.1.2 2.3.2 3.5v.4c0 2.7.7 5.2 2.2 7.4a13 13 0 0 0 5.7 4.9v-.3l-.2-.3c-1.8-5.6-.5-9.5 4.4-12.8l1.5-1a73 73 0 0 0 3.2-2.2 16 16 0 0 0 6.8-11.4c.3-2 .1-4-.6-6l-.8.6-1.6 1a37 37 0 0 1-22.4 2.7c-5-.7-9.7-2-13.2-6.2Z" />
    <style>
        path { fill: #000; }
        @media (prefers-color-scheme: dark) {
            path { fill: #FFF; }
        }
    </style>
</svg>
<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 外圈金色边框 -->
  <circle cx="32" cy="32" r="30" fill="url(#goldGradient)" stroke="#B45309" stroke-width="2"/>
  
  <!-- 内圈深色背景 -->
  <circle cx="32" cy="32" r="26" fill="url(#blueGradient)"/>
  
  <!-- 装饰性内圈 -->
  <circle cx="32" cy="32" r="22" fill="none" stroke="url(#lightGold)" stroke-width="1" opacity="0.6"/>
  
  <!-- PPA 文字 -->
  <text x="32" y="38" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#F59E0B">PPA</text>
  
  <!-- 装饰性元素 - 顶部小圆点 -->
  <circle cx="32" cy="12" r="2" fill="#F59E0B" opacity="0.8"/>
  
  <!-- 装饰性元素 - 底部小线条 -->
  <line x1="26" y1="48" x2="38" y2="48" stroke="#F59E0B" stroke-width="2" stroke-linecap="round" opacity="0.6"/>
  
  <!-- 渐变定义 -->
  <defs>
    <!-- 金色渐变 -->
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FCD34D;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#F59E0B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D97706;stop-opacity:1" />
    </linearGradient>
    
    <!-- 蓝色渐变 -->
    <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E3A8A;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#1E40AF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1D4ED8;stop-opacity:1" />
    </linearGradient>
    
    <!-- 浅金色 -->
    <linearGradient id="lightGold" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FEF3C7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FCD34D;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 128 128">
    <path d="M50.4 78.5a75.1 75.1 0 0 0-28.5 6.9l24.2-65.7c.7-2 1.9-3.2 3.4-3.2h29c1.5 0 2.7 1.2 3.4 3.2l24.2 65.7s-11.6-7-28.5-7L67 45.5c-.4-1.7-1.6-2.8-2.9-2.8-1.3 0-2.5 1.1-2.9 2.7L50.4 78.5Zm-1.1 28.2Zm-4.2-20.2c-2 6.6-.6 15.8 4.2 20.2a17.5 17.5 0 0 1 .2-.7 5.5 5.5 0 0 1 5.7-4.5c2.8.1 4.3 1.5 4.7 4.7.2 1.1.2 2.3.2 3.5v.4c0 2.7.7 5.2 2.2 7.4a13 13 0 0 0 5.7 4.9v-.3l-.2-.3c-1.8-5.6-.5-9.5 4.4-12.8l1.5-1a73 73 0 0 0 3.2-2.2 16 16 0 0 0 6.8-11.4c.3-2 .1-4-.6-6l-.8.6-1.6 1a37 37 0 0 1-22.4 2.7c-5-.7-9.7-2-13.2-6.2Z" />
    <style>
        path { fill: #000; }
        @media (prefers-color-scheme: dark) {
            path { fill: #FFF; }
        }
    </style>
</svg>
<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
    <!-- 外圈金色边框 -->
    <circle cx="32" cy="32" r="30" fill="url(#goldGradient)" stroke="#B45309" stroke-width="2"/>
    
    <!-- 内圈深色背景 -->
    <circle cx="32" cy="32" r="26" fill="url(#blueGradient)"/>
    
    <!-- 装饰性内圈 -->
    <circle cx="32" cy="32" r="22" fill="none" stroke="url(#lightGold)" stroke-width="1" opacity="0.6"/>
    
    <!-- PPA 文字 -->
    <text x="32" y="38" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#F59E0B">PPA</text>
    
    <!-- 装饰性元素 - 顶部小圆点 -->
    <circle cx="32" cy="12" r="2" fill="#F59E0B" opacity="0.8"/>
    
    <!-- 装饰性元素 - 底部小线条 -->
    <line x1="26" y1="48" x2="38" y2="48" stroke="#F59E0B" stroke-width="2" stroke-linecap="round" opacity="0.6"/>
    
    <!-- 渐变定义 -->
    <defs>
      <!-- 金色渐变 -->
      <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#FCD34D;stop-opacity:1" />
        <stop offset="50%" style="stop-color:#F59E0B;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#D97706;stop-opacity:1" />
      </linearGradient>
      
      <!-- 蓝色渐变 -->
      <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#1E3A8A;stop-opacity:1" />
        <stop offset="50%" style="stop-color:#1E40AF;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#1D4ED8;stop-opacity:1" />
      </linearGradient>
      
      <!-- 浅金色 -->
      <linearGradient id="lightGold" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#FEF3C7;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#FCD34D;stop-opacity:1" />
      </linearGradient>
    </defs>
  </svg>
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 128 128">
      <path d="M50.4 78.5a75.1 75.1 0 0 0-28.5 6.9l24.2-65.7c.7-2 1.9-3.2 3.4-3.2h29c1.5 0 2.7 1.2 3.4 3.2l24.2 65.7s-11.6-7-28.5-7L67 45.5c-.4-1.7-1.6-2.8-2.9-2.8-1.3 0-2.5 1.1-2.9 2.7L50.4 78.5Zm-1.1 28.2Zm-4.2-20.2c-2 6.6-.6 15.8 4.2 20.2a17.5 17.5 0 0 1 .2-.7 5.5 5.5 0 0 1 5.7-4.5c2.8.1 4.3 1.5 4.7 4.7.2 1.1.2 2.3.2 3.5v.4c0 2.7.7 5.2 2.2 7.4a13 13 0 0 0 5.7 4.9v-.3l-.2-.3c-1.8-5.6-.5-9.5 4.4-12.8l1.5-1a73 73 0 0 0 3.2-2.2 16 16 0 0 0 6.8-11.4c.3-2 .1-4-.6-6l-.8.6-1.6 1a37 37 0 0 1-22.4 2.7c-5-.7-9.7-2-13.2-6.2Z" />
      <style>
          path { fill: #000; }
          @media (prefers-color-scheme: dark) {
              path { fill: #FFF; }
          }
      </style>
  </svg>
  <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
    <!-- 外圈金色边框 -->
    <circle cx="32" cy="32" r="30" fill="url(#goldGradient)" stroke="#B45309" stroke-width="2"/>
    
    <!-- 内圈深色背景 -->
    <circle cx="32" cy="32" r="26" fill="url(#blueGradient)"/>
    
    <!-- 装饰性内圈 -->
    <circle cx="32" cy="32" r="22" fill="none" stroke="url(#lightGold)" stroke-width="1" opacity="0.6"/>
    
    <!-- PPA 文字 -->
    <text x="32" y="38" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#F59E0B">PPA</text>
    
    <!-- 装饰性元素 - 顶部小圆点 -->
    <circle cx="32" cy="12" r="2" fill="#F59E0B" opacity="0.8"/>
    
    <!-- 装饰性元素 - 底部小线条 -->
    <line x1="26" y1="48" x2="38" y2="48" stroke="#F59E0B" stroke-width="2" stroke-linecap="round" opacity="0.6"/>
    
    <!-- 渐变定义 -->
    <defs>
      <!-- 金色渐变 -->
      <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#FCD34D;stop-opacity:1" />
        <stop offset="50%" style="stop-color:#F59E0B;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#D97706;stop-opacity:1" />
      </linearGradient>
      
      <!-- 蓝色渐变 -->
      <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#1E3A8A;stop-opacity:1" />
        <stop offset="50%" style="stop-color:#1E40AF;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#1D4ED8;stop-opacity:1" />
      </linearGradient>
      
      <!-- 浅金色 -->
      <linearGradient id="lightGold" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#FEF3C7;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#FCD34D;stop-opacity:1" />
      </linearGradient>
    </defs>
  </svg>
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 128 128">
      <path d="M50.4 78.5a75.1 75.1 0 0 0-28.5 6.9l24.2-65.7c.7-2 1.9-3.2 3.4-3.2h29c1.5 0 2.7 1.2 3.4 3.2l24.2 65.7s-11.6-7-28.5-7L67 45.5c-.4-1.7-1.6-2.8-2.9-2.8-1.3 0-2.5 1.1-2.9 2.7L50.4 78.5Zm-1.1 28.2Zm-4.2-20.2c-2 6.6-.6 15.8 4.2 20.2a17.5 17.5 0 0 1 .2-.7 5.5 5.5 0 0 1 5.7-4.5c2.8.1 4.3 1.5 4.7 4.7.2 1.1.2 2.3.2 3.5v.4c0 2.7.7 5.2 2.2 7.4a13 13 0 0 0 5.7 4.9v-.3l-.2-.3c-1.8-5.6-.5-9.5 4.4-12.8l1.5-1a73 73 0 0 0 3.2-2.2 16 16 0 0 0 6.8-11.4c.3-2 .1-4-.6-6l-.8.6-1.6 1a37 37 0 0 1-22.4 2.7c-5-.7-9.7-2-13.2-6.2Z" />
      <style>
          path { fill: #000; }
          @media (prefers-color-scheme: dark) {
              path { fill: #FFF; }
          }
      </style>
  </svg>
  <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
    <!-- 外圈金色边框 -->
    <circle cx="32" cy="32" r="30" fill="url(#goldGradient)" stroke="#B45309" stroke-width="2"/>
    
    <!-- 内圈深色背景 -->
    <circle cx="32" cy="32" r="26" fill="url(#blueGradient)"/>
    
    <!-- 装饰性内圈 -->
    <circle cx="32" cy="32" r="22" fill="none" stroke="url(#lightGold)" stroke-width="1" opacity="0.6"/>
    
    <!-- PPA 文字 -->
    <text x="32" y="38" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#F59E0B">PPA</text>
    
    <!-- 装饰性元素 - 顶部小圆点 -->
    <circle cx="32" cy="12" r="2" fill="#F59E0B" opacity="0.8"/>
    
    <!-- 装饰性元素 - 底部小线条 -->
    <line x1="26" y1="48" x2="38" y2="48" stroke="#F59E0B" stroke-width="2" stroke-linecap="round" opacity="0.6"/>
    
    <!-- 渐变定义 -->
    <defs>
      <!-- 金色渐变 -->
      <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#FCD34D;stop-opacity:1" />
        <stop offset="50%" style="stop-color:#F59E0B;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#D97706;stop-opacity:1" />
      </linearGradient>
      
      <!-- 蓝色渐变 -->
      <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#1E3A8A;stop-opacity:1" />
        <stop offset="50%" style="stop-color:#1E40AF;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#1D4ED8;stop-opacity:1" />
      </linearGradient>
      
      <!-- 浅金色 -->
      <linearGradient id="lightGold" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#FEF3C7;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#FCD34D;stop-opacity:1" />
      </linearGradient>
    </defs>
  </svg>
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 128 128">
      <path d="M50.4 78.5a75.1 75.1 0 0 0-28.5 6.9l24.2-65.7c.7-2 1.9-3.2 3.4-3.2h29c1.5 0 2.7 1.2 3.4 3.2l24.2 65.7s-11.6-7-28.5-7L67 45.5c-.4-1.7-1.6-2.8-2.9-2.8-1.3 0-2.5 1.1-2.9 2.7L50.4 78.5Zm-1.1 28.2Zm-4.2-20.2c-2 6.6-.6 15.8 4.2 20.2a17.5 17.5 0 0 1 .2-.7 5.5 5.5 0 0 1 5.7-4.5c2.8.1 4.3 1.5 4.7 4.7.2 1.1.2 2.3.2 3.5v.4c0 2.7.7 5.2 2.2 7.4a13 13 0 0 0 5.7 4.9v-.3l-.2-.3c-1.8-5.6-.5-9.5 4.4-12.8l1.5-1a73 73 0 0 0 3.2-2.2 16 16 0 0 0 6.8-11.4c.3-2 .1-4-.6-6l-.8.6-1.6 1a37 37 0 0 1-22.4 2.7c-5-.7-9.7-2-13.2-6.2Z" />
      <style>
          path { fill: #000; }
          @media (prefers-color-scheme: dark) {
              path { fill: #FFF; }
          }
      </style>
  </svg>
  <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
    <!-- 外圈金色边框 -->
    <circle cx="32" cy="32" r="30" fill="url(#goldGradient)" stroke="#B45309" stroke-width="2"/>
    
    <!-- 内圈深色背景 -->
    <circle cx="32" cy="32" r="26" fill="url(#blueGradient)"/>
    
    <!-- 装饰性内圈 -->
    <circle cx="32" cy="32" r="22" fill="none" stroke="url(#lightGold)" stroke-width="1" opacity="0.6"/>
    
    <!-- PPA 文字 -->
    <text x="32" y="38" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#F59E0B">PPA</text>
    
    <!-- 装饰性元素 - 顶部小圆点 -->
    <circle cx="32" cy="12" r="2" fill="#F59E0B" opacity="0.8"/>
    
    <!-- 装饰性元素 - 底部小线条 -->
    <line x1="26" y1="48" x2="38" y2="48" stroke="#F59E0B" stroke-width="2" stroke-linecap="round" opacity="0.6"/>
    
    <!-- 渐变定义 -->
    <defs>
      <!-- 金色渐变 -->
      <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#FCD34D;stop-opacity:1" />
        <stop offset="50%" style="stop-color:#F59E0B;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#D97706;stop-opacity:1" />
      </linearGradient>
      
      <!-- 蓝色渐变 -->
      <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#1E3A8A;stop-opacity:1" />
        <stop offset="50%" style="stop-color:#1E40AF;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#1D4ED8;stop-opacity:1" />
      </linearGradient>
      
      <!-- 浅金色 -->
      <linearGradient id="lightGold" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#FEF3C7;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#FCD34D;stop-opacity:1" />
      </linearGradient>
    </defs>
  </svg>
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 128 128">
      <path d="M50.4 78.5a75.1 75.1 0 0 0-28.5 6.9l24.2-65.7c.7-2 1.9-3.2 3.4-3.2h29c1.5 0 2.7 1.2 3.4 3.2l24.2 65.7s-11.6-7-28.5-7L67 45.5c-.4-1.7-1.6-2.8-2.9-2.8-1.3 0-2.5 1.1-2.9 2.7L50.4 78.5Zm-1.1 28.2Zm-4.2-20.2c-2 6.6-.6 15.8 4.2 20.2a17.5 17.5 0 0 1 .2-.7 5.5 5.5 0 0 1 5.7-4.5c2.8.1 4.3 1.5 4.7 4.7.2 1.1.2 2.3.2 3.5v.4c0 2.7.7 5.2 2.2 7.4a13 13 0 0 0 5.7 4.9v-.3l-.2-.3c-1.8-5.6-.5-9.5 4.4-12.8l1.5-1a73 73 0 0 0 3.2-2.2 16 16 0 0 0 6.8-11.4c.3-2 .1-4-.6-6l-.8.6-1.6 1a37 37 0 0 1-22.4 2.7c-5-.7-9.7-2-13.2-6.2Z" />
      <style>
          path { fill: #000; }
          @media (prefers-color-scheme: dark) {
              path { fill: #FFF; }
          }
      </style>
  </svg>
  <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
    <!-- 外圈金色边框 -->
    <circle cx="32
