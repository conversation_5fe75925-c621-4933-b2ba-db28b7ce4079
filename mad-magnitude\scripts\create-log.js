#!/usr/bin/env node

/**
 * 研究日志创建脚本
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const CONTENT_DIR = path.join(__dirname, '../src/content');
const TEMPLATE_PATH = path.join(CONTENT_DIR, 'templates/log-template.md');

// 心情类型配置
const MOOD_TYPES = {
  thoughtful: { label: '深思', icon: '🤔', description: '深入思考和反思' },
  critical: { label: '批判', icon: '🧐', description: '批判性思维和分析' },
  optimistic: { label: '乐观', icon: '😊', description: '积极乐观的心态' },
  analytical: { label: '分析', icon: '🔍', description: '理性分析和逻辑思考' },
};

// 研究所配置
const INSTITUTES = {
  economics: { label: '经济研究所', icon: '💰' },
  philosophy: { label: '哲学研究所', icon: '🤔' },
  internet: { label: '互联网研究所', icon: '🌐' },
  ai: { label: 'AI研究所', icon: '🤖' },
  future: { label: '未来研究所', icon: '🔮' },
};

/**
 * 生成文件名（基于日期）
 */
function generateFileName(date = new Date()) {
  const dateStr = date.toISOString().split('T')[0];
  return `${dateStr}-research-log.md`;
}

/**
 * 创建研究日志
 */
function createLog(options) {
  const {
    title,
    date = new Date(),
    mood = 'thoughtful',
    institutes = [],
    tags = [],
    weather = '',
    location = '书房',
  } = options;

  // 验证输入
  if (!title) {
    throw new Error('标题是必需的');
  }

  if (!MOOD_TYPES[mood]) {
    throw new Error(`无效的心情类型: ${mood}`);
  }

  // 验证研究所
  const validInstitutes = institutes.filter(inst => INSTITUTES[inst]);
  if (institutes.length > 0 && validInstitutes.length === 0) {
    console.warn('警告: 没有有效的研究所被指定');
  }

  // 读取模板
  if (!fs.existsSync(TEMPLATE_PATH)) {
    throw new Error(`模板文件不存在: ${TEMPLATE_PATH}`);
  }

  const template = fs.readFileSync(TEMPLATE_PATH, 'utf8');

  // 生成内容
  const dateStr = date.toISOString().split('T')[0];

  const content = template
    .replace(/{{date}}/g, dateStr)
    .replace(/{{mood}}/g, mood)
    .replace(/{{weather}}/g, weather)
    .replace(/{{location}}/g, location)
    .replace(/date:\s*[\d-]+/g, `date: ${dateStr}`)
    .replace(/title:\s*'[^']*'/g, `title: '${title}'`)
    .replace(/mood:\s*'[^']*'/g, `mood: '${mood}'`)
    .replace(
      /relatedInstitute:\s*\[[^\]]*\]/g,
      `relatedInstitute: [${validInstitutes.map(i => `'${i}'`).join(', ')}]`
    )
    .replace(/tags:\s*\[[^\]]*\]/g, `tags: [${tags.map(t => `'${t}'`).join(', ')}]`)
    .replace(/weather:\s*'[^']*'/g, `weather: '${weather}'`)
    .replace(/location:\s*'[^']*'/g, `location: '${location}'`);

  // 生成文件名和路径
  const fileName = generateFileName(date);
  const filePath = path.join(CONTENT_DIR, 'logs', fileName);

  // 检查文件是否已存在
  if (fs.existsSync(filePath)) {
    throw new Error(`今日日志已存在: ${fileName}`);
  }

  // 创建文件
  fs.writeFileSync(filePath, content, 'utf8');

  return {
    fileName,
    filePath,
    mood: MOOD_TYPES[mood].label,
    institutes: validInstitutes.map(i => INSTITUTES[i].label),
  };
}

/**
 * 快速创建今日日志
 */
function createTodayLog(title, options = {}) {
  const today = new Date();
  return createLog({
    title,
    date: today,
    ...options,
  });
}

/**
 * 交互式创建日志
 */
async function interactiveCreate() {
  const readline = await import('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  const question = prompt => new Promise(resolve => rl.question(prompt, resolve));

  try {
    console.log('📔 创建新的研究日志\n');

    const title = await question('📝 请输入日志标题: ');
    if (!title.trim()) {
      console.log('❌ 标题不能为空');
      process.exit(1);
    }

    const dateInput = await question('📅 请输入日期 (YYYY-MM-DD，回车使用今天): ');
    let logDate = new Date();
    if (dateInput.trim()) {
      logDate = new Date(dateInput.trim());
      if (isNaN(logDate.getTime())) {
        console.log('❌ 日期格式无效');
        process.exit(1);
      }
    }

    console.log('\n😊 选择心情:');
    Object.entries(MOOD_TYPES).forEach(([key, config], index) => {
      console.log(`  ${index + 1}. ${config.icon} ${config.label} - ${config.description}`);
    });

    const moodChoice = await question('请选择心情 (1-4): ');
    const moodKeys = Object.keys(MOOD_TYPES);
    const selectedMood = moodKeys[parseInt(moodChoice) - 1] || 'thoughtful';

    console.log('\n🏛️ 选择相关研究所 (可多选，用逗号分隔):');
    Object.entries(INSTITUTES).forEach(([key, config], index) => {
      console.log(`  ${index + 1}. ${config.icon} ${config.label}`);
    });

    const instituteChoice = await question('请选择研究所 (例: 1,3): ');
    const instituteKeys = Object.keys(INSTITUTES);
    const selectedInstitutes = instituteChoice
      .split(',')
      .map(i => instituteKeys[parseInt(i.trim()) - 1])
      .filter(Boolean);

    const tags = await question('🏷️ 请输入标签 (用逗号分隔): ');
    const tagList = tags
      .split(',')
      .map(t => t.trim())
      .filter(Boolean);

    const weather = await question('🌤️ 今日天气 (可选): ');
    const location = (await question('📍 记录地点 (可选，默认: 书房): ')) || '书房';

    // 创建日志
    const result = createLog({
      title: title.trim(),
      date: logDate,
      mood: selectedMood,
      institutes: selectedInstitutes,
      tags: tagList,
      weather: weather.trim(),
      location: location.trim(),
    });

    console.log('\n✅ 日志创建成功!');
    console.log(`📁 文件名: ${result.fileName}`);
    console.log(`📂 路径: ${result.filePath}`);
    console.log(`😊 心情: ${result.mood}`);
    if (result.institutes.length > 0) {
      console.log(`🏛️ 研究所: ${result.institutes.join(', ')}`);
    }
  } catch (error) {
    console.error('❌ 创建失败:', error.message);
    process.exit(1);
  } finally {
    rl.close();
  }
}

/**
 * 命令行创建日志
 */
function cliCreate() {
  const args = process.argv.slice(2);

  if (args.length === 0) {
    console.log(`
📔 研究日志创建工具

用法:
  node create-log.js                    # 交互式创建
  node create-log.js [选项]             # 命令行创建

选项:
  --title "标题"                        # 日志标题 (必需)
  --date YYYY-MM-DD                    # 日期 (默认今天)
  --mood thoughtful|critical|optimistic|analytical  # 心情
  --institutes economics,ai,philosophy  # 相关研究所
  --tags "标签1,标签2"                  # 标签
  --weather "天气"                     # 天气情况
  --location "地点"                    # 记录地点

示例:
  node create-log.js --title "AI研究思考" --mood thoughtful --institutes ai --tags "AI,思考" --weather "晴朗"

快捷命令:
  node create-log.js --today "今日思考"  # 快速创建今日日志

可用的心情类型: ${Object.keys(MOOD_TYPES).join(', ')}
可用的研究所: ${Object.keys(INSTITUTES).join(', ')}
`);
    return;
  }

  // 解析命令行参数
  const options = {};
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    if (arg.startsWith('--')) {
      const key = arg.slice(2);
      if (key === 'today') {
        const title = args[i + 1];
        if (title && !title.startsWith('--')) {
          try {
            const result = createTodayLog(title);
            console.log('✅ 今日日志创建成功!');
            console.log(`📁 文件: ${result.fileName}`);
            return;
          } catch (error) {
            console.error('❌ 创建失败:', error.message);
            process.exit(1);
          }
        }
      } else {
        const value = args[i + 1];
        if (value && !value.startsWith('--')) {
          if (key === 'institutes' || key === 'tags') {
            options[key] = value.split(',').map(s => s.trim());
          } else if (key === 'date') {
            options[key] = new Date(value);
          } else {
            options[key] = value;
          }
          i++; // 跳过值参数
        }
      }
    }
  }

  try {
    const result = createLog(options);
    console.log('✅ 日志创建成功!');
    console.log(`📁 文件: ${result.fileName}`);
  } catch (error) {
    console.error('❌ 创建失败:', error.message);
    process.exit(1);
  }
}

// 主函数
function main() {
  const args = process.argv.slice(2);

  if (args.length === 0 || args.includes('--interactive')) {
    interactiveCreate();
  } else {
    cliCreate();
  }
}

// 运行脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { createLog, createTodayLog, generateFileName };
