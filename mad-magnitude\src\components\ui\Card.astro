---
/**
 * Enhanced Card component for Pennfly Private Academy
 * Provides flexible container with consistent styling and interactive states
 */

export interface Props {
  variant?: 'default' | 'elevated' | 'outlined' | 'filled';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  interactive?: boolean;
  href?: string;
  target?: '_blank' | '_self' | '_parent' | '_top';
  class?: string;
  'aria-label'?: string;
}

const {
  variant = 'default',
  padding = 'md',
  interactive = false,
  href,
  target,
  class: className = '',
  'aria-label': ariaLabel,
  ...rest
} = Astro.props;

// Padding configurations
const paddingClasses = {
  none: 'p-0',
  sm: 'p-4',
  md: 'p-6',
  lg: 'p-8',
  xl: 'p-10',
};

// Variant configurations using theme-aware classes
const variantClasses = {
  default: `
    bg-theme-bg-elevated
    border border-theme-border-default
    shadow-theme-sm
  `,
  elevated: `
    bg-theme-bg-elevated
    border border-theme-border-subtle
    shadow-theme-md
  `,
  outlined: `
    bg-theme-bg-primary
    border-2 border-theme-border-strong
    shadow-none
  `,
  filled: `
    bg-theme-bg-secondary
    border border-theme-border-subtle
    shadow-none
  `,
};

// Interactive states
const interactiveClasses =
  interactive || href
    ? `
  transition-all duration-200 ease-in-out
  hover:shadow-theme-lg hover:border-theme-border-interactive
  hover:-translate-y-1
  active:translate-y-0 active:shadow-theme-md
  focus:outline-none focus:ring-2 focus:ring-theme-interactive-focus focus:ring-offset-2 focus:ring-offset-theme-bg-primary
  cursor-pointer
`
    : '';

// Base classes
const baseClasses = `
  rounded-lg
  overflow-hidden
  ${paddingClasses[padding]}
  ${variantClasses[variant]}
  ${interactiveClasses}
`;

const combinedClasses = `
  ${baseClasses}
  ${className}
`
  .trim()
  .replace(/\s+/g, ' ');

// Determine if this should render as a link or div
const isLink = href && !rest.disabled;
const Component = isLink ? 'a' : 'div';

// Prepare props for the component
const componentProps = isLink ? { href, target } : {};

// Add role and tabindex for interactive non-link cards
const accessibilityProps = interactive && !isLink ? { role: 'button', tabindex: '0' } : {};
---

<Component
  class={combinedClasses}
  aria-label={ariaLabel}
  {...componentProps}
  {...accessibilityProps}
  {...rest}
>
  <slot />
</Component>

<style>
  /* Ensure smooth transitions respect user preferences */
  @media (prefers-reduced-motion: reduce) {
    div,
    a {
      transition: none !important;
      transform: none !important;
    }
  }

  /* Enhanced focus styles for better accessibility */
  div[role='button']:focus-visible,
  a:focus-visible {
    outline: 2px solid var(--color-interactive-focus);
    outline-offset: 2px;
  }

  /* Keyboard interaction for interactive cards */
  div[role='button']:focus,
  div[role='button']:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }

  div[role='button']:active {
    transform: translateY(0);
    box-shadow: var(--shadow-md);
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    div,
    a {
      border-width: 2px;
      border-color: currentColor;
    }
  }

  /* Print styles */
  @media print {
    div,
    a {
      box-shadow: none !important;
      transform: none !important;
      border: 1px solid #000 !important;
    }
  }
</style>
