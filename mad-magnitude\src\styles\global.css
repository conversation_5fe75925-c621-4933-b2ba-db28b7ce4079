@import 'tailwindcss';
@import './tokens.css';

/* 基础样式重置和优化 */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-family:
    'PingFang SC',
    'Hiragino Sans GB',
    'Microsoft YaHei',
    'WenQuanYi Micro Hei',
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    'Helvetica Neue',
    Helvetica,
    Arial,
    sans-serif;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #fafafa;
  color: #1f2937;
}

/* 确保图片响应式 */
img {
  max-width: 100%;
  height: auto;
}

/* 优化按钮和表单元素 */
button,
input,
textarea,
select {
  font: inherit;
}

/* 改善焦点指示器 */
:focus {
  outline: none;
}

:focus-visible {
  outline: 3px solid #3b82f6;
  outline-offset: 2px;
  border-radius: 3px;
}

/* 确保键盘用户能看到焦点 */
.js-focus-visible :focus:not(.focus-visible) {
  outline: none;
}

/* 改善跳转链接 */
.skip-link:focus {
  position: static;
  width: auto;
  height: auto;
  padding: 8px 16px;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* 平滑过渡 */
* {
  transition:
    color 0.2s ease,
    background-color 0.2s ease,
    border-color 0.2s ease,
    text-decoration-color 0.2s ease,
    fill 0.2s ease,
    stroke 0.2s ease,
    opacity 0.2s ease,
    box-shadow 0.2s ease,
    transform 0.2s ease,
    filter 0.2s ease,
    backdrop-filter 0.2s ease;
}

/* 容器样式优化 */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

/* 改善卡片样式 */
.card {
  background: white;
  border-radius: 0.75rem;
  box-shadow:
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

/* 减少动画对于偏好减少动画的用户 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
