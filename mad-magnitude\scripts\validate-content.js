#!/usr/bin/env node

/**
 * 内容验证和格式化脚本
 */

import fs from 'fs';
import matter from 'gray-matter';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const CONTENT_DIR = path.join(__dirname, '../src/content');

// 必需字段定义
const REQUIRED_FIELDS = {
  base: ['title.zh', 'description.zh', 'publishDate', 'tags', 'author'],
  news: ['type'],
  logs: ['date', 'title'],
  research: ['type'],
  reflections: ['type'],
};

// 推荐字段
const RECOMMENDED_FIELDS = {
  base: ['summary', 'featured'],
  news: ['relatedInstitute'],
  research: ['relatedInstitute', 'relatedContent'],
  reflections: ['relatedInstitute', 'mood'],
};

/**
 * 获取嵌套对象的值
 */
function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

/**
 * 验证单个内容文件
 */
function validateContentFile(filePath, collectionType) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const { data: frontmatter } = matter(content);

    const errors = [];
    const warnings = [];

    // 检查必需字段
    const requiredFields = [...REQUIRED_FIELDS.base, ...(REQUIRED_FIELDS[collectionType] || [])];

    for (const field of requiredFields) {
      const value = getNestedValue(frontmatter, field);
      if (value === undefined || value === null || value === '') {
        errors.push(`Missing required field: ${field}`);
      }
    }

    // 检查推荐字段
    const recommendedFields = [
      ...RECOMMENDED_FIELDS.base,
      ...(RECOMMENDED_FIELDS[collectionType] || []),
    ];

    for (const field of recommendedFields) {
      const value = getNestedValue(frontmatter, field);
      if (value === undefined || value === null || value === '') {
        warnings.push(`Missing recommended field: ${field}`);
      }
    }

    // 特定验证
    if (frontmatter.tags && !Array.isArray(frontmatter.tags)) {
      errors.push('Tags must be an array');
    }

    if (frontmatter.relatedContent && !Array.isArray(frontmatter.relatedContent)) {
      errors.push('relatedContent must be an array');
    }

    if (frontmatter.publishDate && !(frontmatter.publishDate instanceof Date)) {
      try {
        new Date(frontmatter.publishDate);
      } catch {
        errors.push('Invalid publishDate format');
      }
    }

    return {
      file: path.relative(CONTENT_DIR, filePath),
      valid: errors.length === 0,
      errors,
      warnings,
      frontmatter,
    };
  } catch (error) {
    return {
      file: path.relative(CONTENT_DIR, filePath),
      valid: false,
      errors: [`Failed to parse file: ${error.message}`],
      warnings: [],
      frontmatter: null,
    };
  }
}

/**
 * 扫描内容目录
 */
function scanContentDirectory() {
  const results = [];

  // 扫描各个集合目录
  const collections = [
    'news',
    'logs',
    'research',
    'reflections',
    'economics',
    'philosophy',
    'internet',
    'ai',
    'future',
    'products',
  ];

  for (const collection of collections) {
    const collectionDir = path.join(CONTENT_DIR, collection);

    if (!fs.existsSync(collectionDir)) {
      console.log(`⚠️  Collection directory not found: ${collection}`);
      continue;
    }

    const files = fs
      .readdirSync(collectionDir)
      .filter(file => file.endsWith('.md'))
      .map(file => path.join(collectionDir, file));

    for (const file of files) {
      const result = validateContentFile(file, collection);
      result.collection = collection;
      results.push(result);
    }
  }

  return results;
}

/**
 * 生成报告
 */
function generateReport(results) {
  const validFiles = results.filter(r => r.valid);
  const invalidFiles = results.filter(r => !r.valid);
  const filesWithWarnings = results.filter(r => r.warnings.length > 0);

  console.log('\n📊 Content Validation Report');
  console.log('='.repeat(50));

  console.log(`\n✅ Valid files: ${validFiles.length}`);
  console.log(`❌ Invalid files: ${invalidFiles.length}`);
  console.log(`⚠️  Files with warnings: ${filesWithWarnings.length}`);
  console.log(`📁 Total files: ${results.length}`);

  if (invalidFiles.length > 0) {
    console.log('\n❌ Invalid Files:');
    console.log('-'.repeat(30));

    for (const file of invalidFiles) {
      console.log(`\n📄 ${file.file} (${file.collection})`);
      for (const error of file.errors) {
        console.log(`   ❌ ${error}`);
      }
    }
  }

  if (filesWithWarnings.length > 0) {
    console.log('\n⚠️  Files with Warnings:');
    console.log('-'.repeat(30));

    for (const file of filesWithWarnings) {
      console.log(`\n📄 ${file.file} (${file.collection})`);
      for (const warning of file.warnings) {
        console.log(`   ⚠️  ${warning}`);
      }
    }
  }

  // 统计信息
  console.log('\n📈 Statistics by Collection:');
  console.log('-'.repeat(30));

  const collectionStats = {};
  for (const result of results) {
    if (!collectionStats[result.collection]) {
      collectionStats[result.collection] = { total: 0, valid: 0, invalid: 0 };
    }
    collectionStats[result.collection].total++;
    if (result.valid) {
      collectionStats[result.collection].valid++;
    } else {
      collectionStats[result.collection].invalid++;
    }
  }

  for (const [collection, stats] of Object.entries(collectionStats)) {
    console.log(`${collection}: ${stats.valid}/${stats.total} valid`);
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🔍 Validating content files...');

  const results = scanContentDirectory();
  generateReport(results);

  const hasErrors = results.some(r => !r.valid);
  process.exit(hasErrors ? 1 : 0);
}

// 运行脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { scanContentDirectory, validateContentFile };
