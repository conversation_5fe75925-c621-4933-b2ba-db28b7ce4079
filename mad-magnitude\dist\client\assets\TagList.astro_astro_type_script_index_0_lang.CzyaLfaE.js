document.addEventListener("DOMContentLoaded",()=>{document.querySelectorAll('.tag-item[style*="--tag-color"]').forEach(t=>{const e=t,i=e.style.getPropertyValue("--tag-color");if(i){const s=i.replace("#",""),o=parseInt(s.substr(0,2),16),r=parseInt(s.substr(2,2),16),h=parseInt(s.substr(4,2),16);e.style.setProperty("--tag-color-rgb",`${o}, ${r}, ${h}`)}})});document.addEventListener("DOMContentLoaded",()=>{document.querySelectorAll('.category-card[style*="--category-color"]').forEach(t=>{const e=t,i=e.style.getPropertyValue("--category-color");if(i){const s=i.replace("#",""),o=parseInt(s.substring(0,2),16),r=parseInt(s.substring(2,4),16),h=parseInt(s.substring(4,6),16);e.style.setProperty("--category-color-rgb",`${o}, ${r}, ${h}`)}})});class l{input;clearButton;categorySelect;suggestionsDropdown;suggestionsList;suggestionsCount;loadingIndicator;noResults;viewAllButton;maxSuggestions;searchTimeout=null;currentQuery="";highlightedIndex=-1;suggestions=[];constructor(t){this.input=t.querySelector(".search-input"),this.clearButton=t.querySelector(".clear-button"),this.categorySelect=t.querySelector(".category-select"),this.suggestionsDropdown=t.querySelector(".suggestions-dropdown"),this.suggestionsList=t.querySelector(".suggestions-list"),this.suggestionsCount=t.querySelector(".suggestions-count"),this.loadingIndicator=t.querySelector(".loading-indicator"),this.noResults=t.querySelector(".no-results"),this.viewAllButton=t.querySelector(".view-all-button"),this.maxSuggestions=parseInt(this.input.dataset.maxSuggestions||"8"),this.bindEvents()}bindEvents(){this.input.addEventListener("input",t=>{const e=t.target.value.trim();this.handleInput(e)}),this.input.addEventListener("keydown",t=>{this.handleKeydown(t)}),this.clearButton.addEventListener("click",()=>{this.clearSearch()}),this.categorySelect&&this.categorySelect.addEventListener("change",()=>{this.currentQuery&&this.performSearch(this.currentQuery)}),this.viewAllButton.addEventListener("click",()=>{this.navigateToTagsPage()}),document.addEventListener("click",t=>{this.input.closest(".tag-search")?.contains(t.target)||this.hideSuggestions()})}handleInput(t){if(this.currentQuery=t,this.updateClearButton(),this.searchTimeout&&clearTimeout(this.searchTimeout),t.length===0){this.hideSuggestions();return}t.length<2||(this.searchTimeout=window.setTimeout(()=>{this.performSearch(t)},300))}async performSearch(t){this.showLoading();try{const e=this.categorySelect?.value||"all",i=await fetch(`/api/tags/search?q=${encodeURIComponent(t)}&category=${e}&limit=${this.maxSuggestions}`);if(!i.ok)throw new Error("搜索失败");const s=await i.json();this.suggestions=s.results,this.displaySuggestions(s)}catch(e){console.error("标签搜索错误:",e),this.showNoResults()}}displaySuggestions(t){if(this.hideLoading(),t.results.length===0){this.showNoResults();return}this.suggestionsList.innerHTML="",this.suggestionsCount.textContent=`${t.results.length} 个结果`,t.results.forEach((e,i)=>{const s=document.createElement("div");s.className="suggestion-item",s.dataset.index=i.toString(),s.innerHTML=`
          <div class="suggestion-content">
            <span class="suggestion-name">${this.highlightQuery(e.name,t.query)}</span>
            <span class="suggestion-category">${e.categoryDisplayName}</span>
          </div>
          <span class="suggestion-count">${e.count}</span>
        `,s.addEventListener("click",()=>{this.selectTag(e)}),this.suggestionsList.appendChild(s)}),this.showSuggestions(),this.highlightedIndex=-1}highlightQuery(t,e){const i=new RegExp(`(${e})`,"gi");return t.replace(i,"<mark>$1</mark>")}handleKeydown(t){if(this.isSuggestionsVisible())switch(t.key){case"ArrowDown":t.preventDefault(),this.highlightNext();break;case"ArrowUp":t.preventDefault(),this.highlightPrevious();break;case"Enter":t.preventDefault(),this.selectHighlighted();break;case"Escape":this.hideSuggestions();break}}highlightNext(){const t=this.suggestionsList.querySelectorAll(".suggestion-item");t.length!==0&&(this.highlightedIndex<t.length-1?this.highlightedIndex++:this.highlightedIndex=0,this.updateHighlight())}highlightPrevious(){const t=this.suggestionsList.querySelectorAll(".suggestion-item");t.length!==0&&(this.highlightedIndex>0?this.highlightedIndex--:this.highlightedIndex=t.length-1,this.updateHighlight())}updateHighlight(){this.suggestionsList.querySelectorAll(".suggestion-item").forEach((e,i)=>{e.classList.toggle("highlighted",i===this.highlightedIndex)})}selectHighlighted(){this.highlightedIndex>=0&&this.suggestions[this.highlightedIndex]&&this.selectTag(this.suggestions[this.highlightedIndex])}selectTag(t){window.location.href=t.url}navigateToTagsPage(){const t=this.currentQuery,e=this.categorySelect?.value||"all";let i="/tags";const s=new URLSearchParams;t&&s.set("q",t),e!=="all"&&s.set("category",e),s.toString()&&(i+="?"+s.toString()),window.location.href=i}clearSearch(){this.input.value="",this.currentQuery="",this.hideSuggestions(),this.updateClearButton(),this.input.focus()}updateClearButton(){this.clearButton.style.display=this.input.value?"block":"none"}showSuggestions(){this.suggestionsDropdown.style.display="block",this.loadingIndicator.style.display="none",this.noResults.style.display="none"}hideSuggestions(){this.suggestionsDropdown.style.display="none",this.loadingIndicator.style.display="none",this.noResults.style.display="none"}showLoading(){this.loadingIndicator.style.display="flex",this.suggestionsDropdown.style.display="none",this.noResults.style.display="none"}hideLoading(){this.loadingIndicator.style.display="none"}showNoResults(){this.noResults.style.display="block",this.suggestionsDropdown.style.display="none",this.loadingIndicator.style.display="none"}isSuggestionsVisible(){return this.suggestionsDropdown.style.display==="block"}}document.addEventListener("DOMContentLoaded",()=>{document.querySelectorAll(".tag-search").forEach(t=>{new l(t)})});
