{"enabled": true, "name": "构建分析器", "description": "分析构建结果和性能优化建议", "version": "1", "when": {"type": "userTriggered"}, "then": {"type": "askAgent", "prompt": "请分析当前项目的构建状态和性能：\n\n1. **运行构建分析**：\n   ```bash\n   npm run build\n   npm run analyze\n   ```\n\n2. **检查构建产物**：\n   - 总体积是否合理（目标 < 3MB）\n   - JavaScript 包大小\n   - CSS 文件大小\n   - 图片优化情况\n   - 字体文件大小\n\n3. **性能指标**：\n   - 构建时间\n   - 页面数量\n   - 静态资源数量\n\n4. **优化建议**：\n   - 识别可以优化的大文件\n   - 建议代码分割策略\n   - 推荐图片压缩方案\n   - 提出缓存优化建议\n\n5. **质量检查**：\n   - TypeScript 编译状态\n   - ESLint 检查结果\n   - 测试覆盖率\n\n请提供详细的分析报告和具体的优化建议。"}}