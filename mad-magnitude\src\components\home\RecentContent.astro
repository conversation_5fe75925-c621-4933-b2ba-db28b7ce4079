---
import { getCollection } from 'astro:content';
import { formatDate } from '../../utils/dateUtils';

// 获取最新的动态资讯
const recentNews = await getCollection('news').then((items: any[]) =>
  items.filter((item: any) => !item.data.draft)
);

// 获取最新的研究日志
const recentLogs = await getCollection('logs').then((items: any[]) =>
  items.filter((item: any) => !item.data.draft)
);

// 获取最新的研究内容
const recentResearch = await getCollection('research').then((items: any[]) =>
  items.filter((item: any) => !item.data.draft)
);

// 内容类型定义
type ContentType = 'news' | 'logs' | 'research';

// 合并并排序所有内容，取最新的6条
const allContent = [
  ...recentNews.map((item: any) => ({ ...item, type: 'news' as ContentType, collection: 'news' })),
  ...recentLogs.map((item: any) => ({ ...item, type: 'logs' as ContentType, collection: 'logs' })),
  ...recentResearch.map((item: any) => ({
    ...item,
    type: 'research' as ContentType,
    collection: 'research',
  })),
]
  .sort((a: any, b: any) => {
    const dateA = a.data.publishDate || a.data.date;
    const dateB = b.data.publishDate || b.data.date;
    if (!dateA || !dateB) return 0;
    return new Date(dateB).getTime() - new Date(dateA).getTime();
  })
  .slice(0, 6);

// 内容类型图标映射
const typeIcons: Record<ContentType, string> = {
  news: '📰',
  logs: '📔',
  research: '🔬',
};

// 内容类型名称映射
const typeNames: Record<ContentType, string> = {
  news: '动态资讯',
  logs: '研究日志',
  research: '研究文章',
};
---

<section class="mb-16">
  <h3 class="mb-8 text-center text-2xl font-bold text-slate-800">最新内容</h3>
  <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
    {
      allContent.map((item: any) => {
        const title =
          typeof item.data.title === 'string' ? item.data.title : item.data.title?.zh || '无标题';
        const description =
          typeof item.data.description === 'string'
            ? item.data.description
            : item.data.description?.zh || item.data.summary || '';
        const publishDate = item.data.publishDate || item.data.date;
        const url = `/${item.collection}/${item.slug}`;

        return (
          <article class="rounded-lg bg-white p-6 shadow-md transition-all hover:-translate-y-1 hover:shadow-lg">
            <div class="mb-3 flex items-center justify-between">
              <div class="flex items-center">
                <span class="mr-2 text-lg">{typeIcons[item.type as ContentType]}</span>
                <span class="text-sm font-medium text-blue-600">
                  {typeNames[item.type as ContentType]}
                </span>
              </div>
              {publishDate && (
                <time class="text-sm text-slate-500" datetime={publishDate.toISOString()}>
                  {formatDate(publishDate)}
                </time>
              )}
            </div>

            <h4 class="mb-3 line-clamp-2 text-lg font-semibold text-slate-800">
              <a href={url} class="transition-colors hover:text-blue-600">
                {title}
              </a>
            </h4>

            {description && <p class="mb-4 line-clamp-3 text-slate-600">{description}</p>}

            <div class="flex items-center justify-between">
              <div class="flex flex-wrap gap-1">
                {item.data.tags?.slice(0, 2).map((tag: string) => (
                  <span class="rounded-full bg-slate-100 px-2 py-1 text-xs text-slate-600">
                    {tag}
                  </span>
                ))}
              </div>
              <a
                href={url}
                class="text-sm font-medium text-blue-600 transition-colors hover:text-blue-800"
              >
                阅读更多 →
              </a>
            </div>
          </article>
        );
      })
    }
  </div>

  <div class="mt-8 text-center">
    <a
      href="/search"
      class="inline-flex items-center rounded-lg bg-blue-600 px-6 py-3 font-medium text-white transition-colors hover:bg-blue-700"
    >
      <span class="mr-2">🔍</span>
      浏览所有内容
    </a>
  </div>
</section>

<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
