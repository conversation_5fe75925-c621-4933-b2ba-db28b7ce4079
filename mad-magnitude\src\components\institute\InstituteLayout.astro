---
import Layout from '../../layouts/Layout.astro';

export interface Props {
  institute: {
    id: string;
    name: string;
    nameEn: string;
    icon: string;
    description: string;
    color: string;
    gradientFrom: string;
    gradientTo: string;
    fields: Array<{
      name: string;
      description: string;
    }>;
  };
  articles: any[];
  totalCount: number;
}

const { institute, articles, totalCount } = Astro.props;
---

<Layout title={`${institute.name} - Pennfly Private Academy`} description={institute.description}>
  <main class="min-h-screen bg-gray-50">
    <!-- 页面头部 -->
    <div
      class={`bg-gradient-to-r ${institute.gradientFrom} ${institute.gradientTo} py-16 text-white`}
    >
      <div class="container mx-auto px-6">
        <div class="mb-6 flex items-center">
          <span class="mr-4 text-5xl">{institute.icon}</span>
          <div>
            <h1 class="mb-2 text-4xl font-bold">{institute.name}</h1>
            <p class="text-xl opacity-90">{institute.nameEn}</p>
          </div>
        </div>
        <p class="max-w-3xl text-lg opacity-90">
          {institute.description}
        </p>
      </div>
    </div>

    <!-- 研究领域 -->
    <div class="container mx-auto px-6 py-12">
      <div class="mb-12">
        <h2 class="mb-6 text-2xl font-bold text-gray-800">研究领域</h2>
        <div
          class={`grid gap-4 ${institute.fields.length <= 4 ? 'md:grid-cols-2 lg:grid-cols-4' : 'md:grid-cols-2 lg:grid-cols-3'}`}
        >
          {
            institute.fields.map(field => (
              <div class="rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
                <h3 class="mb-2 font-semibold text-gray-800">{field.name}</h3>
                <p class="text-sm text-gray-600">{field.description}</p>
              </div>
            ))
          }
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="mb-12">
        <div class="grid gap-6 md:grid-cols-3">
          <div class="rounded-lg border border-gray-200 bg-white p-6 text-center shadow-sm">
            <div class="mb-2 text-3xl font-bold text-gray-800">{totalCount}</div>
            <div class="text-sm text-gray-600">研究文章</div>
          </div>
          <div class="rounded-lg border border-gray-200 bg-white p-6 text-center shadow-sm">
            <div class="mb-2 text-3xl font-bold text-gray-800">
              {articles.filter(article => article.data.featured).length}
            </div>
            <div class="text-sm text-gray-600">精选文章</div>
          </div>
          <div class="rounded-lg border border-gray-200 bg-white p-6 text-center shadow-sm">
            <div class="mb-2 text-3xl font-bold text-gray-800">
              {new Set(articles.flatMap(article => article.data.tags)).size}
            </div>
            <div class="text-sm text-gray-600">研究标签</div>
          </div>
        </div>
      </div>

      <!-- 文章列表 -->
      <div class="mb-12">
        <div class="mb-8 flex items-center justify-between">
          <h2 class="text-2xl font-bold text-gray-800">研究文章</h2>
          <div class="text-sm text-gray-600">
            共 {totalCount} 篇文章
          </div>
        </div>

        {
          articles.length > 0 ? (
            <div class="space-y-6">
              {articles.map((article: any) => (
                <article class="rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition-shadow hover:shadow-md">
                  <div class="mb-4 flex items-start justify-between">
                    <div class="flex-1">
                      <h3 class="mb-2 text-xl font-semibold text-gray-800">
                        <a
                          href={`/${institute.id}/${article.slug}`}
                          class={`transition-colors hover:${institute.color}`}
                        >
                          {article.data.title.zh}
                        </a>
                      </h3>
                      <p class="mb-3 text-gray-600">{article.data.description.zh}</p>

                      <div class="flex items-center space-x-4 text-sm text-gray-500">
                        <span>{article.data.publishDate.toLocaleDateString('zh-CN')}</span>
                        {article.data.readingTime && (
                          <span>阅读时间 {article.data.readingTime} 分钟</span>
                        )}
                      </div>
                    </div>

                    {article.data.featured && (
                      <div class="ml-4">
                        <span class="rounded bg-yellow-100 px-2 py-1 text-xs font-medium text-yellow-800">
                          精选
                        </span>
                      </div>
                    )}
                  </div>

                  {article.data.tags.length > 0 && (
                    <div class="flex flex-wrap gap-2">
                      {article.data.tags.map((tag: string) => (
                        <span class="rounded bg-gray-100 px-2 py-1 text-xs text-gray-700">
                          {tag}
                        </span>
                      ))}
                    </div>
                  )}
                </article>
              ))}
            </div>
          ) : (
            <div class="py-12 text-center">
              <div class="mb-4 text-6xl">{institute.icon}</div>
              <h3 class="mb-2 text-xl font-semibold text-gray-800">暂无研究文章</h3>
              <p class="mb-6 text-gray-600">{institute.name}正在筹备中，敬请期待精彩的研究内容</p>
              <a
                href="/admin"
                class={`inline-block rounded-lg ${institute.color.replace('text-', 'bg-').replace('-600', '-600')} px-6 py-2 text-white transition-colors hover:${institute.color.replace('text-', 'bg-').replace('-600', '-700')}`}
              >
                发布文章
              </a>
            </div>
          )
        }
      </div>

      <!-- 返回首页 -->
      <div class="text-center">
        <a
          href="/"
          class="inline-block rounded-lg bg-gray-600 px-6 py-2 text-white transition-colors hover:bg-gray-700"
        >
          ← 返回研究院首页
        </a>
      </div>
    </div>
  </main>
</Layout>
