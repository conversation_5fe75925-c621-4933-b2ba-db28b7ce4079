/**
 * Astro Content Collections 类型定义
 */

// 基础内容项接口
export interface BaseContentEntry {
  slug: string;
  data: {
    title?: string | { zh: string; en?: string };
    description?: string | { zh: string; en?: string };
    publishDate?: Date;
    date?: Date;
    draft?: boolean;
    tags?: string[];
    [key: string]: any;
  };
}

// 内容集合类型
export type ContentCollectionName =
  | 'news'
  | 'logs'
  | 'research'
  | 'economics'
  | 'philosophy'
  | 'internet'
  | 'ai'
  | 'future'
  | 'products'
  | 'reflections';

// 扩展的内容项（带类型标识）
export interface ExtendedContentEntry extends BaseContentEntry {
  type: string;
  collection: string;
}

// 内容筛选函数类型
export type ContentFilter = (entry: { data: any }) => boolean;
