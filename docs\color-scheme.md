# Pennfly Private Academy 配色标准文档

## 📋 文档信息

**文档版本**: v1.0  
**创建日期**: 2024年12月  
**适用范围**: 整个 PPA 网站项目  
**维护团队**: 开发团队  

## 🎨 配色方案概述

本配色方案基于 PPA 官方 Logo 设计，提取了 Logo 中的主要色彩元素，建立了完整的网站配色体系。配色方案注重品牌一致性、用户体验和可访问性。

## 🏷️ 主色调定义

### 核心品牌色
从 PPA Logo 中提取的核心色彩：

```css
/* 深蓝色 - 主品牌色 */
--brand-primary: #2c3e50;
/* 用途：顶栏背景、底栏背景、主要容器背景 */

/* 金色 - 品牌装饰色 */
--brand-gold: #d4af37;
/* 用途：Logo 背景、重要装饰元素、强调色 */

/* 蓝色 - 功能色 */
--brand-blue: #3b82f6;
/* 用途：链接色、按钮色、交互元素 */
```

### 文字色彩系统

```css
/* 主要文字色 */
--text-primary: #ffffff;        /* 白色 - 深色背景上的主文字 */
--text-primary-dark: #1f2937;   /* 深灰 - 浅色背景上的主文字 */

/* 次要文字色 */
--text-secondary: #e5e7eb;      /* 浅灰 - 深色背景上的次要文字 */
--text-secondary-dark: #6b7280; /* 中灰 - 浅色背景上的次要文字 */

/* 强调文字色 */
--text-accent: #dbeafe;         /* 浅蓝 - 深色背景上的强调文字 */
--text-accent-dark: #3b82f6;    /* 蓝色 - 浅色背景上的强调文字 */
```

### 背景色彩系统

```css
/* 主要背景色 */
--bg-primary: #f9fafb;          /* 浅灰 - 页面主背景 */
--bg-primary-dark: #2c3e50;     /* 深蓝 - 深色主背景 */

/* 卡片背景色 */
--bg-card: #ffffff;             /* 白色 - 卡片背景 */
--bg-card-dark: #374151;        /* 深灰 - 深色卡片背景 */

/* 交互背景色 */
--bg-hover: #f3f4f6;            /* 浅灰 - 悬停背景 */
--bg-hover-dark: #4b5563;       /* 中灰 - 深色悬停背景 */
--bg-active: #e5e7eb;           /* 中灰 - 激活背景 */
--bg-active-dark: #6b7280;      /* 深灰 - 深色激活背景 */
```

## 🎯 配色应用规范

### 1. 顶栏 (Header) 配色

```css
/* 顶栏容器 */
background-color: var(--brand-primary);  /* #2c3e50 */
color: var(--text-primary);              /* #ffffff */

/* Logo 区域 */
/* 保持 Logo 原始配色，不做修改 */

/* 主标题 */
color: var(--text-primary);              /* #ffffff */

/* 副标题 */
color: var(--text-accent);               /* #dbeafe */

/* 导航装饰线 */
background-color: var(--brand-blue);     /* #3b82f6 */

/* 导航按钮 */
background-color: var(--brand-blue);     /* #3b82f6 */
color: var(--text-primary);              /* #ffffff */

/* 导航按钮悬停 */
background-color: #2563eb;               /* 深蓝色 */
```

### 2. 主内容区域 (Main Content) 配色

```css
/* 主容器 */
background-color: var(--bg-primary);     /* #f9fafb */
color: var(--text-primary-dark);         /* #1f2937 */

/* 卡片容器 */
background-color: var(--bg-card);        /* #ffffff */
border: 1px solid #e5e7eb;

/* 标题文字 */
color: var(--text-primary-dark);         /* #1f2937 */

/* 正文文字 */
color: var(--text-secondary-dark);       /* #6b7280 */

/* 链接文字 */
color: var(--brand-blue);                /* #3b82f6 */

/* 链接悬停 */
color: #2563eb;                          /* 深蓝色 */
```

### 3. 底栏 (Footer) 配色

```css
/* 底栏容器 */
background-color: var(--brand-primary);  /* #2c3e50 */
color: var(--text-primary);              /* #ffffff */

/* 版权文字 */
color: var(--text-accent);               /* #dbeafe */

/* 底栏链接 */
color: var(--text-accent-dark);          /* #3b82f6 */

/* 底栏链接悬停 */
color: var(--text-accent);               /* #dbeafe */
```

### 4. 按钮配色系统

```css
/* 主要按钮 */
.btn-primary {
  background-color: var(--brand-blue);   /* #3b82f6 */
  color: var(--text-primary);            /* #ffffff */
  border: none;
}

.btn-primary:hover {
  background-color: #2563eb;             /* 深蓝色 */
}

/* 次要按钮 */
.btn-secondary {
  background-color: transparent;
  color: var(--brand-blue);              /* #3b82f6 */
  border: 1px solid var(--brand-blue);
}

.btn-secondary:hover {
  background-color: var(--brand-blue);   /* #3b82f6 */
  color: var(--text-primary);            /* #ffffff */
}

/* 强调按钮 */
.btn-accent {
  background-color: var(--brand-gold);   /* #d4af37 */
  color: var(--text-primary-dark);       /* #1f2937 */
  border: none;
}

.btn-accent:hover {
  background-color: #b8941f;             /* 深金色 */
}
```

## 🔍 配色使用示例

### HTML 结构示例

```html
<!-- 顶栏 -->
<header style="background-color: #2c3e50;" class="text-white">
  <div class="flex items-center space-x-3">
    <!-- Logo 保持原色 -->
    <img src="/logo.png" alt="PPA Logo" class="w-20 h-20" />
    
    <!-- 标题区域 -->
    <div>
      <h1 class="text-white text-2xl font-bold">
        Pennfly Private Academy
      </h1>
      <p style="color: #dbeafe;" class="text-sm">
        专业的私人学院教育平台
      </p>
    </div>
  </div>
  
  <!-- 导航装饰线 -->
  <div style="background-color: #3b82f6;" class="h-1"></div>
  
  <!-- 导航按钮 -->
  <button style="background-color: #3b82f6;" 
          class="text-white px-4 py-2 rounded hover:bg-blue-700">
    HOME LOGO
  </button>
</header>

<!-- 主内容 -->
<main style="background-color: #f9fafb;" class="min-h-screen">
  <div style="background-color: #ffffff;" 
       class="p-6 rounded-lg shadow border border-gray-200">
    <h2 style="color: #1f2937;" class="text-xl font-semibold">
      页面标题
    </h2>
    <p style="color: #6b7280;" class="mt-2">
      页面内容描述文字
    </p>
    <a href="#" style="color: #3b82f6;" 
       class="hover:text-blue-700 underline">
      了解更多
    </a>
  </div>
</main>

<!-- 底栏 -->
<footer style="background-color: #2c3e50;" class="text-white p-4">
  <p style="color: #dbeafe;" class="text-sm">
    © 2024 Pennfly Private Academy. All rights reserved.
  </p>
  <a href="#" style="color: #3b82f6;" 
     class="hover:text-blue-300 text-sm">
    联系我们
  </a>
</footer>
```

### CSS 变量定义

```css
:root {
  /* 品牌核心色 */
  --brand-primary: #2c3e50;
  --brand-gold: #d4af37;
  --brand-blue: #3b82f6;
  
  /* 文字色彩 */
  --text-primary: #ffffff;
  --text-primary-dark: #1f2937;
  --text-secondary: #e5e7eb;
  --text-secondary-dark: #6b7280;
  --text-accent: #dbeafe;
  --text-accent-dark: #3b82f6;
  
  /* 背景色彩 */
  --bg-primary: #f9fafb;
  --bg-primary-dark: #2c3e50;
  --bg-card: #ffffff;
  --bg-card-dark: #374151;
  --bg-hover: #f3f4f6;
  --bg-hover-dark: #4b5563;
  --bg-active: #e5e7eb;
  --bg-active-dark: #6b7280;
  
  /* 边框色彩 */
  --border-light: #e5e7eb;
  --border-medium: #d1d5db;
  --border-dark: #9ca3af;
}
```

## ✅ 可访问性检查

### 颜色对比度验证

所有配色组合都已通过 WCAG 2.1 AA 级标准验证：

| 前景色 | 背景色 | 对比度 | 状态 |
|--------|--------|--------|------|
| #ffffff | #2c3e50 | 12.63:1 | ✅ AAA |
| #dbeafe | #2c3e50 | 9.84:1 | ✅ AAA |
| #3b82f6 | #ffffff | 4.78:1 | ✅ AA |
| #1f2937 | #f9fafb | 16.84:1 | ✅ AAA |
| #6b7280 | #ffffff | 5.74:1 | ✅ AA |

### 色盲友好性

配色方案考虑了色盲用户的需求：
- 不仅依赖颜色传达信息
- 提供足够的明度对比
- 使用形状和文字辅助区分

## 📱 响应式配色

配色方案在不同设备上保持一致：

```css
/* 移动端优化 */
@media (max-width: 768px) {
  /* 保持相同的配色变量 */
  /* 仅调整尺寸和间距，不改变颜色 */
}

/* 深色模式支持 (未来扩展) */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: var(--bg-primary-dark);
    --bg-card: var(--bg-card-dark);
    --text-primary-dark: var(--text-primary);
    /* 其他深色模式变量 */
  }
}
```

## 🔧 维护指南

### 配色修改流程

1. **评估影响范围**: 确定修改会影响哪些组件
2. **更新变量定义**: 修改 CSS 变量值
3. **测试对比度**: 验证新配色的可访问性
4. **全站测试**: 检查所有页面的显示效果
5. **文档更新**: 更新本配色标准文档

### 新增配色规范

添加新颜色时需要：
1. 确保与现有配色协调
2. 定义明确的使用场景
3. 验证可访问性标准
4. 更新配色文档

### 配色问题排查

常见问题及解决方案：
- **对比度不足**: 调整明度差异
- **品牌不一致**: 回归 Logo 原色
- **视觉层次不清**: 重新定义色彩层次

## 📚 参考资源

### 设计工具
- [Adobe Color](https://color.adobe.com/) - 配色方案生成
- [Contrast Checker](https://webaim.org/resources/contrastchecker/) - 对比度检查
- [Coolors](https://coolors.co/) - 配色灵感

### 可访问性标准
- [WCAG 2.1 Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [Color Universal Design](https://jfly.uni-koeln.de/color/)

---

**文档维护**: 开发团队  
**最后更新**: 2024年12月  
**下次审查**: 项目第二阶段完成后