/**
 * 测试环境初始化配置
 *
 * 此文件在所有测试运行前执行，用于设置全局测试环境
 */

import '@testing-library/jest-dom';
import { afterAll, afterEach, beforeAll, beforeEach } from 'vitest';

// 全局测试配置
beforeAll(() => {
  // 设置测试环境变量
  process.env.NODE_ENV = 'test';

  // 模拟浏览器 API
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: (query: string) => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: () => {},
      removeListener: () => {},
      addEventListener: () => {},
      removeEventListener: () => {},
      dispatchEvent: () => {},
    }),
  });

  // 模拟 IntersectionObserver
  (global as any).IntersectionObserver = class IntersectionObserver {
    root: Element | null = null;
    rootMargin: string = '0px';
    thresholds: ReadonlyArray<number> = [];

    constructor(_callback: IntersectionObserverCallback, _options?: IntersectionObserverInit) {}

    observe() {
      return null;
    }
    disconnect() {
      return null;
    }
    unobserve() {
      return null;
    }
    takeRecords(): IntersectionObserverEntry[] {
      return [];
    }
  };

  // 模拟 ResizeObserver
  global.ResizeObserver = class ResizeObserver {
    constructor() {}
    observe() {
      return null;
    }
    disconnect() {
      return null;
    }
    unobserve() {
      return null;
    }
  };

  // 模拟 fetch API
  global.fetch = async (_url: string | URL | Request, _init?: RequestInit) => {
    return new Response(JSON.stringify({}), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  };
});

// 每个测试前的清理
beforeEach(() => {
  // 清理 DOM
  document.body.innerHTML = '';

  // 重置所有模拟
  vi.clearAllMocks();
});

// 每个测试后的清理
afterEach(() => {
  // 清理定时器
  vi.clearAllTimers();

  // 清理事件监听器
  vi.unstubAllEnvs();
});

// 全局测试清理
afterAll(() => {
  // 清理全局状态
  vi.clearAllMocks();
  vi.resetAllMocks();
});

// 扩展 Vitest 的全局类型
declare global {
  const vi: typeof import('vitest').vi;
  const expect: typeof import('vitest').expect;
  const describe: typeof import('vitest').describe;
  const it: typeof import('vitest').it;
  const test: typeof import('vitest').test;
  const beforeAll: typeof import('vitest').beforeAll;
  const afterAll: typeof import('vitest').afterAll;
  const beforeEach: typeof import('vitest').beforeEach;
  const afterEach: typeof import('vitest').afterEach;
}
