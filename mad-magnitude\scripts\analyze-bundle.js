#!/usr/bin/env node

import { readdirSync, statSync } from 'fs';
import { dirname, extname, join } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * 分析构建产物大小和性能
 */
class BundleAnalyzer {
  constructor() {
    this.distPath = join(__dirname, '../dist');
    this.results = {
      totalSize: 0,
      files: [],
      chunks: {
        js: [],
        css: [],
        images: [],
        fonts: [],
        other: []
      },
      recommendations: []
    };
  }

  /**
   * 格式化文件大小
   */
  formatSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 递归分析目录
   */
  analyzeDirectory(dirPath, relativePath = '') {
    try {
      const items = readdirSync(dirPath);
      
      for (const item of items) {
        const fullPath = join(dirPath, item);
        const relativeFilePath = join(relativePath, item);
        const stats = statSync(fullPath);
        
        if (stats.isDirectory()) {
          this.analyzeDirectory(fullPath, relativeFilePath);
        } else {
          this.analyzeFile(fullPath, relativeFilePath, stats.size);
        }
      }
    } catch (error) {
      console.warn(`无法分析目录 ${dirPath}:`, error.message);
    }
  }

  /**
   * 分析单个文件
   */
  analyzeFile(filePath, relativePath, size) {
    this.results.totalSize += size;
    
    const fileInfo = {
      path: relativePath,
      size: size,
      formattedSize: this.formatSize(size),
      type: this.getFileType(relativePath)
    };
    
    this.results.files.push(fileInfo);
    
    // 按类型分类
    const ext = extname(relativePath).toLowerCase();
    if (['.js', '.mjs'].includes(ext)) {
      this.results.chunks.js.push(fileInfo);
    } else if (ext === '.css') {
      this.results.chunks.css.push(fileInfo);
    } else if (['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp', '.avif'].includes(ext)) {
      this.results.chunks.images.push(fileInfo);
    } else if (['.woff', '.woff2', '.ttf', '.otf', '.eot'].includes(ext)) {
      this.results.chunks.fonts.push(fileInfo);
    } else {
      this.results.chunks.other.push(fileInfo);
    }
  }

  /**
   * 获取文件类型
   */
  getFileType(filePath) {
    const ext = extname(filePath).toLowerCase();
    const typeMap = {
      '.js': 'JavaScript',
      '.mjs': 'JavaScript Module',
      '.css': 'CSS',
      '.png': 'PNG Image',
      '.jpg': 'JPEG Image',
      '.jpeg': 'JPEG Image',
      '.gif': 'GIF Image',
      '.svg': 'SVG Image',
      '.webp': 'WebP Image',
      '.avif': 'AVIF Image',
      '.woff': 'WOFF Font',
      '.woff2': 'WOFF2 Font',
      '.ttf': 'TrueType Font',
      '.otf': 'OpenType Font',
      '.html': 'HTML',
      '.ico': 'Icon'
    };
    return typeMap[ext] || 'Other';
  }

  /**
   * 生成优化建议
   */
  generateRecommendations() {
    const recommendations = [];
    
    // JavaScript 文件大小检查
    const largeJsFiles = this.results.chunks.js.filter(file => file.size > 100 * 1024); // > 100KB
    if (largeJsFiles.length > 0) {
      recommendations.push({
        type: 'warning',
        category: 'JavaScript',
        message: `发现 ${largeJsFiles.length} 个大型 JavaScript 文件 (>100KB)`,
        files: largeJsFiles.map(f => `${f.path} (${f.formattedSize})`),
        suggestion: '考虑进一步代码分割或移除未使用的代码'
      });
    }

    // CSS 文件大小检查
    const largeCssFiles = this.results.chunks.css.filter(file => file.size > 50 * 1024); // > 50KB
    if (largeCssFiles.length > 0) {
      recommendations.push({
        type: 'warning',
        category: 'CSS',
        message: `发现 ${largeCssFiles.length} 个大型 CSS 文件 (>50KB)`,
        files: largeCssFiles.map(f => `${f.path} (${f.formattedSize})`),
        suggestion: '考虑 CSS 代码分割或移除未使用的样式'
      });
    }

    // 图片优化检查
    const largeImages = this.results.chunks.images.filter(file => file.size > 200 * 1024); // > 200KB
    if (largeImages.length > 0) {
      recommendations.push({
        type: 'info',
        category: 'Images',
        message: `发现 ${largeImages.length} 个大型图片文件 (>200KB)`,
        files: largeImages.map(f => `${f.path} (${f.formattedSize})`),
        suggestion: '考虑使用 WebP/AVIF 格式或进一步压缩图片'
      });
    }

    // 总体大小检查
    if (this.results.totalSize > 5 * 1024 * 1024) { // > 5MB
      recommendations.push({
        type: 'warning',
        category: 'Overall',
        message: `总构建大小较大: ${this.formatSize(this.results.totalSize)}`,
        suggestion: '考虑启用 gzip/brotli 压缩，或进一步优化资源'
      });
    }

    // 文件数量检查
    if (this.results.files.length > 50) {
      recommendations.push({
        type: 'info',
        category: 'Files',
        message: `文件数量较多: ${this.results.files.length} 个文件`,
        suggestion: '考虑合并小文件或使用 HTTP/2 推送'
      });
    }

    this.results.recommendations = recommendations;
  }

  /**
   * 打印分析结果
   */
  printResults() {
    console.log('\n🔍 构建产物分析报告');
    console.log('='.repeat(50));
    
    // 总体信息
    console.log(`\n📊 总体信息:`);
    console.log(`   总大小: ${this.formatSize(this.results.totalSize)}`);
    console.log(`   文件数量: ${this.results.files.length}`);
    
    // 按类型统计
    console.log(`\n📁 文件类型统计:`);
    const categories = [
      { name: 'JavaScript', files: this.results.chunks.js },
      { name: 'CSS', files: this.results.chunks.css },
      { name: 'Images', files: this.results.chunks.images },
      { name: 'Fonts', files: this.results.chunks.fonts },
      { name: 'Other', files: this.results.chunks.other }
    ];
    
    categories.forEach(category => {
      if (category.files.length > 0) {
        const totalSize = category.files.reduce((sum, file) => sum + file.size, 0);
        console.log(`   ${category.name}: ${category.files.length} 个文件, ${this.formatSize(totalSize)}`);
      }
    });
    
    // 最大的文件
    console.log(`\n📈 最大的文件 (前10个):`);
    const sortedFiles = [...this.results.files].sort((a, b) => b.size - a.size).slice(0, 10);
    sortedFiles.forEach((file, index) => {
      console.log(`   ${index + 1}. ${file.path} - ${file.formattedSize} (${file.type})`);
    });
    
    // 优化建议
    if (this.results.recommendations.length > 0) {
      console.log(`\n💡 优化建议:`);
      this.results.recommendations.forEach((rec, index) => {
        const icon = rec.type === 'warning' ? '⚠️' : 'ℹ️';
        console.log(`   ${icon} ${rec.category}: ${rec.message}`);
        console.log(`      建议: ${rec.suggestion}`);
        if (rec.files && rec.files.length > 0) {
          console.log(`      相关文件:`);
          rec.files.forEach(file => console.log(`        - ${file}`));
        }
        console.log('');
      });
    } else {
      console.log(`\n✅ 构建产物大小合理，无需特别优化`);
    }
    
    console.log('='.repeat(50));
  }

  /**
   * 运行分析
   */
  async run() {
    try {
      console.log('🚀 开始分析构建产物...');
      
      // 检查 dist 目录是否存在
      try {
        statSync(this.distPath);
      } catch (error) {
        console.error('❌ 未找到 dist 目录，请先运行构建命令');
        process.exit(1);
      }
      
      // 分析文件
      this.analyzeDirectory(this.distPath);
      
      // 生成建议
      this.generateRecommendations();
      
      // 打印结果
      this.printResults();
      
      console.log('✅ 分析完成');
      
    } catch (error) {
      console.error('❌ 分析过程中出现错误:', error.message);
      process.exit(1);
    }
  }
}

// 运行分析
const analyzer = new BundleAnalyzer();
analyzer.run();