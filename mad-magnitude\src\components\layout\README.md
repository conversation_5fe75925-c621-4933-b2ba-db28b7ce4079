# Enhanced Layout Components

Comprehensive responsive layout system for Pennfly Private Academy with advanced
breakpoint management and flexible grid options.

## Components

### Container

Enhanced responsive container with comprehensive padding and width options.

```astro
---
import Container from '../components/layout/Container.astro';
---

<!-- Basic container -->
<Container maxWidth="7xl" paddingX="md">
  <h1>Content goes here</h1>
</Container>

<!-- Responsive container -->
<Container
  maxWidth="sm"
  mdMaxWidth="lg"
  xlMaxWidth="2xl"
  paddingX="sm"
  mdPaddingX="md"
  xlPaddingX="lg"
>
  <p>Responsive content</p>
</Container>
```

**Props:**

- `maxWidth`: Container max width with responsive variants
- `paddingX/paddingY`: Padding with responsive variants
- `centered`: Center alignment (default: true)
- `fluid`: No max-width constraint
- `fullHeight`: Full viewport height

### Grid

Flexible CSS Grid layout with comprehensive responsive options.

```astro
---
import Grid from '../components/layout/Grid.astro';
import GridItem from '../components/layout/GridItem.astro';
---

<!-- Responsive grid -->
<Grid cols={1} smCols={2} mdCols={3} lgCols={4} gap="md">
  <GridItem>Item 1</GridItem>
  <GridItem colSpan={2}>Spans 2 columns</GridItem>
  <GridItem>Item 3</GridItem>
</Grid>
```

**Grid Props:**

- `cols`: Number of columns with responsive variants
- `gap`: Grid gap with responsive variants
- `itemsAlign`: Items alignment
- `contentAlign`: Content alignment

**GridItem Props:**

- `colSpan`: Column span with responsive variants
- `rowSpan`: Row span
- `colStart/colEnd`: Column positioning
- `rowStart/rowEnd`: Row positioning

### Flex

Enhanced Flexbox layout with comprehensive responsive control.

```astro
---
import Flex from '../components/layout/Flex.astro';
---

<!-- Responsive flex -->
<Flex
  direction="col"
  lgDirection="row"
  gap="md"
  justify="between"
  align="center"
>
  <div>Item 1</div>
  <div>Item 2</div>
  <div>Item 3</div>
</Flex>
```

**Props:**

- `direction`: Flex direction with responsive variants
- `wrap`: Flex wrap with responsive variants
- `justify`: Justify content with responsive variants
- `align`: Align items with responsive variants
- `gap`: Gap with responsive variants
- `fullWidth/fullHeight`: Size constraints

## Responsive Breakpoints

Enhanced breakpoint system with device-specific and orientation breakpoints:

```javascript
// Standard breakpoints
xs: '475px',    // Large phones
sm: '640px',    // Small tablets
md: '768px',    // Tablets
lg: '1024px',   // Small desktops
xl: '1280px',   // Desktops
'2xl': '1536px', // Large desktops
'3xl': '1920px', // Ultra-wide screens

// Special breakpoints
'xs-only': { 'max': '639px' },     // Only small screens
'mobile': { 'max': '767px' },      // Mobile devices
'tablet': { 'min': '768px', 'max': '1023px' }, // Tablet devices
'desktop': { 'min': '1024px' },    // Desktop devices
'portrait': { 'raw': '(orientation: portrait)' },
'landscape': { 'raw': '(orientation: landscape)' },
```

## Responsive Utilities

JavaScript utilities for responsive behavior:

```javascript
import { responsive } from '../utils/responsive';

// Get current breakpoint
const currentBp = responsive.getCurrentBreakpoint(); // 'xs' | 'sm' | 'md' | etc.

// Check breakpoint matches
const isMobile = responsive.isMobile(); // below md
const isTablet = responsive.isTablet(); // md to lg
const isDesktop = responsive.isDesktop(); // lg and above

// Listen for breakpoint changes
const cleanup = responsive.onBreakpointChange(breakpoint => {
  console.log('Breakpoint changed to:', breakpoint);
});

// Get responsive values
const columns = responsive.getResponsiveValue(
  {
    xs: 1,
    sm: 2,
    md: 3,
    lg: 4,
  },
  2
); // fallback value

// Device detection
const isTouchDevice = responsive.device.isTouchDevice();
const isPortrait = responsive.device.isPortrait();
const isHighDPI = responsive.device.isHighDPI();
```

## Usage Examples

### Article Layout with Sidebar

```astro
<Container maxWidth="7xl" paddingX="md">
  <Grid cols={1} lgCols={4} gap="lg">
    <!-- Main content -->
    <GridItem colSpan={1} lgColSpan={3}>
      <article>Main article content</article>
    </GridItem>

    <!-- Sidebar -->
    <GridItem colSpan={1}>
      <aside>Sidebar content</aside>
    </GridItem>
  </Grid>
</Container>
```

### Card Grid

```astro
<Container>
  <Grid cols={1} smCols={2} mdCols={3} xlCols={4} gap="md">
    {
      cards.map(card => (
        <Card padding="md">
          <h3>{card.title}</h3>
          <p>{card.description}</p>
        </Card>
      ))
    }
  </Grid>
</Container>
```

### Navigation Layout

```astro
<Container fluid paddingX="md">
  <Flex justify="between" align="center" class="h-16">
    <!-- Logo -->
    <div>
      <img src="/logo.svg" alt="Logo" />
    </div>

    <!-- Navigation -->
    <Flex direction="col" mdDirection="row" gap="sm" mdGap="lg" class="md:flex">
      <a href="/home">Home</a>
      <a href="/about">About</a>
      <a href="/contact">Contact</a>
    </Flex>
  </Flex>
</Container>
```

## Best Practices

### Mobile-First Design

Always start with mobile layouts and enhance for larger screens:

```astro
<!-- Good: Mobile-first approach -->
<Grid cols={1} smCols={2} lgCols={3} gap="sm" lgGap="lg">
  <!-- Content -->
</Grid>

<!-- Avoid: Desktop-first approach -->
<Grid cols={3} smCols={1} gap="lg" smGap="sm">
  <!-- Content -->
</Grid>
```

### Performance Considerations

- Use appropriate container sizes to avoid unnecessary wide layouts
- Consider content hierarchy when choosing grid vs flex
- Use responsive images with the image utilities
- Test on actual devices, not just browser resize

### Accessibility

- Ensure proper heading hierarchy regardless of visual layout
- Test keyboard navigation across all breakpoints
- Verify screen reader compatibility with layout changes
- Maintain logical tab order

## Testing

Visit `/responsive-grid-test` to see all layout components in action with
various responsive configurations and real-world examples.

## Integration with Design System

All layout components integrate seamlessly with the design system:

- Use theme-aware spacing and colors
- Support reduced motion preferences
- Include print-friendly styles
- Work with high contrast modes
- Respect user's accessibility preferences
