---
import { getCollection } from 'astro:content';

// 获取各类内容的统计数据
const newsCount = (await getCollection('news')).filter((item: any) => !item.data.draft).length;
const logsCount = (await getCollection('logs')).filter((item: any) => !item.data.draft).length;
const researchCount = (await getCollection('research')).filter(
  (item: any) => !item.data.draft
).length;
const economicsCount = (await getCollection('economics')).filter(
  (item: any) => !item.data.draft
).length;
const philosophyCount = (await getCollection('philosophy')).filter(
  (item: any) => !item.data.draft
).length;
const aiCount = (await getCollection('ai')).filter((item: any) => !item.data.draft).length;

// 计算总数
const totalArticles = newsCount + researchCount + economicsCount + philosophyCount + aiCount;

// 获取所有标签
const allCollections = await Promise.all([
  getCollection('news'),
  getCollection('research'),
  getCollection('economics'),
  getCollection('philosophy'),
  getCollection('ai'),
  getCollection('logs'),
]);

const allTags = new Set<string>();
allCollections.flat().forEach((item: any) => {
  if (item.data.tags) {
    item.data.tags.forEach((tag: string) => allTags.add(tag));
  }
});

const totalTags = allTags.size;

// 计算最近更新时间
const allDates = allCollections
  .flat()
  .map((item: any) => item.data.publishDate || item.data.date)
  .filter((date: any) => date)
  .sort((a: any, b: any) => new Date(b).getTime() - new Date(a).getTime());

const lastUpdate = allDates[0];

// 统计数据
const stats = [
  {
    icon: '📚',
    label: '研究文章',
    value: totalArticles,
    description: '深度研究与思考',
  },
  {
    icon: '📔',
    label: '研究日志',
    value: logsCount,
    description: '日常思考记录',
  },
  {
    icon: '🏷️',
    label: '内容标签',
    value: totalTags,
    description: '知识分类体系',
  },
  {
    icon: '🔄',
    label: '最近更新',
    value: lastUpdate ? new Date(lastUpdate).toLocaleDateString('zh-CN') : '暂无',
    description: '持续更新中',
  },
];
---

<section class="mb-16">
  <div class="rounded-lg bg-gradient-to-r from-slate-50 to-blue-50 p-8">
    <h3 class="mb-8 text-center text-2xl font-bold text-slate-800">研究院概况</h3>

    <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
      {
        stats.map(stat => (
          <div class="text-center">
            <div class="mb-3 text-4xl">{stat.icon}</div>
            <div class="mb-2 text-2xl font-bold text-slate-800">{stat.value}</div>
            <div class="mb-1 font-medium text-slate-700">{stat.label}</div>
            <div class="text-sm text-slate-600">{stat.description}</div>
          </div>
        ))
      }
    </div>

    <!-- 研究所活跃度 -->
    <div class="mt-8 border-t border-slate-200 pt-8">
      <h4 class="mb-4 text-center text-lg font-semibold text-slate-800">研究所活跃度</h4>
      <div class="grid gap-4 md:grid-cols-3 lg:grid-cols-5">
        <div class="text-center">
          <div class="mb-2 text-2xl">💰</div>
          <div class="text-sm font-medium text-slate-700">经济研究所</div>
          <div class="text-xs text-slate-600">{economicsCount} 篇文章</div>
        </div>
        <div class="text-center">
          <div class="mb-2 text-2xl">🤔</div>
          <div class="text-sm font-medium text-slate-700">哲学研究所</div>
          <div class="text-xs text-slate-600">{philosophyCount} 篇文章</div>
        </div>
        <div class="text-center">
          <div class="mb-2 text-2xl">🌐</div>
          <div class="text-sm font-medium text-slate-700">互联网研究所</div>
          <div class="text-xs text-slate-600">0 篇文章</div>
        </div>
        <div class="text-center">
          <div class="mb-2 text-2xl">🤖</div>
          <div class="text-sm font-medium text-slate-700">AI研究所</div>
          <div class="text-xs text-slate-600">{aiCount} 篇文章</div>
        </div>
        <div class="text-center">
          <div class="mb-2 text-2xl">🔮</div>
          <div class="text-sm font-medium text-slate-700">未来研究所</div>
          <div class="text-xs text-slate-600">0 篇文章</div>
        </div>
      </div>
    </div>
  </div>
</section>
