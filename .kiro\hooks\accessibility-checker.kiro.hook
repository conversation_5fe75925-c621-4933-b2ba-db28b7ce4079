{"enabled": true, "name": "可访问性检查器", "description": "检查组件和页面的可访问性合规性", "version": "1", "when": {"type": "manual"}, "then": {"type": "askAgent", "prompt": "请对当前项目进行全面的可访问性检查：\n\n1. **WCAG 2.1 AA 标准检查**：\n   - 颜色对比度是否 ≥ 4.5:1\n   - 所有交互元素是否可键盘访问\n   - 是否有跳转链接\n   - 焦点指示器是否清晰可见\n\n2. **语义化 HTML**：\n   - 是否使用了正确的标题层级\n   - 表单元素是否有关联标签\n   - 列表和表格结构是否正确\n   - 是否使用了适当的 landmark 元素\n\n3. **ARIA 标签使用**：\n   - 复杂组件是否有适当的 ARIA 标签\n   - 动态内容是否有 live regions\n   - 是否正确使用了 aria-label 和 aria-describedby\n   - 隐藏内容是否正确标记\n\n4. **图片和媒体**：\n   - 所有图片是否有描述性 alt 文本\n   - 装饰性图片是否标记为 alt=\"\"\n   - 视频是否有字幕\n   - 音频是否有文字描述\n\n5. **表单可访问性**：\n   - 表单字段是否有清晰标签\n   - 错误信息是否与字段关联\n   - 必填字段是否明确标识\n   - 是否有表单验证反馈\n\n6. **导航可访问性**：\n   - 导航结构是否清晰\n   - 是否有站点地图\n   - 面包屑导航是否完整\n   - 搜索功能是否可访问\n\n请提供详细的检查报告和具体的改进建议，包括代码修改示例。"}}