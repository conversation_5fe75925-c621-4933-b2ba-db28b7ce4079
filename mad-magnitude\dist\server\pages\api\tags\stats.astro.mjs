import { g as globalTagManager } from "../../../assets/utils.bIDOeBqD.js";
import { i } from "../../../assets/vendor-astro.Dc6apy9i.js";
const GET = async ({ url }) => {
  try {
    const searchParams = new URL(url).searchParams;
    const category = searchParams.get("category");
    const includeRelated = searchParams.get("includeRelated") === "true";
    const limit = parseInt(searchParams.get("limit") || "20");
    const tagStats = await globalTagManager.getTagStats();
    const response = {
      totalTags: tagStats.totalTags,
      totalUniqueContent: tagStats.totalUniqueContent,
      categories: Object.keys(tagStats.tagsByCategory).map((cat) => ({
        name: cat,
        displayName: getCategoryDisplayName(cat),
        count: tagStats.tagsByCategory[cat].length,
        totalUsage: tagStats.tagsByCategory[cat].reduce((sum, tag) => sum + tag.count, 0),
        topTag: tagStats.tagsByCategory[cat][0]?.name || null
      })),
      recentTags: tagStats.recentTags.slice(0, 10)
    };
    if (category && category !== "all" && tagStats.tagsByCategory[category]) {
      const categoryTags = tagStats.tagsByCategory[category].slice(0, limit);
      response.categoryDetails = {
        name: category,
        displayName: getCategoryDisplayName(category),
        tags: categoryTags.map((tag) => ({
          name: tag.name,
          count: tag.count,
          color: tag.color,
          relatedTags: includeRelated ? tag.relatedTags.slice(0, 3) : [],
          url: `/tags/${encodeURIComponent(tag.name)}`
        }))
      };
    } else {
      response.popularTags = tagStats.mostPopularTags.slice(0, limit).map((tag) => ({
        name: tag.name,
        count: tag.count,
        category: tag.category,
        color: tag.color,
        relatedTags: includeRelated ? tag.relatedTags.slice(0, 3) : [],
        url: `/tags/${encodeURIComponent(tag.name)}`
      }));
    }
    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        "Cache-Control": "public, max-age=600"
        // 缓存10分钟
      }
    });
  } catch (error) {
    console.error("标签统计 API 错误:", error);
    return new Response(
      JSON.stringify({
        error: "Internal server error",
        code: "INTERNAL_ERROR"
      }),
      {
        status: 500,
        headers: {
          "Content-Type": "application/json"
        }
      }
    );
  }
};
function getCategoryDisplayName(category) {
  const names = {
    technology: "技术",
    economics: "经济",
    philosophy: "哲学",
    society: "社会",
    research: "研究",
    tools: "工具",
    general: "通用"
  };
  return names[category] || category;
}
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({ __proto__: null, GET }, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  i as renderers
};
