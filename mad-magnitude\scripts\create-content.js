#!/usr/bin/env node

/**
 * 内容创建脚本
 * 用法: node scripts/create-content.js <type> <title> [institute]
 * 示例: node scripts/create-content.js research "新的经济分析" economics
 */

const fs = require('fs');
const path = require('path');

const contentTypes = {
  research: 'src/content/research',
  log: 'src/content/logs',
  news: 'src/content/news',
  product: 'src/content/products',
};

const institutes = ['economics', 'philosophy', 'internet', 'ai', 'future'];

function slugify(text) {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // 移除特殊字符
    .replace(/[\s_-]+/g, '-') // 替换空格和下划线为连字符
    .replace(/^-+|-+$/g, ''); // 移除开头和结尾的连字符
}

function getCurrentDate() {
  return new Date().toISOString().split('T')[0];
}

function createContent(type, title, institute) {
  // 验证参数
  if (!contentTypes[type]) {
    console.error(`❌ 错误：不支持的内容类型 "${type}"`);
    console.log(`支持的类型：${Object.keys(contentTypes).join(', ')}`);
    return;
  }

  if (type === 'research' && !institutes.includes(institute)) {
    console.error(`❌ 错误：不支持的研究所 "${institute}"`);
    console.log(`支持的研究所：${institutes.join(', ')}`);
    return;
  }

  // 读取模板
  const templatePath = `src/content/templates/${type}-template.md`;
  if (!fs.existsSync(templatePath)) {
    console.error(`❌ 错误：模板文件不存在 "${templatePath}"`);
    return;
  }

  const template = fs.readFileSync(templatePath, 'utf8');

  // 替换模板变量
  const content = template
    .replace(/{{title\.zh}}/g, title)
    .replace(/{{title}}/g, title)
    .replace(/{{date}}/g, getCurrentDate())
    .replace(/{{institute}}/g, institute || 'general')
    .replace(/{{updateDate}}/g, getCurrentDate());

  // 生成文件名
  const fileName = slugify(title) + '.md';

  // 确定文件路径
  let filePath;
  if (type === 'research' && institute) {
    const instituteDir = path.join(contentTypes[type], institute);
    if (!fs.existsSync(instituteDir)) {
      fs.mkdirSync(instituteDir, { recursive: true });
    }
    filePath = path.join(instituteDir, fileName);
  } else {
    const contentDir = contentTypes[type];
    if (!fs.existsSync(contentDir)) {
      fs.mkdirSync(contentDir, { recursive: true });
    }
    filePath = path.join(contentDir, fileName);
  }

  // 检查文件是否已存在
  if (fs.existsSync(filePath)) {
    console.error(`❌ 错误：文件已存在 "${filePath}"`);
    return;
  }

  // 写入文件
  fs.writeFileSync(filePath, content);
  console.log(`✅ 成功创建：${filePath}`);

  // 提供下一步建议
  console.log(`\n📝 下一步：`);
  console.log(`1. 编辑文件：code "${filePath}"`);
  console.log(`2. 预览网站：npm run dev`);
  console.log(
    `3. 提交更改：git add . && git commit -m "feat: 新增${type === 'research' ? '研究文章' : type === 'log' ? '研究日志' : '内容'} - ${title}"`
  );
}

function showHelp() {
  console.log(`
📝 内容创建工具

用法：
  node scripts/create-content.js <type> <title> [institute]

参数：
  type      内容类型 (${Object.keys(contentTypes).join(', ')})
  title     内容标题
  institute 研究所 (仅research类型需要: ${institutes.join(', ')})

示例：
  node scripts/create-content.js research "人工智能的未来发展" ai
  node scripts/create-content.js log "今日思考记录"
  node scripts/create-content.js news "研究院最新动态"

选项：
  -h, --help    显示帮助信息
`);
}

// 主程序
function main() {
  const args = process.argv.slice(2);

  if (args.length === 0 || args.includes('-h') || args.includes('--help')) {
    showHelp();
    return;
  }

  const [type, title, institute] = args;

  if (!type || !title) {
    console.error('❌ 错误：缺少必要参数');
    showHelp();
    return;
  }

  createContent(type, title, institute);
}

main();
