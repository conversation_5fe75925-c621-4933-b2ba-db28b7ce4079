/**
 * 测试工具函数
 *
 * 提供常用的测试辅助函数和断言工具
 */

import type { MockedFunction } from 'vitest';
import { expect } from 'vitest';

/**
 * URL 验证工具
 */
export function expectToBeValidUrl(url: string): void {
  expect(() => new URL(url)).not.toThrow();
  expect(url).toMatch(/^https?:\/\/.+/);
}

/**
 * HTML 结构验证工具
 */
export function expectToHaveValidHtml(html: string): void {
  const trimmedHtml = html.trim();
  expect(trimmedHtml).toMatch(/^<!DOCTYPE html>/i);
  expect(trimmedHtml).toContain('<html');
  expect(trimmedHtml).toContain('</html>');
  expect(trimmedHtml).toContain('<head');
  expect(trimmedHtml).toContain('</head>');
  expect(trimmedHtml).toContain('<body');
  expect(trimmedHtml).toContain('</body>');
}

/**
 * 检查元素是否具有有效的可访问性属性
 */
export function expectToHaveAccessibilityAttributes(element: Element): void {
  // 检查是否有适当的 ARIA 属性或语义标签
  const hasAriaLabel = element.hasAttribute('aria-label');
  const hasAriaLabelledBy = element.hasAttribute('aria-labelledby');
  const hasAriaDescribedBy = element.hasAttribute('aria-describedby');
  const isSemanticElement = [
    'button',
    'a',
    'input',
    'textarea',
    'select',
    'h1',
    'h2',
    'h3',
    'h4',
    'h5',
    'h6',
  ].includes(element.tagName.toLowerCase());

  expect(hasAriaLabel || hasAriaLabelledBy || hasAriaDescribedBy || isSemanticElement).toBe(true);
}

/**
 * 检查响应式设计类名
 */
export function expectToHaveResponsiveClasses(element: Element): void {
  const classList = Array.from(element.classList);
  const hasResponsiveClasses = classList.some(
    className =>
      className.includes('sm:') ||
      className.includes('md:') ||
      className.includes('lg:') ||
      className.includes('xl:') ||
      className.includes('2xl:')
  );

  expect(hasResponsiveClasses).toBe(true);
}

/**
 * 模拟异步操作
 */
export async function waitForAsync(ms: number = 0): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 创建模拟的 Astro 组件 props
 */
export function createMockAstroProps<T extends Record<string, any>>(props: T): T {
  return {
    ...props,
    'data-testid': 'mock-component',
  };
}

/**
 * 模拟 fetch 响应
 */
export function mockFetchResponse<T>(data: T, status: number = 200): MockedFunction<typeof fetch> {
  return vi.fn().mockResolvedValue({
    ok: status >= 200 && status < 300,
    status,
    statusText: status === 200 ? 'OK' : 'Error',
    json: () => Promise.resolve(data),
    text: () => Promise.resolve(JSON.stringify(data)),
    headers: new Headers({ 'Content-Type': 'application/json' }),
  } as Response);
}

/**
 * 模拟错误的 fetch 响应
 */
export function mockFetchError(error: string): MockedFunction<typeof fetch> {
  return vi.fn().mockRejectedValue(new Error(error));
}

/**
 * 创建测试用的 DOM 元素
 */
export function createTestElement(
  tag: string = 'div',
  attributes: Record<string, string> = {}
): HTMLElement {
  const element = document.createElement(tag);

  Object.entries(attributes).forEach(([key, value]) => {
    element.setAttribute(key, value);
  });

  return element;
}

/**
 * 模拟 localStorage
 */
export function mockLocalStorage(): Storage {
  const store: Record<string, string> = {};

  return {
    getItem: vi.fn((key: string) => store[key] || null),
    setItem: vi.fn((key: string, value: string) => {
      store[key] = value;
    }),
    removeItem: vi.fn((key: string) => {
      delete store[key];
    }),
    clear: vi.fn(() => {
      Object.keys(store).forEach(key => delete store[key]);
    }),
    key: vi.fn((index: number) => Object.keys(store)[index] || null),
    get length() {
      return Object.keys(store).length;
    },
  };
}

/**
 * 模拟 sessionStorage
 */
export function mockSessionStorage(): Storage {
  return mockLocalStorage(); // 相同的实现
}

/**
 * 检查函数是否被正确调用
 */
export function expectFunctionToHaveBeenCalledWith<T extends any[]>(
  mockFn: MockedFunction<(..._args: T) => any>,
  ...expectedArgs: T
): void {
  expect(mockFn).toHaveBeenCalledWith(...expectedArgs);
}

/**
 * 检查异步函数的执行结果
 */
export async function expectAsyncToResolve<T>(
  asyncFn: () => Promise<T>,
  expectedValue?: T
): Promise<void> {
  const result = await asyncFn();

  if (expectedValue !== undefined) {
    expect(result).toEqual(expectedValue);
  } else {
    expect(result).toBeDefined();
  }
}

/**
 * 检查异步函数是否抛出错误
 */
export async function expectAsyncToReject(
  asyncFn: () => Promise<any>,
  expectedError?: string | RegExp
): Promise<void> {
  await expect(asyncFn()).rejects.toThrow(expectedError);
}

/**
 * 创建测试用的时间戳
 */
export function createTestTimestamp(offset: number = 0): Date {
  return new Date(Date.now() + offset);
}

/**
 * 模拟用户交互事件
 */
export function createMockEvent(type: string, properties: Partial<Event> = {}): Event {
  const event = new Event(type, { bubbles: true, cancelable: true });

  Object.assign(event, properties);

  return event;
}

/**
 * 等待 DOM 更新
 */
export async function waitForDOMUpdate(): Promise<void> {
  await new Promise(resolve => setTimeout(resolve, 0));
}

/**
 * 检查元素是否在视口中
 */
export function expectElementToBeInViewport(element: Element): void {
  const rect = element.getBoundingClientRect();
  const isInViewport =
    rect.top >= 0 &&
    rect.left >= 0 &&
    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
    rect.right <= (window.innerWidth || document.documentElement.clientWidth);

  expect(isInViewport).toBe(true);
}
