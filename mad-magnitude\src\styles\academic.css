/* 学术风格样式 - 优化版本 */
.academic-content {
  max-width: none;

  /* 段落和文本样式 */
  p {
    margin-bottom: 1rem;
    line-height: 1.625;
    color: rgb(55 65 81);
    text-align: justify;
    text-justify: inter-ideograph;
  }

  /* 标题样式 */
  h1, h2, h3, h4, h5, h6 {
    margin-top: 2rem;
    margin-bottom: 1rem;
    font-weight: 700;
    color: rgb(17 24 39);
    line-height: 1.3;
  }

  h1 { margin-bottom: 1.5rem; font-size: 1.875rem; }
  h2 { margin-bottom: 1.25rem; font-size: 1.5rem; }
  h3 { margin-bottom: 1rem; font-size: 1.25rem; }
  h4 { margin-bottom: 0.75rem; font-size: 1.125rem; }
  h5 { margin-bottom: 0.5rem; font-size: 1rem; }
  h6 { margin-bottom: 0.5rem; font-size: 0.875rem; }

  /* 列表样式 */
  ul, ol {
    margin: 1rem 0;
    padding-left: 1.5rem;
  }
  ul { list-style-type: disc; }
  ol { list-style-type: decimal; }
  li { margin: 0.25rem 0; }

  /* 引用样式 */
  blockquote {
    margin: 1.5rem 0;
    padding: 1rem 1.5rem;
    border-left: 4px solid rgb(59 130 246);
    background-color: rgb(239 246 255);
    font-style: italic;
  }

  /* 链接样式 */
  a {
    color: rgb(59 130 246);
    text-decoration: underline;
    transition: color 0.2s;
  }
  a:hover { color: rgb(37 99 235); }

  /* 强调样式 */
  strong, b { font-weight: 700; color: rgb(17 24 39); }
  em, i { font-style: italic; color: rgb(55 65 81); }

  /* 代码样式 */
  code {
    padding: 0.125rem 0.25rem;
    background-color: rgb(243 244 246);
    border-radius: 0.25rem;
    font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
    font-size: 0.875rem;
    color: rgb(220 38 127);
  }

  pre {
    margin: 1.5rem 0;
    padding: 1rem;
    background-color: rgb(31 41 55);
    border-radius: 0.5rem;
    overflow-x: auto;
    position: relative;
  }

  pre code {
    background-color: transparent;
    color: rgb(243 244 246);
    padding: 0;
  }

  /* 代码块语言标签 */
  pre[class*='language-']::before {
    content: attr(data-language);
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    font-size: 0.75rem;
    color: rgb(156 163 175);
    text-transform: uppercase;
  }

  /* 表格样式 */
  table {
    width: 100%;
    border-collapse: collapse;
    margin: 1.5rem 0;
  }

  th, td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid rgb(229 231 235);
  }

  th {
    background-color: rgb(249 250 251);
    font-weight: 600;
  }

  /* 分隔线样式 */
  hr {
    margin: 2rem 0;
    border: none;
    border-top: 1px solid rgb(229 231 235);
  }

  /* 图片样式 */
  img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
    margin: 1rem 0;
  }

  /* 图片说明样式 */
  .image-caption {
    margin-top: 0.5rem;
    text-align: center;
    font-size: 0.875rem;
    color: rgb(75 85 99);
    font-style: italic;
  }

  /* 脚注样式 */
  .footnote {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid rgb(229 231 235);
    font-size: 0.875rem;
    color: rgb(75 85 99);
  }

  .footnote-ref {
    color: rgb(59 130 246);
    text-decoration: none;
  }

  .footnote-ref::before { content: '['; }
  .footnote-ref::after { content: ']'; }

  /* 参考文献样式 */
  .references {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 2px solid rgb(229 231 235);
  }

  .references h3 {
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1.25rem;
  }

  .references ol {
    padding-left: 1rem;
    font-size: 0.875rem;
  }

  .references ol li {
    margin-bottom: 0.5rem;
    line-height: 1.5;
  }
}

/* 目录导航样式 */
.table-of-contents {
  background-color: rgb(249 250 251);
  border: 1px solid rgb(229 231 235);
  border-radius: 0.5rem;
  padding: 1rem;
  margin: 1rem 0;
}

.table-of-contents h3 {
  margin-top: 0;
  margin-bottom: 0.75rem;
  font-size: 1rem;
  color: rgb(17 24 39);
}

.table-of-contents ul {
  list-style: none;
  padding-left: 0;
}

.table-of-contents ul li {
  margin: 0.25rem 0;
}

.table-of-contents ul li a {
  display: block;
  padding: 0.25rem 0.5rem;
  color: rgb(75 85 99);
  text-decoration: none;
  border-radius: 0.25rem;
  transition: all 0.2s;
  font-size: 0.875rem;
}

.table-of-contents ul li a:hover {
  background-color: rgb(59 130 246);
  color: white;
}

.table-of-contents ul li ul {
  margin-left: 1rem;
  margin-top: 0.25rem;
}

.table-of-contents ul li ul a {
  font-size: 0.8125rem;
}

/* 阅读进度指示器 */
.reading-progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: rgb(229 231 235);
  z-index: 1000;
}

.reading-progress .progress-bar {
  height: 100%;
  background-color: rgb(59 130 246);
  transition: width 0.1s ease;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .academic-content h1 { font-size: 1.5rem; }
  .academic-content h2 { font-size: 1.25rem; }
  .academic-content h3 { font-size: 1.125rem; }
  
  .academic-content pre {
    font-size: 0.8125rem;
  }

  .academic-content table {
    font-size: 0.875rem;
  }

  .table-of-contents {
    margin: 0.5rem 0;
  }
}

/* 打印样式 */
@media print {
  .academic-content {
    font-size: 12pt;
  }

  .academic-content a {
    color: black;
    text-decoration: none;
  }

  .academic-content a::after {
    content: ' (' attr(href) ')';
    font-size: 0.8em;
  }

  .academic-content pre {
    background-color: white;
    border: 1px solid black;
    color: black;
    page-break-inside: avoid;
  }

  .reading-progress,
  .table-of-contents {
    display: none;
  }
}
