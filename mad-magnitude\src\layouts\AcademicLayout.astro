---
import Layout from './Layout.astro';
import AcademicContent from '../components/academic/AcademicContent.astro';

export interface Props {
  title: string;
  description: string;
  showToc?: boolean;
  showProgress?: boolean;
  enableMermaid?: boolean;
  breadcrumbs?: Array<{
    name: string;
    href: string;
    icon?: string;
  }>;
  article?: {
    publishDate: Date;
    updateDate?: Date;
    readingTime?: number;
    tags?: string[];
    author?: string;
  };
}

const {
  title,
  description,
  showToc = true,
  showProgress = true,
  enableMermaid = true,
  breadcrumbs = [],
  article,
} = Astro.props;

// 简化的面包屑处理
const breadcrumbItems = breadcrumbs;
---

<Layout title={title} description={description}>
  <main class="min-h-screen bg-gray-50">
    <div class="container mx-auto px-6 py-8">
      <!-- 简化的面包屑导航 -->
      {
        breadcrumbItems.length > 0 && (
          <nav class="mb-6 text-sm text-gray-600">
            {breadcrumbItems.map((crumb, index) => (
              <span>
                {index > 0 && <span class="mx-2">›</span>}
                {index === breadcrumbItems.length - 1 ? (
                  <span class="font-medium text-gray-900">{crumb.name}</span>
                ) : (
                  <a href={crumb.href} class="transition-colors hover:text-blue-600">
                    {crumb.name}
                  </a>
                )}
              </span>
            ))}
          </nav>
        )
      }

      <!-- 文章头部信息 -->
      {
        article && (
          <header class="mb-8 rounded-lg border border-gray-200 bg-white p-8 shadow-sm">
            <h1 class="mb-4 text-4xl font-bold text-gray-900">{title}</h1>
            <p class="mb-6 text-xl text-gray-600">{description}</p>

            <div class="flex flex-wrap items-center gap-4 text-sm text-gray-500">
              <span>发布时间：{article.publishDate.toLocaleDateString('zh-CN')}</span>
              {article.updateDate && (
                <span>更新时间：{article.updateDate.toLocaleDateString('zh-CN')}</span>
              )}
              {article.readingTime && <span>阅读时间：{article.readingTime} 分钟</span>}
              {article.author && <span>作者：{article.author}</span>}
            </div>

            {article.tags && article.tags.length > 0 && (
              <div class="mt-4">
                <div class="mb-2 text-sm text-gray-500">标签：</div>
                <div class="flex flex-wrap gap-2">
                  {article.tags.map((tag: string) => (
                    <span class="rounded bg-blue-100 px-3 py-1 text-sm text-blue-800">{tag}</span>
                  ))}
                </div>
              </div>
            )}
          </header>
        )
      }

      <!-- 学术内容 -->
      <div class="rounded-lg border border-gray-200 bg-white shadow-sm">
        <AcademicContent
          showToc={showToc}
          showProgress={showProgress}
          enableMermaid={enableMermaid}
          class="p-8"
        >
          <slot />
        </AcademicContent>
      </div>

      <!-- 返回按钮 -->
      <div class="mt-8 text-center">
        <button
          onclick="history.back()"
          class="inline-block rounded-lg bg-gray-600 px-6 py-2 text-white transition-colors hover:bg-gray-700"
        >
          ← 返回上一页
        </button>
      </div>
    </div>
  </main>
</Layout>

<style>
  /* 确保学术内容样式正确加载 */
  @import '../styles/academic.css';
</style>
