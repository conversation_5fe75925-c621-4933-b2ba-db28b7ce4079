{"enabled": true, "name": "内容验证器", "description": "在保存内容文件时自动验证格式和质量", "version": "1", "when": {"type": "fileEdited", "patterns": ["mad-magnitude/src/content/**/*.md"]}, "then": {"type": "askAgent", "prompt": "请检查这个内容文件是否符合项目标准：\n\n1. 检查 frontmatter 格式是否正确\n2. 验证必需字段是否存在（title.zh, description.zh, publishDate等）\n3. 检查标签是否合适和一致\n4. 验证 markdown 语法\n5. 检查是否有拼写错误\n6. 确认数学公式和代码块格式正确\n7. 验证图片链接和 alt 文本\n\n如果发现问题，请提供具体的修改建议。如果一切正常，请确认内容质量良好。"}}