---
// 简单的管理后台首页
import { getCollection } from 'astro:content';
import Layout from '../../layouts/Layout.astro';

// 获取各个板块的内容统计
const newsCount = (await getCollection('news')).length;
const logsCount = (await getCollection('logs')).length;
const economicsCount = (await getCollection('economics')).length;
const philosophyCount = (await getCollection('philosophy')).length;
const internetCount = (await getCollection('internet')).length;
const aiCount = (await getCollection('ai')).length;
const futureCount = (await getCollection('future')).length;
const productsCount = (await getCollection('products')).length;

const totalContent =
  newsCount +
  logsCount +
  economicsCount +
  philosophyCount +
  internetCount +
  aiCount +
  futureCount +
  productsCount;
---

<Layout title="管理后台 - Pennfly Private Academy">
  <main class="min-h-screen bg-gray-50 py-8">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <!-- 页面标题 -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">管理后台</h1>
        <p class="mt-2 text-gray-600">Pennfly Private Academy 内容管理系统</p>
      </div>

      <!-- 统计卡片 -->
      <div class="mb-8 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
        <div class="rounded-lg bg-white p-6 shadow">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="flex h-8 w-8 items-center justify-center rounded-full bg-blue-500">
                <span class="text-sm font-medium text-white">📊</span>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">总内容数</p>
              <p class="text-2xl font-semibold text-gray-900">{totalContent}</p>
            </div>
          </div>
        </div>

        <div class="rounded-lg bg-white p-6 shadow">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="flex h-8 w-8 items-center justify-center rounded-full bg-green-500">
                <span class="text-sm font-medium text-white">📰</span>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">动态资讯</p>
              <p class="text-2xl font-semibold text-gray-900">{newsCount}</p>
            </div>
          </div>
        </div>

        <div class="rounded-lg bg-white p-6 shadow">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="flex h-8 w-8 items-center justify-center rounded-full bg-purple-500">
                <span class="text-sm font-medium text-white">📔</span>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">研究日志</p>
              <p class="text-2xl font-semibold text-gray-900">{logsCount}</p>
            </div>
          </div>
        </div>

        <div class="rounded-lg bg-white p-6 shadow">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="flex h-8 w-8 items-center justify-center rounded-full bg-orange-500">
                <span class="text-sm font-medium text-white">🛠️</span>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">开发项目</p>
              <p class="text-2xl font-semibold text-gray-900">{productsCount}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 研究所统计 -->
      <div class="mb-8 rounded-lg bg-white shadow">
        <div class="border-b border-gray-200 px-6 py-4">
          <h2 class="text-lg font-medium text-gray-900">研究所内容统计</h2>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            <div class="flex items-center justify-between rounded-lg bg-gray-50 p-4">
              <div class="flex items-center">
                <span class="mr-3 text-2xl">💰</span>
                <span class="font-medium">经济研究所</span>
              </div>
              <span class="text-lg font-semibold text-blue-600">{economicsCount}</span>
            </div>

            <div class="flex items-center justify-between rounded-lg bg-gray-50 p-4">
              <div class="flex items-center">
                <span class="mr-3 text-2xl">🤔</span>
                <span class="font-medium">哲学研究所</span>
              </div>
              <span class="text-lg font-semibold text-blue-600">{philosophyCount}</span>
            </div>

            <div class="flex items-center justify-between rounded-lg bg-gray-50 p-4">
              <div class="flex items-center">
                <span class="mr-3 text-2xl">🌐</span>
                <span class="font-medium">互联网研究所</span>
              </div>
              <span class="text-lg font-semibold text-blue-600">{internetCount}</span>
            </div>

            <div class="flex items-center justify-between rounded-lg bg-gray-50 p-4">
              <div class="flex items-center">
                <span class="mr-3 text-2xl">🤖</span>
                <span class="font-medium">AI研究所</span>
              </div>
              <span class="text-lg font-semibold text-blue-600">{aiCount}</span>
            </div>

            <div class="flex items-center justify-between rounded-lg bg-gray-50 p-4">
              <div class="flex items-center">
                <span class="mr-3 text-2xl">🔮</span>
                <span class="font-medium">未来研究所</span>
              </div>
              <span class="text-lg font-semibold text-blue-600">{futureCount}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="rounded-lg bg-white shadow">
        <div class="border-b border-gray-200 px-6 py-4">
          <h2 class="text-lg font-medium text-gray-900">快速操作</h2>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
            <a
              href="/admin/content/create"
              class="text-decoration-none flex items-center justify-center rounded-lg border border-gray-300 px-4 py-3 transition-colors hover:bg-gray-50"
            >
              <span class="mr-2 text-xl">➕</span>
              <span class="font-medium">创建内容</span>
            </a>

            <a
              href="/admin/content"
              class="text-decoration-none flex items-center justify-center rounded-lg border border-gray-300 px-4 py-3 transition-colors hover:bg-gray-50"
            >
              <span class="mr-2 text-xl">📝</span>
              <span class="font-medium">内容管理</span>
            </a>

            <a
              href="/tags"
              class="text-decoration-none flex items-center justify-center rounded-lg border border-gray-300 px-4 py-3 transition-colors hover:bg-gray-50"
            >
              <span class="mr-2 text-xl">🏷️</span>
              <span class="font-medium">标签管理</span>
            </a>
          </div>
        </div>
      </div>

      <!-- 最近活动 -->
      <div class="mt-8 rounded-lg bg-white shadow">
        <div class="border-b border-gray-200 px-6 py-4">
          <h2 class="text-lg font-medium text-gray-900">最近活动</h2>
        </div>
        <div class="p-6">
          <div class="space-y-4">
            <div class="flex items-center">
              <div class="h-2 w-2 flex-shrink-0 rounded-full bg-green-400"></div>
              <div class="ml-4">
                <p class="text-sm text-gray-900">创建了新文章：数字经济发展趋势分析</p>
                <p class="text-xs text-gray-500">2025年1月1日</p>
              </div>
            </div>

            <div class="flex items-center">
              <div class="h-2 w-2 flex-shrink-0 rounded-full bg-blue-400"></div>
              <div class="ml-4">
                <p class="text-sm text-gray-900">添加了研究日志：研究院启动日志</p>
                <p class="text-xs text-gray-500">2025年1月1日</p>
              </div>
            </div>

            <div class="flex items-center">
              <div class="h-2 w-2 flex-shrink-0 rounded-full bg-purple-400"></div>
              <div class="ml-4">
                <p class="text-sm text-gray-900">发布了公告：欢迎来到 Pennfly Private Academy</p>
                <p class="text-xs text-gray-500">2025年1月1日</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>
</Layout>

<style>
  /* 确保管理后台的样式 */
  body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
</style>
