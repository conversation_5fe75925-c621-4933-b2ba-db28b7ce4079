---
// 代码高亮示例组件
const pythonCode = `import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error, r2_score

# 生成示例数据
np.random.seed(42)
X = np.random.randn(100, 1)
y = 2 * X.squeeze() + 1 + np.random.randn(100) * 0.1

# 分割训练集和测试集
X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, random_state=42
)

# 创建并训练模型
model = LinearRegression()
model.fit(X_train, y_train)

# 预测
y_pred = model.predict(X_test)

# 评估模型
mse = mean_squared_error(y_test, y_pred)
r2 = r2_score(y_test, y_pred)

print(f"均方误差: {mse:.4f}")
print(f"R²分数: {r2:.4f}")`;

const jsCode = `// 异步数据获取和处理
class DataProcessor {
  constructor(apiUrl) {
    this.apiUrl = apiUrl;
    this.cache = new Map();
  }
  
  async fetchData(endpoint) {
    const cacheKey = \`\${this.apiUrl}/\${endpoint}\`;
    
    // 检查缓存
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }
    
    try {
      const response = await fetch(cacheKey);
      if (!response.ok) {
        throw new Error(\`HTTP error! status: \${response.status}\`);
      }
      
      const data = await response.json();
      this.cache.set(cacheKey, data);
      return data;
    } catch (error) {
      console.error('数据获取失败:', error);
      throw error;
    }
  }
  
  async processData(data) {
    return data.map(item => ({
      ...item,
      processed: true,
      timestamp: new Date().toISOString()
    }));
  }
}

// 使用示例
const processor = new DataProcessor('https://api.example.com');
processor.fetchData('users')
  .then(data => processor.processData(data))
  .then(processedData => console.log(processedData))
  .catch(error => console.error('处理失败:', error));`;

const sqlCode = `-- 分析用户行为的复杂查询
WITH user_stats AS (
  SELECT 
    u.user_id,
    u.username,
    COUNT(DISTINCT o.order_id) as total_orders,
    SUM(o.total_amount) as total_spent,
    AVG(o.total_amount) as avg_order_value,
    MAX(o.created_at) as last_order_date
  FROM users u
  LEFT JOIN orders o ON u.user_id = o.user_id
  WHERE u.created_at >= '2024-01-01'
  GROUP BY u.user_id, u.username
),
user_segments AS (
  SELECT 
    *,
    CASE 
      WHEN total_spent > 1000 THEN 'VIP'
      WHEN total_spent > 500 THEN 'Premium'
      WHEN total_spent > 100 THEN 'Regular'
      ELSE 'New'
    END as user_segment,
    CASE 
      WHEN last_order_date >= CURRENT_DATE - INTERVAL '30 days' THEN 'Active'
      WHEN last_order_date >= CURRENT_DATE - INTERVAL '90 days' THEN 'Inactive'
      ELSE 'Dormant'
    END as activity_status
  FROM user_stats
)
SELECT 
  user_segment,
  activity_status,
  COUNT(*) as user_count,
  AVG(total_spent) as avg_lifetime_value,
  AVG(avg_order_value) as avg_order_size
FROM user_segments
GROUP BY user_segment, activity_status
ORDER BY user_segment, activity_status;`;
---

<div class="code-examples bg-purple-50 border border-purple-200 rounded-lg p-6 mb-6">
  <h3 class="text-lg font-semibold mb-4 text-purple-900">代码高亮示例</h3>
  
  <div class="space-y-4">
    <!-- Python 示例 -->
    <div>
      <h4 class="font-medium mb-2">Python - 机器学习示例：</h4>
      <pre><code class="language-python" set:html={pythonCode}></code></pre>
    </div>
    
    <!-- JavaScript 示例 -->
    <div>
      <h4 class="font-medium mb-2">JavaScript - 异步数据处理：</h4>
      <pre><code class="language-javascript" set:html={jsCode}></code></pre>
    </div>
    
    <!-- SQL 示例 -->
    <div>
      <h4 class="font-medium mb-2">SQL - 复杂查询：</h4>
      <pre><code class="language-sql" set:html={sqlCode}></code></pre>
    </div>
  </div>
</div>