# Pennfly Private Academy

> 个人化私人研究院 · 跨领域学术探索 · 思想与创意的交流平台

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Astro](https://img.shields.io/badge/Astro-5.12.9-purple.svg)](https://docs.astro.build)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.9.2-blue.svg)](https://www.typescriptlang.org/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind-4.1.11-38B2AC.svg)](https://tailwindcss.com/)
[![Security](https://img.shields.io/badge/Security-A+-green.svg)](./src/utils/security.ts)
[![Version](https://img.shields.io/badge/Version-1.0.0-brightgreen.svg)](./CHANGELOG.md)
[![Build Status](https://img.shields.io/badge/Build-Passing-success.svg)](./package.json)

## 🎉 版本 1.0.0 - 核心功能完整版

**发布日期**: 2025年1月13日

### ✅ 已完成功能

- ✅ **完整的研究院架构** - 5个专业研究所 + 功能中心
- ✅ **响应式设计** - 完美适配所有设备
- ✅ **可访问性支持** - WCAG 2.1 AA 标准
- ✅ **性能优化** - 2.62MB 总体积，图片优化 84%
- ✅ **主题系统** - 亮色/暗色/高对比度主题，支持系统自动切换
- ✅ **产品发布中心** - 完整的产品展示功能
- ✅ **学术内容渲染** - 数学公式、代码高亮、图表支持
- ✅ **内容管理系统** - Content Collections 完整配置
- ✅ **开发工具链** - ESLint、Prettier、TypeScript 完整配置

### 📊 性能指标

- **总体积**: 2.62 MB
- **JavaScript**: 436.37 KB (14 个文件)
- **CSS**: 197.33 KB (4 个文件)
- **图片**: 53.86 KB (已优化)
- **字体**: 1.02 MB (KaTeX 数学字体)
- **构建时间**: < 3 秒
- **类型检查**: ✅ 无错误

## 🏛️ 研究院架构

本学院由多个专业研究所组成，每个研究所专注于特定领域的深度研究：

- 🤖 **AI研究所** - 人工智能技术与应用研究
- 💰 **经济研究所** - 经济理论与市场分析
- 🌐 **互联网研究所** - 网络技术与数字化转型
- 🧠 **哲学研究所** - 哲学思辨与人文思考
- 🔮 **未来研究所** - 前沿趋势与未来预测

## ✨ 核心功能

- 📚 **学术内容管理** - 支持数学公式、图表、代码高亮的专业内容渲染
- 🔍 **智能搜索** - 基于 Fuse.js 的全文搜索和内容推荐
- 📱 **响应式设计** - 完美适配桌面、平板和移动设备
- 🎨 **智能主题系统** - 亮色/暗色/高对比度主题，支持系统自动切换和用户偏好记忆
- 🌍 **多语言支持** - 中英文双语内容管理
- 🔒 **安全防护** - 完整的安全检查和防护机制
- 📊 **内容关联** - 跨研究所的内容标签和推荐系统
- 🤖 **AI 辅助开发** - 集成 Kiro AI 助手，提供智能化开发支持
- ✅ **自动化质量检查** - 内容验证、性能分析、可访问性检查等自动化工具

## 🚀 项目结构

```text
mad-magnitude/
├── public/                     # 静态资源
├── src/
│   ├── components/            # 可复用组件
│   │   ├── academic/         # 学术内容组件
│   │   ├── home/            # 首页组件
│   │   ├── institute/       # 研究所组件
│   │   ├── navigation/      # 导航组件
│   │   └── theme/           # 主题系统组件
│   │       ├── ThemeProvider.astro  # 主题提供者 (已完成)
│   │       └── ThemeToggle.astro    # 主题切换器 (开发中)
│   ├── content/              # 内容集合
│   │   ├── ai/              # AI研究所内容
│   │   ├── economics/       # 经济研究所内容
│   │   ├── future/          # 未来研究所内容
│   │   ├── internet/        # 互联网研究所内容
│   │   ├── philosophy/      # 哲学研究所内容
│   │   ├── logs/            # 研究日志
│   │   ├── news/            # 动态资讯
│   │   └── config.ts        # 内容类型配置
│   ├── layouts/              # 页面布局
│   │   ├── Layout.astro     # 基础布局
│   │   └── AcademicLayout.astro # 学术内容布局
│   ├── pages/                # 路由页面
│   │   ├── [institute]/     # 各研究所页面
│   │   ├── logs/            # 日志页面
│   │   ├── news/            # 资讯页面
│   │   └── index.astro      # 首页
│   ├── styles/               # 样式文件
│   │   ├── global.css       # 全局样式
│   │   ├── academic.css     # 学术样式
│   │   └── tokens/          # 设计令牌系统
│   │       ├── themes.ts    # 主题配置
│   │       ├── colors.ts    # 颜色令牌
│   │       ├── typography.ts # 字体令牌
│   │       └── animations.ts # 动画令牌
│   ├── utils/                # 工具函数
│   │   ├── content.ts       # 内容处理
│   │   ├── security.ts      # 安全工具
│   │   ├── theme.ts         # 主题工具函数
│   │   └── instituteManager.ts # 研究所管理
│   └── types/                # TypeScript 类型定义
│       ├── theme.ts         # 主题系统类型
│       └── ...              # 其他类型定义
├── scripts/                  # 构建和工具脚本
├── docs/                     # 项目文档
│   ├── ai-automation.md     # AI自动化系统文档
│   ├── color-scheme.md      # 配色标准文档
│   ├── content-management.md # 内容管理系统文档
│   └── ui-frontend-optimization.md # UI前端优化规格文档
└── .kiro/                   # 🤖 Kiro AI 自动化系统 (已完全部署)
    ├── hooks/               # 15个智能化钩子 (已验证)
    │   ├── index.json                    # 钩子索引配置
    │   ├── content-validator.json        # 内容验证器 (自动触发)
    │   ├── component-optimizer.json      # 组件优化器 (自动触发)
    │   ├── seo-optimizer.json           # SEO优化器 (自动触发)
    │   ├── typescript-validator.json    # TypeScript验证器 (自动触发)
    │   ├── style-validator.json         # 样式验证器 (自动触发)
    │   ├── quick-diagnosis.json         # 快速诊断 (手动触发)
    │   ├── code-quality-fixer.json     # 代码质量修复器 (手动触发)
    │   ├── build-analyzer.json         # 构建分析器 (手动触发)
    │   ├── performance-optimizer.json   # 性能优化器 (手动触发)
    │   ├── security-auditor.json       # 安全审计器 (手动触发)
    │   ├── accessibility-checker.json  # 可访问性检查器 (手动触发)
    │   ├── test-runner.json            # 测试运行器 (手动触发)
    │   ├── deployment-checker.json     # 部署检查器 (手动触发)
    │   ├── documentation-generator.json # 文档生成器 (手动触发)
    │   └── content-creator.json        # 内容创建助手 (手动触发)
    ├── steering/            # 4个智能指导文档 (已配置)
    │   ├── project-context.md          # 项目上下文 (始终包含)
    │   ├── development-standards.md    # 开发标准 (文件匹配时包含)
    │   ├── content-creation.md         # 内容创建指南 (手动包含) ✨ 新增
    │   └── current-priorities.md       # 当前优先级 (始终包含)
    ├── specs/               # 项目规格文档
    │   ├── content-management/         # 内容管理规格和任务列表
    │   └── ui-frontend-optimization/   # UI前端优化设计规格和实施计划
    ├── HOOKS_USAGE_GUIDE.md # 钩子使用指南
    ├── TROUBLESHOOTING.md   # 故障排除指南 (已完善)
    ├── config.json          # AI系统主配置
    ├── settings.json        # 系统设置
    └── reload-hooks.js      # 钩子重载和验证脚本
```

## 🚀 快速开始

### 环境要求

- Node.js >= 18.0.0
- npm >= 9.0.0

### 安装和运行

```bash
# 克隆项目
git clone https://github.com/pennfly/pennfly-academy.git
cd mad-magnitude

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

## 🧞 可用命令

### 开发命令

| 命令                | 说明                            |
| :------------------ | :------------------------------ |
| `npm run dev`       | 启动开发服务器 (localhost:4321) |
| `npm run build`     | 构建生产版本到 `./dist/`        |
| `npm run preview`   | 本地预览构建结果                |
| `npm run astro ...` | 运行 Astro CLI 命令             |

### 代码质量

| 命令                 | 说明                                |
| :------------------- | :---------------------------------- |
| `npm run lint`       | 检查并修复代码质量问题              |
| `npm run format`     | 使用 Prettier 格式化代码            |
| `npm run type-check` | TypeScript 类型检查                 |
| `npm run check`      | 运行所有检查 (lint + format + type) |

### 测试命令

| 命令                    | 说明                     |
| :---------------------- | :----------------------- |
| `npm run test`          | 运行测试套件             |
| `npm run test:ui`       | 启动测试 UI 界面         |
| `npm run test:coverage` | 运行测试并生成覆盖率报告 |
| `npm run test:run`      | 运行测试一次 (CI 模式)   |

### 安全检查

| 命令                     | 说明               |
| :----------------------- | :----------------- |
| `npm run security:check` | 运行综合安全检查   |
| `npm run security:audit` | npm 依赖安全审计   |
| `npm run security:full`  | 完整安全检查和审计 |

### 构建分析

| 命令                    | 说明             |
| :---------------------- | :--------------- |
| `npm run build:analyze` | 构建并分析包大小 |
| `npm run build:report`  | 生成完整构建报告 |
| `npm run analyze:size`  | 分析构建产物大小 |

### 内容管理

| 命令                      | 说明             |
| :------------------------ | :--------------- |
| `npm run create:research` | 创建新的研究内容 |
| `npm run create:log`      | 创建新的研究日志 |
| `npm run create:news`     | 创建新的动态资讯 |
| `npm run create:product`  | 创建新的产品发布 |

### 🤖 AI 辅助内容创建

- **内容创建助手** - 使用 Kiro AI 钩子生成符合规范的内容模板
- **内容验证器** - 保存时自动检查格式、标签、语法和质量
- **创建指南** - 完整的内容类型、格式和写作规范文档

### 🤖 AI 辅助开发系统

本项目集成了完整的 Kiro AI 助手系统，包含 **15 个智能化钩子** 和
**4 个指导文档**，提供全方位的开发自动化支持。系统已完全部署并经过验证，所有钩子和指导文档均已就绪：

#### 🔄 自动触发钩子 (5个)

- **内容验证器** - 保存 `.md` 文件时自动验证格式、标签、语法
- **组件优化器** - 保存 `.astro` 组件时检查性能和可访问性
- **SEO 优化器** - 保存页面文件时检查 SEO 设置和优化
- **TypeScript 验证器** - 保存 `.ts/.tsx` 文件时进行类型检查
- **样式验证器** - 保存样式文件时检查 CSS 规范和性能

#### 🔧 手动触发钩子 (10个)

- **快速问题诊断** - 全面检查项目健康状况和常见问题
- **代码质量修复器** - 自动修复 ESLint 错误和代码质量问题
- **构建分析器** - 分析构建结果、性能指标和优化建议
- **性能优化器** - 检查和优化页面加载性能
- **安全审计器** - 扫描安全漏洞和潜在风险
- **可访问性检查器** - 全面检查 WCAG 2.1 AA 标准合规性
- **测试运行器** - 执行测试套件并生成覆盖率报告
- **部署检查器** - 验证部署前的准备工作和配置
- **文档生成器** - 自动生成和更新项目文档
- **内容创建助手** - 帮助创建符合规范的新内容

#### 📚 智能指导系统

- **项目上下文** - 完整的项目架构和技术栈信息
- **开发标准** - 代码质量、性能和可访问性要求
- **内容创建指南** - 内容格式、写作规范和最佳实践
- **当前优先级** - 开发路线图和任务优先级

#### 使用方法

- **自动触发**：保存相关文件时自动运行对应钩子
- **手动触发**：在 Kiro 界面中点击对应按钮
- **命令面板**：`Ctrl+Shift+P` 搜索 "Kiro Hook"

#### 系统状态

- ✅ **15 个钩子** 已部署并验证
- ✅ **4 个指导文档** 已创建并配置
- ✅ **自动化系统** 完全就绪
- ✅ **故障排除指南** 已完善

如果遇到钩子显示问题，请参考 `.kiro/TROUBLESHOOTING.md` 文档，通常重启 Kiro
IDE 即可解决。

## 🎨 主题系统

项目集成了完整的主题管理系统，提供优秀的视觉体验和可访问性支持：

### 支持的主题

- **🌞 亮色主题** - 清爽明亮的日间阅读体验
- **🌙 暗色主题** - 护眼舒适的夜间阅读体验
- **⚡ 高对比度主题** - 符合无障碍访问标准的高对比度体验

### 智能特性

- **🔄 系统自动切换** - 根据操作系统主题偏好自动切换
- **💾 偏好记忆** - 用户选择的主题会自动保存到本地存储
- **📱 移动端适配** - 支持移动端浏览器主题色显示
- **🎭 平滑过渡** - 主题切换时的流畅动画效果
- **♿ 可访问性** - 尊重用户的动画偏好设置 (prefers-reduced-motion)

### 技术实现

- **静态 CSS 生成** - 高性能的主题切换，无运行时开销
- **CSS 自定义属性** - 完整的设计令牌系统
- **TypeScript 支持** - 完整的类型定义和 API
- **事件系统** - 主题变化的响应式事件通知

### 使用方法

```javascript
// 获取主题管理器
const themeManager = window.themeManager;

// 设置主题
themeManager.setTheme('dark');
themeManager.toggleTheme();

// 监听主题变化
window.addEventListener('themechange', event => {
  console.log('主题已切换到:', event.detail.theme);
});
```

## 🔧 技术栈

- **框架**: Astro 5.12.9 + TypeScript 5.9.2
- **样式**: Tailwind CSS 4.1.11 + CSS 自定义属性
- **主题系统**: 自研主题管理器 + 设计令牌系统
- **构建**: Vite + ESBuild
- **测试**: Vitest + Testing Library
- **代码质量**: ESLint + Prettier + Husky
- **安全**: ESLint Security + 自定义安全脚本
- **学术功能**: KaTeX + Mermaid + Highlight.js
- **搜索**: Fuse.js

## 📚 开发指南

### 添加新内容

1. **研究所文章**: 在 `src/content/[institute]/` 目录下创建 `.md` 文件
2. **研究日志**: 在 `src/content/logs/` 目录下创建日志文件
3. **动态资讯**: 在 `src/content/news/` 目录下创建资讯文件
4. **产品发布**: 在 `src/content/products/` 目录下创建产品文件

### 内容创建指南

项目现已集成完整的**内容创建指南系统**，通过 Kiro
AI 助手提供智能化的内容创建支持：

- 📝 **内容类型规范** - 详细的内容分类和格式要求
- 🏷️ **标签系统** - 统一的标签管理和使用规范
- ✍️ **写作规范** - 学术写作标准和最佳实践
- 🔍 **SEO 优化** - 搜索引擎优化指导
- ♿ **可访问性** - 无障碍访问标准
- 🤖 **AI 辅助** - 内容创建助手自动化支持

**使用方法**：

- 在 Kiro AI 中使用 `#content-creation` 上下文键获取创建指南
- 使用**内容创建助手**钩子生成符合规范的内容模板
- **内容验证器**会在保存时自动检查格式和质量

### 内容格式

所有内容文件都需要包含 frontmatter 元数据：

```yaml
---
title:
  zh: '中文标题'
  en: 'English Title'
description:
  zh: '中文描述'
publishDate: 2025-01-01
tags: ['tag1', 'tag2']
author: 'Pennfly'
# 根据内容类型添加特定字段
type: research | announcement | reflection | milestone # 动态资讯
category: ai | education | philosophy | technology # 研究报告
mood: thoughtful | critical | optimistic | analytical # 研究日志
---
```

### 学术功能

- **数学公式**: 使用 LaTeX 语法，支持行内 `$...$` 和块级 `$$...$$`
- **代码高亮**: 支持多种编程语言的语法高亮
- **图表**: 支持 Mermaid 图表渲染
- **目录导航**: 自动生成文章目录和阅读进度

## 🛡️ 安全特性

- **输入验证**: 所有用户输入都经过严格验证和清理
- **安全头**: 配置了完整的 HTTP 安全头
- **依赖审计**: 定期检查依赖包的安全漏洞
- **代码扫描**: ESLint 安全规则检查潜在安全问题

## 📖 文档资源

- [Astro 官方文档](https://docs.astro.build)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)
- [TypeScript 文档](https://www.typescriptlang.org/docs)
- [Vitest 测试框架](https://vitest.dev)

## 🤝 贡献指南

1. Fork 本项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

### 提交规范

本项目使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

- `feat:` 新功能
- `fix:` 修复问题
- `docs:` 文档更新
- `style:` 代码格式调整
- `refactor:` 代码重构
- `test:` 测试相关
- `chore:` 构建或工具相关

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📈 项目状态

### 当前版本: v1.0.0 (生产就绪)

- ✅ **核心功能** - 完整的研究院架构和内容管理
- ✅ **AI 自动化系统** - 15个钩子 + 4个指导文档，完全部署
- ✅ **性能优化** - 构建产物 2.62MB，加载时间 < 2s
- ✅ **可访问性** - WCAG 2.1 AA 标准合规
- ✅ **代码质量** - TypeScript 严格模式，ESLint + Prettier
- ✅ **测试覆盖** - 单元测试和集成测试
- ✅ **安全防护** - 完整的安全检查机制
- ✅ **文档完善** - 开发指南、使用文档、故障排除

### 下一版本: v1.1.0 (开发中)

- 🎨 **UI 前端优化** - 全面的用户界面和体验优化 (详见
  [UI 优化规格](docs/ui-frontend-optimization.md))
  - ✅ **设计令牌系统** - 完整的颜色、字体、间距等设计令牌配置
  - ✅ **主题系统架构** - 完整的主题管理系统，支持亮色/暗色/高对比度主题
  - 🚧 **响应式网格系统** - 优化移动端和平板体验，支持更多设备尺寸 (进行中)
  - 🚧
    **基础组件库重构** - 使用新设计系统重构 Button、Card、Typography 等组件 (进行中)
  - 📋 **交互体验优化** - 动画过渡、微交互和用户反馈系统 (待开始)
  - 📋 **内容展示优化** - 学术内容的最佳呈现方式和阅读体验 (待开始)
  - 📋 **导航和搜索体验** - 智能搜索界面和内容发现优化 (待开始)
  - 📋 **性能和加载优化** - 2秒首屏加载目标和60fps流畅体验 (待开始)
  - 📋 **可访问性增强** - WCAG 2.1 AA+ 标准和辅助技术支持 (待开始)
  - 📋 **个性化用户偏好** - 主题切换 UI、字体调节、语言设置等 (待开始)
- 🔍 **搜索功能增强** - Fuse.js 集成和搜索体验优化
- 🏷️ **标签系统完善** - 统一标签管理和跨研究所筛选
- 📈 **SEO 深度优化** - 结构化数据和社交媒体优化
- 🌐 **国际化准备** - 多语言路由和本地化支持

## 📞 联系方式

- 作者: Pennfly
- 主页: [https://pennfly.com](https://pennfly.com)
- 项目地址:
  [https://github.com/pennfly/pennfly-academy](https://github.com/pennfly/pennfly-academy)

---

⭐ 如果这个项目对你有帮助，请给它一个星标！
