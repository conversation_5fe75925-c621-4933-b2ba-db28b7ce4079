{"$ref": "#/definitions/logs", "definitions": {"logs": {"type": "object", "properties": {"date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "string", "format": "date"}, {"type": "integer", "format": "unix-time"}]}, "title": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}, "default": []}, "mood": {"type": "string", "enum": ["thoughtful", "critical", "optimistic", "analytical"]}, "relatedInstitute": {"type": "array", "items": {"type": "string", "enum": ["economics", "philosophy", "internet", "ai", "future"]}}, "draft": {"type": "boolean", "default": false}, "$schema": {"type": "string"}}, "required": ["date", "title"], "additionalProperties": false}}, "$schema": "http://json-schema.org/draft-07/schema#"}