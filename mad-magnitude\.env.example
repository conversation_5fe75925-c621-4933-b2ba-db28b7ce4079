# Pennfly Private Academy - Environment Variables Template
# Copy this file to .env and fill in your actual values

# =============================================================================
# Site Configuration
# =============================================================================
SITE_URL=https://pennfly.com
SITE_TITLE=Pennfly Private Academy
SITE_DESCRIPTION=个人化私人研究院 · 跨领域学术探索 · 思想与创意的交流平台

# =============================================================================
# Environment Settings
# =============================================================================
NODE_ENV=development
DEBUG=false

# =============================================================================
# Build Configuration
# =============================================================================
BUILD_ANALYZE=false
BUILD_SOURCEMAP=true

# =============================================================================
# Development Settings
# =============================================================================
DEV_PORT=4321
DEV_HOST=localhost
DEV_OPEN_BROWSER=true

# =============================================================================
# Security Settings
# =============================================================================
# Content Security Policy settings
CSP_ENABLED=true
CSP_REPORT_ONLY=false

# CORS settings
CORS_ORIGIN=*
CORS_CREDENTIALS=false

# =============================================================================
# Third-party Services (Optional)
# =============================================================================
# Analytics
# GOOGLE_ANALYTICS_ID=
# PLAUSIBLE_DOMAIN=

# Comment System
# DISQUS_SHORTNAME=
# GISCUS_REPO=
# GISCUS_REPO_ID=
# GISCUS_CATEGORY=
# GISCUS_CATEGORY_ID=

# Search
# ALGOLIA_APP_ID=
# ALGOLIA_API_KEY=
# ALGOLIA_INDEX_NAME=

# Email Service
# SMTP_HOST=
# SMTP_PORT=587
# SMTP_USER=
# SMTP_PASS=
# CONTACT_EMAIL=

# =============================================================================
# API Keys (Keep these secret!)
# =============================================================================
# External APIs
# OPENAI_API_KEY=
# GITHUB_TOKEN=
# NOTION_TOKEN=

# Database (if needed)
# DATABASE_URL=
# REDIS_URL=

# =============================================================================
# Feature Flags
# =============================================================================
FEATURE_COMMENTS=false
FEATURE_SEARCH=false
FEATURE_ANALYTICS=false
FEATURE_NEWSLETTER=false