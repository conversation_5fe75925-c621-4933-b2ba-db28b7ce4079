---
import { getCollection } from 'astro:content';
import Layout from '../../layouts/Layout.astro';
import { formatDate, groupByMonth } from '../../utils/dateUtils';

// 获取研究日志
const researchLogs = await getCollection('logs');
const sortedLogs = researchLogs
  .filter((log: any) => !log.data.draft)
  .sort((a: any, b: any) => b.data.date.getTime() - a.data.date.getTime());

// 按心情分组
const logsByMood = {
  all: sortedLogs,
  thoughtful: sortedLogs.filter((log: any) => log.data.mood === 'thoughtful'),
  critical: sortedLogs.filter((log: any) => log.data.mood === 'critical'),
  optimistic: sortedLogs.filter((log: any) => log.data.mood === 'optimistic'),
  analytical: sortedLogs.filter((log: any) => log.data.mood === 'analytical'),
};

// 按研究所分组
const logsByInstitute = {
  economics: sortedLogs.filter((log: any) => log.data.relatedInstitute?.includes('economics')),
  philosophy: sortedLogs.filter((log: any) => log.data.relatedInstitute?.includes('philosophy')),
  internet: sortedLogs.filter((log: any) => log.data.relatedInstitute?.includes('internet')),
  ai: sortedLogs.filter((log: any) => log.data.relatedInstitute?.includes('ai')),
  future: sortedLogs.filter((log: any) => log.data.relatedInstitute?.includes('future')),
};

// 按月份分组（用于时间线视图）
const logsByMonth = groupByMonth(sortedLogs);

// 统计信息
const stats = {
  total: sortedLogs.length,
  thisMonth: sortedLogs.filter((log: any) => {
    const now = new Date();
    const logDate = new Date(log.data.date);
    return logDate.getMonth() === now.getMonth() && logDate.getFullYear() === now.getFullYear();
  }).length,
  byMood: {
    thoughtful: logsByMood.thoughtful.length,
    critical: logsByMood.critical.length,
    optimistic: logsByMood.optimistic.length,
    analytical: logsByMood.analytical.length,
  },
  byInstitute: {
    economics: logsByInstitute.economics.length,
    philosophy: logsByInstitute.philosophy.length,
    internet: logsByInstitute.internet.length,
    ai: logsByInstitute.ai.length,
    future: logsByInstitute.future.length,
  },
};

// 获取所有标签
const allTags = new Set<string>();
sortedLogs.forEach((log: any) => {
  log.data.tags?.forEach((tag: string) => allTags.add(tag));
});
const popularTags = Array.from(allTags).slice(0, 10);
---

<Layout
  title="研究日志 - Pennfly Private Academy"
  description="记录日常的研究思考、学习心得和灵感闪现，展示知识创造的真实过程"
>
  <main class="min-h-screen bg-gray-50">
    <!-- 页面头部 -->
    <div class="bg-gradient-to-r from-amber-500 to-orange-600 py-16 text-white">
      <div class="container mx-auto px-6">
        <div class="mb-6 flex items-center">
          <span class="mr-4 text-5xl">📔</span>
          <div>
            <h1 class="mb-2 text-4xl font-bold">研究日志</h1>
            <p class="text-xl opacity-90">Research Journal</p>
          </div>
        </div>
        <p class="max-w-3xl text-lg opacity-90">
          记录日常的研究思考、学习心得和灵感闪现。
          这里是思想的实验室，记录着知识创造和思考演进的真实轨迹。
        </p>
      </div>
    </div>

    <!-- 日志列表 -->
    <div class="container mx-auto px-6 py-12">
      <!-- 统计和筛选 -->
      <div class="mb-8">
        <div class="mb-6 flex flex-wrap items-center justify-between gap-4">
          <h2 class="text-2xl font-bold text-gray-800">研究记录</h2>
          <div class="flex items-center space-x-4">
            <!-- 视图切换 -->
            <div class="flex rounded-lg border border-gray-300 bg-white">
              <button
                class="view-toggle active rounded-l-lg px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
                data-view="list"
              >
                📋 列表视图
              </button>
              <button
                class="view-toggle rounded-r-lg border-l border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
                data-view="timeline"
              >
                📅 时间线
              </button>
            </div>
          </div>
        </div>

        <!-- 筛选器 -->
        <div class="mb-6 space-y-4">
          <!-- 心情筛选 -->
          <div>
            <h3 class="mb-2 text-sm font-medium text-gray-700">按心情筛选</h3>
            <div class="flex flex-wrap gap-2">
              <button class="mood-filter active" data-mood="all">
                全部 ({stats.total})
              </button>
              <button class="mood-filter" data-mood="thoughtful">
                🤔 深思 ({stats.byMood.thoughtful})
              </button>
              <button class="mood-filter" data-mood="optimistic">
                😊 乐观 ({stats.byMood.optimistic})
              </button>
              <button class="mood-filter" data-mood="analytical">
                🔍 分析 ({stats.byMood.analytical})
              </button>
              <button class="mood-filter" data-mood="critical">
                🧐 批判 ({stats.byMood.critical})
              </button>
            </div>
          </div>

          <!-- 热门标签 -->
          {
            popularTags.length > 0 && (
              <div>
                <h3 class="mb-2 text-sm font-medium text-gray-700">热门标签</h3>
                <div class="flex flex-wrap gap-2">
                  {popularTags.map(tag => (
                    <button class="tag-filter rounded-full bg-gray-100 px-3 py-1 text-xs text-gray-700 transition-colors hover:bg-gray-200">
                      #{tag}
                    </button>
                  ))}
                </div>
              </div>
            )
          }
        </div>

        <!-- 统计信息 -->
        <div class="mb-8 grid grid-cols-2 gap-4 md:grid-cols-4">
          <div class="rounded-lg bg-amber-50 p-4 text-center">
            <div class="text-2xl font-bold text-amber-600">{stats.total}</div>
            <div class="text-sm text-amber-800">总日志数</div>
          </div>
          <div class="rounded-lg bg-green-50 p-4 text-center">
            <div class="text-2xl font-bold text-green-600">{stats.thisMonth}</div>
            <div class="text-sm text-green-800">本月记录</div>
          </div>
          <div class="rounded-lg bg-blue-50 p-4 text-center">
            <div class="text-2xl font-bold text-blue-600">
              {stats.byInstitute.ai + stats.byInstitute.economics}
            </div>
            <div class="text-sm text-blue-800">热门研究所</div>
          </div>
          <div class="rounded-lg bg-purple-50 p-4 text-center">
            <div class="text-2xl font-bold text-purple-600">{popularTags.length}</div>
            <div class="text-sm text-purple-800">标签数量</div>
          </div>
        </div>
      </div>

      <!-- 列表视图 -->
      <div id="list-view" class="view-content mb-12">
        {
          sortedLogs.length > 0 ? (
            <div class="space-y-6">
              {sortedLogs.map((log: any) => (
                <article
                  class="log-item rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition-all hover:-translate-y-1 hover:shadow-md"
                  data-mood={log.data.mood || 'none'}
                >
                  <div class="mb-4 flex items-start justify-between">
                    <div class="flex-1">
                      <div class="mb-3 flex items-center space-x-3">
                        <span class="font-mono text-lg text-gray-500">
                          📅 {formatDate(log.data.date)}
                        </span>
                        {log.data.mood && (
                          <span
                            class={`rounded px-2 py-1 text-xs font-medium ${
                              log.data.mood === 'thoughtful'
                                ? 'bg-blue-100 text-blue-800'
                                : log.data.mood === 'critical'
                                  ? 'bg-red-100 text-red-800'
                                  : log.data.mood === 'optimistic'
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-purple-100 text-purple-800'
                            }`}
                          >
                            {log.data.mood === 'thoughtful'
                              ? '🤔 深思'
                              : log.data.mood === 'critical'
                                ? '🧐 批判'
                                : log.data.mood === 'optimistic'
                                  ? '😊 乐观'
                                  : '🔍 分析'}
                          </span>
                        )}
                      </div>

                      <h3 class="mb-3 text-xl font-semibold text-gray-800">
                        <a
                          href={`/logs/${log.slug}`}
                          class="transition-colors hover:text-amber-600"
                        >
                          {log.data.title}
                        </a>
                      </h3>

                      {/* 相关研究所 */}
                      {log.data.relatedInstitute && log.data.relatedInstitute.length > 0 && (
                        <div class="mb-3">
                          <div class="mb-2 text-xs text-gray-500">相关研究所:</div>
                          <div class="flex flex-wrap gap-2">
                            {log.data.relatedInstitute.map((institute: string) => (
                              <a
                                href={`/${institute}`}
                                class="inline-flex items-center space-x-1 rounded bg-green-100 px-2 py-1 text-xs text-green-700 transition-colors hover:bg-green-200"
                              >
                                <span>
                                  {institute === 'economics'
                                    ? '💰'
                                    : institute === 'philosophy'
                                      ? '🤔'
                                      : institute === 'internet'
                                        ? '🌐'
                                        : institute === 'ai'
                                          ? '🤖'
                                          : '🔮'}
                                </span>
                                <span>
                                  {institute === 'economics'
                                    ? '经济研究所'
                                    : institute === 'philosophy'
                                      ? '哲学研究所'
                                      : institute === 'internet'
                                        ? '互联网研究所'
                                        : institute === 'ai'
                                          ? 'AI研究所'
                                          : '未来研究所'}
                                </span>
                              </a>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {log.data.tags && log.data.tags.length > 0 && (
                    <div class="flex flex-wrap gap-2">
                      {log.data.tags.map((tag: string) => (
                        <span class="cursor-pointer rounded-full bg-gray-100 px-3 py-1 text-xs text-gray-700 transition-colors hover:bg-gray-200">
                          #{tag}
                        </span>
                      ))}
                    </div>
                  )}
                </article>
              ))}
            </div>
          ) : (
            <div class="py-12 text-center">
              <div class="mb-4 text-6xl">📔</div>
              <h3 class="mb-2 text-xl font-semibold text-gray-800">暂无研究日志</h3>
              <p class="mb-6 text-gray-600">开始记录您的研究思考和学习心得吧</p>
              <a
                href="/admin"
                class="inline-block rounded-lg bg-amber-600 px-6 py-2 text-white transition-colors hover:bg-amber-700"
              >
                写日志
              </a>
            </div>
          )
        }
      </div>

      <!-- 时间线视图 -->
      <div id="timeline-view" class="view-content mb-12 hidden">
        {
          Object.entries(logsByMonth).map(([month, logs]) => (
            <div class="mb-8">
              <div class="mb-4 flex items-center">
                <div class="mr-4 rounded-lg bg-amber-100 px-3 py-1 text-sm font-medium text-amber-800">
                  {month}
                </div>
                <div class="h-px flex-1 bg-gray-200" />
              </div>

              <div class="space-y-4">
                {logs.map((log: any) => (
                  <div
                    class="log-item flex items-start space-x-4"
                    data-mood={log.data.mood || 'none'}
                  >
                    <div class="flex-shrink-0">
                      <div class="flex h-10 w-10 items-center justify-center rounded-full border-2 border-amber-200 bg-white">
                        <span class="text-sm">📔</span>
                      </div>
                    </div>

                    <div class="flex-1 rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-shadow hover:shadow-md">
                      <div class="mb-2 flex items-center justify-between">
                        <h4 class="font-semibold text-gray-800">
                          <a
                            href={`/logs/${log.slug}`}
                            class="transition-colors hover:text-amber-600"
                          >
                            {log.data.title}
                          </a>
                        </h4>
                        <span class="text-xs text-gray-500">{formatDate(log.data.date)}</span>
                      </div>

                      {log.data.mood && (
                        <div class="mb-2">
                          <span
                            class={`inline-block rounded px-2 py-1 text-xs font-medium ${
                              log.data.mood === 'thoughtful'
                                ? 'bg-blue-100 text-blue-800'
                                : log.data.mood === 'critical'
                                  ? 'bg-red-100 text-red-800'
                                  : log.data.mood === 'optimistic'
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-purple-100 text-purple-800'
                            }`}
                          >
                            {log.data.mood === 'thoughtful'
                              ? '🤔 深思'
                              : log.data.mood === 'critical'
                                ? '🧐 批判'
                                : log.data.mood === 'optimistic'
                                  ? '😊 乐观'
                                  : '🔍 分析'}
                          </span>
                        </div>
                      )}

                      {log.data.tags && log.data.tags.length > 0 && (
                        <div class="flex flex-wrap gap-1">
                          {log.data.tags.slice(0, 3).map((tag: string) => (
                            <span class="rounded bg-gray-100 px-2 py-0.5 text-xs text-gray-600">
                              #{tag}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))
        }
      </div>

      <!-- 返回首页 -->
      <div class="text-center">
        <a
          href="/"
          class="inline-block rounded-lg bg-gray-600 px-6 py-2 text-white transition-colors hover:bg-gray-700"
        >
          ← 返回研究院首页
        </a>
      </div>
    </div>
  </main>
</Layout>

<style>
  .mood-filter,
  .tag-filter {
    cursor: pointer;
    border-radius: 0.5rem;
    border: 1px solid #d1d5db;
    background-color: white;
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #4b5563;
    transition: colors 0.2s;
  }

  .mood-filter:hover,
  .tag-filter:hover {
    background-color: #f9fafb;
  }

  .mood-filter.active {
    border-color: #d97706;
    background-color: #d97706;
    color: white;
  }

  .mood-filter.active:hover {
    background-color: #b45309;
  }

  .view-toggle {
    cursor: pointer;
    transition: colors 0.2s;
  }

  .view-toggle.active {
    background-color: #fef3c7;
    color: #d97706;
  }

  .log-item {
    transition: all 0.3s ease;
  }

  .log-item.hidden {
    display: none;
  }

  .view-content {
    transition: opacity 0.3s ease;
  }

  .view-content.hidden {
    display: none;
  }

  /* 时间线样式 */
  #timeline-view .log-item:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 20px;
    top: 60px;
    width: 2px;
    height: calc(100% + 16px);
    background: linear-gradient(to bottom, #f3f4f6, transparent);
  }

  #timeline-view .log-item {
    position: relative;
  }
</style>

<script>
  // 心情筛选功能
  const moodFilters = document.querySelectorAll('.mood-filter');
  const logItems = document.querySelectorAll('.log-item');

  moodFilters.forEach((button: any) => {
    button.addEventListener('click', () => {
      const targetMood = button.dataset.mood;

      // 更新按钮状态
      moodFilters.forEach(btn => btn.classList.remove('active'));
      button.classList.add('active');

      // 筛选内容
      logItems.forEach((item: any) => {
        if (targetMood === 'all' || item.dataset.mood === targetMood) {
          item.classList.remove('hidden');
        } else {
          item.classList.add('hidden');
        }
      });
    });
  });

  // 视图切换功能
  const viewToggleButtons = document.querySelectorAll('.view-toggle');
  const listView = document.getElementById('list-view');
  const timelineView = document.getElementById('timeline-view');

  viewToggleButtons.forEach((button: any) => {
    button.addEventListener('click', () => {
      const targetView = button.dataset.view;

      // 更新按钮状态
      viewToggleButtons.forEach(btn => btn.classList.remove('active'));
      button.classList.add('active');

      // 切换视图
      if (targetView === 'list') {
        listView?.classList.remove('hidden');
        timelineView?.classList.add('hidden');
      } else {
        listView?.classList.add('hidden');
        timelineView?.classList.remove('hidden');
      }
    });
  });

  // 标签筛选功能
  document.querySelectorAll('.tag-filter').forEach(tag => {
    tag.addEventListener('click', () => {
      const tagText = tag.textContent?.replace('#', '').trim();
      if (tagText) {
        // 筛选包含该标签的日志
        logItems.forEach(item => {
          const itemTags = Array.from(item.querySelectorAll('.rounded-full')).map(el =>
            el.textContent?.replace('#', '').trim()
          );

          if (itemTags.includes(tagText)) {
            item.classList.remove('hidden');
          } else {
            item.classList.add('hidden');
          }
        });

        // 重置心情筛选按钮状态
        moodFilters.forEach(btn => btn.classList.remove('active'));
      }
    });
  });
</script>
