// DOM 操作辅助函数，提供类型安全的 DOM 操作

/**
 * 类型安全的 getElementById
 */
export function getElementById<T extends HTMLElement = HTMLElement>(id: string): T | null {
  return document.getElementById(id) as T | null;
}

/**
 * 类型安全的 querySelector
 */
export function querySelector<T extends Element = Element>(selector: string): T | null {
  return document.querySelector(selector) as T | null;
}

/**
 * 类型安全的 querySelectorAll
 */
export function querySelectorAll<T extends Element = Element>(selector: string): T[] {
  return Array.from(document.querySelectorAll(selector)) as T[];
}

/**
 * 安全的事件目标获取
 */
export function getEventTarget<T extends HTMLElement = HTMLElement>(event: Event): T | null {
  return event.target as T | null;
}

/**
 * 安全的数据集属性获取
 */
export function getDataset(element: HTMLElement, key: string): string | undefined {
  return element.dataset[key];
}

/**
 * 安全的数据集属性设置
 */
export function setDataset(element: HTMLElement, key: string, value: string): void {
  element.dataset[key] = value;
}

/**
 * 批量添加事件监听器
 */
export function addEventListeners<T extends Element>(
  elements: T[],
  event: string,
  handler: (this: T, event: Event) => void
): void {
  elements.forEach(element => {
    element.addEventListener(event, handler);
  });
}

/**
 * 安全的类名操作
 */
export class ClassListHelper {
  static toggle(element: Element, className: string, force?: boolean): boolean {
    return element.classList.toggle(className, force);
  }

  static add(element: Element, ...classNames: string[]): void {
    element.classList.add(...classNames);
  }

  static remove(element: Element, ...classNames: string[]): void {
    element.classList.remove(...classNames);
  }

  static contains(element: Element, className: string): boolean {
    return element.classList.contains(className);
  }

  static replace(element: Element, oldClass: string, newClass: string): boolean {
    return element.classList.replace(oldClass, newClass);
  }
}

/**
 * 安全的样式操作
 */
export class StyleHelper {
  static setProperty(element: HTMLElement, property: string, value: string): void {
    element.style.setProperty(property, value);
  }

  static getProperty(element: HTMLElement, property: string): string {
    return element.style.getPropertyValue(property);
  }

  static show(element: HTMLElement): void {
    element.style.display = '';
  }

  static hide(element: HTMLElement): void {
    element.style.display = 'none';
  }

  static setDisplay(element: HTMLElement, display: string): void {
    element.style.display = display;
  }
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return function (...args: Parameters<T>) {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return function (...args: Parameters<T>) {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

/**
 * 安全的 JSON 解析
 */
export function safeJsonParse<T = any>(json: string, defaultValue: T): T {
  try {
    return JSON.parse(json);
  } catch {
    return defaultValue;
  }
}

/**
 * 平滑滚动到元素
 */
export function scrollToElement(
  element: Element,
  options: ScrollIntoViewOptions = { behavior: 'smooth', block: 'start' }
): void {
  element.scrollIntoView(options);
}

/**
 * 检查元素是否在视口中
 */
export function isElementInViewport(element: Element): boolean {
  const rect = element.getBoundingClientRect();
  return (
    rect.top >= 0 &&
    rect.left >= 0 &&
    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
    rect.right <= (window.innerWidth || document.documentElement.clientWidth)
  );
}
