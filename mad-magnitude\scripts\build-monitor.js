#!/usr/bin/env node

import { execSync } from 'child_process';
import { existsSync, readFileSync, writeFileSync } from 'fs';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * 构建监控和分析工具
 */
class BuildMonitor {
  constructor() {
    this.projectRoot = join(__dirname, '..');
    this.reportsDir = join(this.projectRoot, 'reports');
    this.buildMetricsFile = join(this.reportsDir, 'build-metrics.json');
    this.currentBuild = {
      timestamp: new Date().toISOString(),
      buildTime: 0,
      bundleSize: 0,
      chunkCount: 0,
      errors: [],
      warnings: [],
      performance: {}
    };
  }

  /**
   * 确保报告目录存在
   */
  ensureReportsDir() {
    try {
      if (!existsSync(this.reportsDir)) {
        execSync(`mkdir -p "${this.reportsDir}"`, { cwd: this.projectRoot });
      }
    } catch (error) {
      console.warn('无法创建报告目录:', error.message);
    }
  }

  /**
   * 记录构建开始时间
   */
  startBuild() {
    this.buildStartTime = Date.now();
    console.log('🚀 开始构建监控...');
  }

  /**
   * 记录构建结束时间
   */
  endBuild() {
    this.currentBuild.buildTime = Date.now() - this.buildStartTime;
    console.log(`⏱️  构建耗时: ${this.currentBuild.buildTime}ms`);
  }

  /**
   * 分析构建产物
   */
  async analyzeBuildOutput() {
    try {
      // 运行构建分析脚本
      const analysisResult = execSync('node scripts/analyze-bundle.js', {
        cwd: this.projectRoot,
        encoding: 'utf8'
      });
      
      // 解析分析结果（这里简化处理）
      this.currentBuild.bundleAnalysis = 'completed';
      
    } catch (error) {
      console.warn('构建分析失败:', error.message);
      this.currentBuild.errors.push({
        type: 'analysis_error',
        message: error.message
      });
    }
  }

  /**
   * 检查性能指标
   */
  checkPerformanceMetrics() {
    const metrics = {
      buildTime: this.currentBuild.buildTime,
      timestamp: this.currentBuild.timestamp
    };

    // 构建时间警告
    if (this.currentBuild.buildTime > 60000) { // > 1分钟
      this.currentBuild.warnings.push({
        type: 'slow_build',
        message: `构建时间较长: ${this.currentBuild.buildTime}ms`,
        suggestion: '考虑优化构建配置或使用增量构建'
      });
    }

    this.currentBuild.performance = metrics;
  }

  /**
   * 生成构建报告
   */
  generateBuildReport() {
    const report = {
      ...this.currentBuild,
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      memory: process.memoryUsage()
    };

    return report;
  }

  /**
   * 保存构建历史
   */
  saveBuildHistory() {
    try {
      let history = [];
      
      // 读取现有历史
      if (existsSync(this.buildMetricsFile)) {
        const existingData = readFileSync(this.buildMetricsFile, 'utf8');
        history = JSON.parse(existingData);
      }
      
      // 添加当前构建
      const report = this.generateBuildReport();
      history.push(report);
      
      // 只保留最近50次构建记录
      if (history.length > 50) {
        history = history.slice(-50);
      }
      
      // 保存到文件
      writeFileSync(this.buildMetricsFile, JSON.stringify(history, null, 2));
      
      console.log(`📊 构建报告已保存到: ${this.buildMetricsFile}`);
      
    } catch (error) {
      console.warn('保存构建历史失败:', error.message);
    }
  }

  /**
   * 分析构建趋势
   */
  analyzeBuildTrends() {
    try {
      if (!existsSync(this.buildMetricsFile)) {
        console.log('📈 暂无历史构建数据');
        return;
      }
      
      const history = JSON.parse(readFileSync(this.buildMetricsFile, 'utf8'));
      
      if (history.length < 2) {
        console.log('📈 构建历史数据不足，无法分析趋势');
        return;
      }
      
      const recent = history.slice(-10); // 最近10次构建
      const avgBuildTime = recent.reduce((sum, build) => sum + build.buildTime, 0) / recent.length;
      
      console.log('\n📈 构建趋势分析:');
      console.log(`   最近10次构建平均耗时: ${Math.round(avgBuildTime)}ms`);
      
      // 构建时间趋势
      const currentTime = this.currentBuild.buildTime;
      const timeDiff = currentTime - avgBuildTime;
      const percentChange = ((timeDiff / avgBuildTime) * 100).toFixed(1);
      
      if (Math.abs(timeDiff) > 5000) { // 差异超过5秒
        const trend = timeDiff > 0 ? '📈 变慢' : '📉 变快';
        console.log(`   构建时间${trend}: ${percentChange}% (${timeDiff > 0 ? '+' : ''}${Math.round(timeDiff)}ms)`);
      } else {
        console.log('   构建时间稳定 ✅');
      }
      
      // 错误和警告趋势
      const recentErrors = recent.filter(build => build.errors.length > 0).length;
      const recentWarnings = recent.filter(build => build.warnings.length > 0).length;
      
      if (recentErrors > 0) {
        console.log(`   最近构建错误率: ${((recentErrors / recent.length) * 100).toFixed(1)}%`);
      }
      
      if (recentWarnings > 0) {
        console.log(`   最近构建警告率: ${((recentWarnings / recent.length) * 100).toFixed(1)}%`);
      }
      
    } catch (error) {
      console.warn('分析构建趋势失败:', error.message);
    }
  }

  /**
   * 生成优化建议
   */
  generateOptimizationSuggestions() {
    const suggestions = [];
    
    // 基于构建时间的建议
    if (this.currentBuild.buildTime > 30000) { // > 30秒
      suggestions.push({
        category: 'Build Performance',
        issue: '构建时间较长',
        suggestions: [
          '启用 Vite 的依赖预构建缓存',
          '使用 SWC 替代 Babel 进行更快的转译',
          '考虑使用增量构建',
          '优化 TypeScript 配置',
          '减少不必要的插件'
        ]
      });
    }
    
    // 基于错误和警告的建议
    if (this.currentBuild.errors.length > 0) {
      suggestions.push({
        category: 'Build Errors',
        issue: `发现 ${this.currentBuild.errors.length} 个构建错误`,
        suggestions: [
          '检查 TypeScript 类型错误',
          '验证导入路径的正确性',
          '确保所有依赖都已正确安装',
          '检查配置文件的语法'
        ]
      });
    }
    
    if (this.currentBuild.warnings.length > 0) {
      suggestions.push({
        category: 'Build Warnings',
        issue: `发现 ${this.currentBuild.warnings.length} 个构建警告`,
        suggestions: [
          '解决未使用的变量和导入',
          '优化大型文件的分割',
          '检查过时的 API 使用',
          '更新依赖到最新版本'
        ]
      });
    }
    
    return suggestions;
  }

  /**
   * 打印构建报告
   */
  printBuildReport() {
    console.log('\n📋 构建报告');
    console.log('='.repeat(50));
    
    const report = this.generateBuildReport();
    
    console.log(`构建时间: ${report.buildTime}ms`);
    console.log(`时间戳: ${report.timestamp}`);
    console.log(`Node.js 版本: ${report.nodeVersion}`);
    console.log(`平台: ${report.platform} (${report.arch})`);
    
    // 内存使用情况
    const memMB = (report.memory.heapUsed / 1024 / 1024).toFixed(2);
    console.log(`内存使用: ${memMB} MB`);
    
    // 错误和警告
    if (report.errors.length > 0) {
      console.log(`\n❌ 错误 (${report.errors.length}):`);
      report.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error.type}: ${error.message}`);
      });
    }
    
    if (report.warnings.length > 0) {
      console.log(`\n⚠️  警告 (${report.warnings.length}):`);
      report.warnings.forEach((warning, index) => {
        console.log(`   ${index + 1}. ${warning.type}: ${warning.message}`);
        if (warning.suggestion) {
          console.log(`      建议: ${warning.suggestion}`);
        }
      });
    }
    
    // 优化建议
    const suggestions = this.generateOptimizationSuggestions();
    if (suggestions.length > 0) {
      console.log(`\n💡 优化建议:`);
      suggestions.forEach(suggestion => {
        console.log(`\n   📌 ${suggestion.category}: ${suggestion.issue}`);
        suggestion.suggestions.forEach(s => {
          console.log(`      • ${s}`);
        });
      });
    }
    
    console.log('\n='.repeat(50));
  }

  /**
   * 运行完整的构建监控
   */
  async run() {
    try {
      this.ensureReportsDir();
      this.startBuild();
      
      // 执行构建
      console.log('🔨 执行构建...');
      try {
        execSync('npm run build', {
          cwd: this.projectRoot,
          stdio: 'inherit'
        });
      } catch (error) {
        this.currentBuild.errors.push({
          type: 'build_error',
          message: error.message
        });
      }
      
      this.endBuild();
      
      // 分析构建产物
      await this.analyzeBuildOutput();
      
      // 检查性能指标
      this.checkPerformanceMetrics();
      
      // 保存构建历史
      this.saveBuildHistory();
      
      // 分析趋势
      this.analyzeBuildTrends();
      
      // 打印报告
      this.printBuildReport();
      
      console.log('✅ 构建监控完成');
      
    } catch (error) {
      console.error('❌ 构建监控失败:', error.message);
      process.exit(1);
    }
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  const monitor = new BuildMonitor();
  monitor.run();
}

export default BuildMonitor;