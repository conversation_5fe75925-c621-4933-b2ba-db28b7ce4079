# Pennfly Private Academy 开发规范

## 📜 文档概述

**目的**: 本文档旨在为 Pennfly Private Academy (PPA) 项目提供一套统一的开发规范，以确保代码质量、可读性和可维护性。  
**适用范围**: 所有参与 PPA 项目的开发人员。  
**核心原则**: 优雅、一致、可读。

---

## 📝 代码风格规范

### 1. 命名约定

- **文件**: 使用 `kebab-case` (小写连字符)。
  - `my-component.astro`
  - `api-client.ts`
- **组件**: 使用 `PascalCase` (大驼峰)。
  - `LanguageSwitcher.astro`
  - `ArticleCard.astro`
- **变量/函数**: 使用 `camelCase` (小驼峰)。
  - `const articleList = ...`
  - `function getPosts() { ... }`
- **常量**: 使用 `UPPER_CASE_SNAKE` (大写下划线)。
  - `const API_URL = '...'`
- **CSS 类名**: 使用 `kebab-case`。
  - `.article-card`
  - `.header-nav`

### 2. 格式化

- **缩进**: 使用 2 个空格。
- **引号**: TypeScript/JavaScript 中使用单引号 `'`，除非字符串中包含单引号。
- **分号**: 在所有语句末尾使用分号。
- **行长**: 每行不超过 100 个字符。
- **工具**: 使用 Prettier 和 ESLint 自动格式化和检查代码。

### 3. Astro 规范

- **Props**: 明确定义 `interface Props`。
- **Slots**: 优先使用具名 `slot` 提高组件可读性。
- **样式**: 使用 `<style>` 标签的 scoped 样式，或 Tailwind CSS。

```astro
---
interface Props {
  title: string;
}
const { title } = Astro.props;
---
<div class="card">
  <h2>{title}</h2>
  <slot name="content" />
</div>
```

### 4. TypeScript 规范

- **类型定义**: 优先使用 `interface` 定义对象类型，使用 `type` 定义联合类型或元组。
- **any**: 严格禁止使用 `any` 类型，除非绝对必要。
- **非空断言**: 避免使用 `!` 非空断言，优先使用类型守卫。

---

## 🔧 Git 工作流规范

### 1. 分支策略

- **`main`**: 生产分支，只接受来自 `develop` 的合并请求。
- **`develop`**: 开发主分支，集成所有已完成的功能。
- **`feature/<feature-name>`**: 功能开发分支，从 `develop` 创建。
- **`fix/<issue-name>`**: Bug 修复分支，从 `develop` 创建。
- **`hotfix/<issue-name>`**: 紧急线上 Bug 修复分支，从 `main` 创建，修复后合并回 `main` 和 `develop`。

### 2. 提交规范

遵循 **Conventional Commits** 规范。

#### 格式

```text
<type>([scope]): <subject>

[body]

[footer]
```

#### 组成部分

- **Header**: `<type>([scope]): <subject>`
  - **`type`**: 提交类型（必需）
  - **`scope`**: 本次提交影响的范围（可选），如 `auth`, `api`, `ui`
  - **`subject`**: 简明扼要的提交描述，使用动词原形，首字母小写

- **Body**: 详细描述（可选），解释变更的原因和实现方式

- **Footer**: 补充信息（可选）
  - **BREAKING CHANGE**: 包含重大变更的说明
  - **关联 Issue**: 如 `Closes #123`, `Refs #456`

#### 提交类型 (type)

- `feat`: 新功能
- `fix`: 修复 Bug
- `docs`: 文档变更
- `style`: 代码格式（不影响代码逻辑）
- `refactor`: 代码重构
- `test`: 增加或修改测试
- `chore`: 构建过程或辅助工具的变动
- `perf`: 性能优化
- `ci`: CI/CD 配置变更
- `revert`: 撤销之前的提交

#### 提交示例

**简单提交**:
```text
feat(auth): add user login functionality
fix(ui): correct button alignment on mobile
docs(readme): update setup instructions
```

**完整提交**:
```text
feat(api): add user profile endpoint

Implement the new /api/v1/users/{id}/profile endpoint to retrieve user profiles.
This endpoint requires authentication and returns public user data.
```

**重大变更**:
```text
refactor(auth): switch to JWT authentication

The authentication mechanism has been updated from session-based to JWT.
This change affects all authenticated endpoints and client-side token handling.

BREAKING CHANGE: The session_id cookie is no longer used.
Clients must now send a JWT in the Authorization header.
```

### 3. 合并请求 (Pull Request)

- PR 标题应清晰明了。
- PR 描述需包含：
  - **变更内容**: 本次 PR 的主要变更。
  - **关联 Issue**: 关联的任务或 Bug 编号。
  - **测试说明**: 如何测试本次变更。
- 至少需要一名其他开发者审查通过后方可合并。

---

## 🧩 组件设计原则

1.  **单一职责**: 每个组件只做一件事。
2.  **高内聚，低耦合**: 组件内部逻辑紧密相关，组件之间依赖尽可能少。
3.  **可复用性**: 优先设计可复用的通用组件。
4.  **状态管理**: 无状态组件优先，复杂状态交由父组件或全局状态管理。
5.  **Props 设计**:
    - Props 命名清晰，布尔类型使用 `is` 或 `has` 开头 (如 `isDisabled`)。
    - 提供默认值，使组件在最少配置下可用。

---

## 🧐 代码审查 (Code Review) 规范

- **目标**: 提高代码质量，知识共享，而不是批评。
- **审查内容**:
  - **设计**: 是否遵循了设计模式和架构原则？
  - **功能**: 是否实现了需求？是否存在逻辑漏洞？
  - **可读性**: 代码是否清晰易懂？
  - **命名**: 命名是否规范？
  - **测试**: 是否有足够的测试覆盖？
- **审查者**: 提出建设性意见，使用建议（Suggestion）功能。
- **提交者**: 虚心接受意见，对每一条评论进行回复或解决。

---

## 🧪 测试规范

- **单元测试 (Vitest)**:
  - 针对工具函数、复杂的业务逻辑函数。
  - 测试覆盖率目标 > 80%。
- **组件测试 (Astro Testing Library)**:
  - 针对独立组件的渲染和交互。
  - 测试组件在不同 Props 下的行为。
- **端到端测试 (Playwright)**:
  - 模拟用户操作，测试关键业务流程 (如登录、发布文章)。
  - 每个核心功能至少有一个 E2E 测试用例。

---

## 📚 文档规范

- **代码注释**:
  - 复杂的逻辑、算法或业务规则必须有注释。
  - 对外暴露的函数和模块需有 JSDoc 注释。
- **项目文档**:
  - `README.md`: 项目简介、安装、运行方法。
  - `Development_Plan.md`: 开发计划和里程碑。
  - `Development_Specification.md`: 本开发规范。
- **API 文档**: 如果有后端 API，需提供清晰的 API 文档（如 Swagger/OpenAPI）。

---

**本规范将作为项目开发的最高准则，所有开发者应严格遵守。**

