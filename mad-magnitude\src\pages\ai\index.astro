---
import { getCollection } from 'astro:content';
import Layout from '../../layouts/Layout.astro';
import { getInstituteConfig } from '../../utils/instituteConfig';

// 获取AI研究所的所有文章
const aiArticles = await getCollection('ai');
const sortedArticles = aiArticles
  .filter((article: any) => !article.data.draft)
  .sort((a: any, b: any) => b.data.publishDate.getTime() - a.data.publishDate.getTime());

const instituteConfig = getInstituteConfig('ai')!;
---

<Layout
  title={`${instituteConfig.name} - Pennfly Private Academy`}
  description={instituteConfig.description}
>
  <main class="min-h-screen bg-gray-50">
    <!-- 页面头部 -->
    <div
      class={`bg-gradient-to-r ${instituteConfig.gradientFrom} ${instituteConfig.gradientTo} py-16 text-white`}
    >
      <div class="container mx-auto px-6">
        <div class="mb-6 flex items-center">
          <span class="mr-4 text-5xl">{instituteConfig.icon}</span>
          <div>
            <h1 class="mb-2 text-4xl font-bold">{instituteConfig.name}</h1>
            <p class="text-xl opacity-90">{instituteConfig.nameEn}</p>
          </div>
        </div>
        <p class="max-w-3xl text-lg opacity-90">
          {instituteConfig.description}
        </p>
      </div>
    </div>

    <!-- 研究领域 -->
    <div class="container mx-auto px-6 py-12">
      <div class="mb-12">
        <h2 class="mb-6 text-2xl font-bold text-gray-800">研究领域</h2>
        <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {
            instituteConfig.fields.map(field => (
              <div class="rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
                <h3 class="mb-2 font-semibold text-gray-800">{field.name}</h3>
                <p class="text-sm text-gray-600">{field.description}</p>
              </div>
            ))
          }
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="mb-12">
        <div class="grid gap-6 md:grid-cols-3">
          <div class="rounded-lg border border-gray-200 bg-white p-6 text-center shadow-sm">
            <div class="mb-2 text-3xl font-bold text-gray-800">{sortedArticles.length}</div>
            <div class="text-sm text-gray-600">研究文章</div>
          </div>
          <div class="rounded-lg border border-gray-200 bg-white p-6 text-center shadow-sm">
            <div class="mb-2 text-3xl font-bold text-gray-800">
              {sortedArticles.filter((article: any) => article.data.featured).length}
            </div>
            <div class="text-sm text-gray-600">精选文章</div>
          </div>
          <div class="rounded-lg border border-gray-200 bg-white p-6 text-center shadow-sm">
            <div class="mb-2 text-3xl font-bold text-gray-800">
              {new Set(sortedArticles.flatMap((article: any) => article.data.tags)).size}
            </div>
            <div class="text-sm text-gray-600">研究标签</div>
          </div>
        </div>
      </div>

      <!-- 文章列表 -->
      <div class="mb-12">
        <div class="mb-8 flex items-center justify-between">
          <h2 class="text-2xl font-bold text-gray-800">研究文章</h2>
          <div class="text-sm text-gray-600">
            共 {sortedArticles.length} 篇文章
          </div>
        </div>

        {
          sortedArticles.length > 0 ? (
            <div class="space-y-6">
              {sortedArticles.map((article: any) => (
                <article class="rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition-shadow hover:shadow-md">
                  <div class="mb-4 flex items-start justify-between">
                    <div class="flex-1">
                      <h3 class="mb-2 text-xl font-semibold text-gray-800">
                        <a
                          href={`/ai/${article.slug}`}
                          class="transition-colors hover:text-purple-600"
                        >
                          {article.data.title.zh}
                        </a>
                      </h3>
                      <p class="mb-3 text-gray-600">{article.data.description.zh}</p>

                      <div class="flex items-center space-x-4 text-sm text-gray-500">
                        <span>{article.data.publishDate.toLocaleDateString('zh-CN')}</span>
                        {article.data.readingTime && (
                          <span>阅读时间 {article.data.readingTime} 分钟</span>
                        )}
                        {article.data.aiField && (
                          <span class="rounded bg-purple-100 px-2 py-1 text-xs text-purple-800">
                            {article.data.aiField === 'ml'
                              ? '机器学习'
                              : article.data.aiField === 'nlp'
                                ? '自然语言处理'
                                : article.data.aiField === 'cv'
                                  ? '计算机视觉'
                                  : article.data.aiField === 'ethics'
                                    ? 'AI伦理'
                                    : article.data.aiField === 'agi'
                                      ? '通用AI'
                                      : '机器人学'}
                          </span>
                        )}
                      </div>
                    </div>

                    {article.data.featured && (
                      <div class="ml-4">
                        <span class="rounded bg-yellow-100 px-2 py-1 text-xs font-medium text-yellow-800">
                          精选
                        </span>
                      </div>
                    )}
                  </div>

                  {/* 技术栈标签 */}
                  {article.data.techStack && article.data.techStack.length > 0 && (
                    <div class="mb-3">
                      <div class="mb-1 text-xs text-gray-500">技术栈:</div>
                      <div class="flex flex-wrap gap-1">
                        {article.data.techStack.map((tech: string) => (
                          <span class="rounded bg-blue-100 px-2 py-1 text-xs text-blue-700">
                            {tech}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* 模型标签 */}
                  {article.data.models && article.data.models.length > 0 && (
                    <div class="mb-3">
                      <div class="mb-1 text-xs text-gray-500">相关模型:</div>
                      <div class="flex flex-wrap gap-1">
                        {article.data.models.map((model: string) => (
                          <span class="rounded bg-indigo-100 px-2 py-1 text-xs text-indigo-700">
                            {model}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {article.data.tags.length > 0 && (
                    <div class="flex flex-wrap gap-2">
                      {article.data.tags.map((tag: string) => (
                        <span class="rounded bg-gray-100 px-2 py-1 text-xs text-gray-700">
                          {tag}
                        </span>
                      ))}
                    </div>
                  )}
                </article>
              ))}
            </div>
          ) : (
            <div class="py-12 text-center">
              <div class="mb-4 text-6xl">{instituteConfig.icon}</div>
              <h3 class="mb-2 text-xl font-semibold text-gray-800">暂无研究文章</h3>
              <p class="mb-6 text-gray-600">
                {instituteConfig.name}正在筹备中，敬请期待精彩的研究内容
              </p>
              <a
                href="/admin"
                class="inline-block rounded-lg bg-purple-600 px-6 py-2 text-white transition-colors hover:bg-purple-700"
              >
                发布文章
              </a>
            </div>
          )
        }
      </div>

      <!-- 返回首页 -->
      <div class="text-center">
        <a
          href="/"
          class="inline-block rounded-lg bg-gray-600 px-6 py-2 text-white transition-colors hover:bg-gray-700"
        >
          ← 返回研究院首页
        </a>
      </div>
    </div>
  </main>
</Layout>
