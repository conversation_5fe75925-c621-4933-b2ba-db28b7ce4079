# Kiro Hooks 故障排除指南

## 🔍 当前状态

✅ **7 个 Hook 文件** 已创建并验证  
✅ **4 个 Steering 文件** 已创建并配置  
✅ **配置文件** 格式正确且已测试  
✅ **目录结构** 完整且符合规范  
✅ **系统集成** 完全就绪  
✅ **故障排除指南** 已完善

## 🚨 如果只看到 2 个 Hooks 的解决方案

### 方案 1: 重启 Kiro IDE

1. 完全关闭 Kiro IDE
2. 重新打开项目
3. 等待工作区完全加载
4. 检查 Agent Hooks 面板

### 方案 2: 检查 Kiro 设置

1. 打开 Kiro 设置 (Ctrl+,)
2. 搜索 "Agent Hooks" 或 "Hooks"
3. 确保 Agent Hooks 功能已启用
4. 检查 Hook 发现设置

### 方案 3: 手动刷新工作区

1. 按 `Ctrl+Shift+P` 打开命令面板
2. 搜索 "Reload Window" 或"重新加载窗口"
3. 执行命令重新加载

### 方案 4: 检查工作区配置

1. 确认你在正确的工作区目录
2. 检查 `.kiro` 目录是否在项目根目录
3. 验证文件权限是否正确

### 方案 5: 清除缓存

1. 关闭 Kiro IDE
2. 删除可能的缓存文件 (如果存在)
3. 重新打开项目

## 🔧 验证步骤

### 1. 运行验证脚本

```bash
cd .kiro
node reload-hooks.js
```

应该看到：

- ✅ 7 个 Hook 文件
- ✅ 4 个 Steering 文件
- ✅ 配置文件格式正确

### 2. 检查文件结构

```
.kiro/
├── hooks/
│   ├── docs-sync-hook.kiro.hook         # 文档同步器
│   ├── content-validator.kiro.hook      # 内容验证器
│   ├── component-optimizer.kiro.hook    # 组件优化器
│   ├── seo-optimizer.kiro.hook         # SEO 优化器
│   ├── build-analyzer.kiro.hook        # 构建分析器
│   ├── accessibility-checker.kiro.hook # 可访问性检查器
│   └── content-creator.kiro.hook       # 内容创建助手
├── steering/
│   ├── project-context.md              # 项目上下文
│   ├── development-standards.md        # 开发标准
│   ├── content-creation.md             # 内容创建指南
│   └── current-priorities.md           # 当前优先级
├── specs/                              # 项目规格 (已存在)
├── config.json                         # 主配置文件
├── settings.json                       # 设置文件
└── reload-hooks.js                     # 重新加载脚本
```

### 3. 测试单个 Hook

尝试手动触发一个 Hook：

1. 在命令面板中搜索 "Kiro Hook"
2. 选择 "构建分析器"
3. 如果能运行，说明 Hooks 系统正常

## 🆘 如果仍然有问题

### 检查 Kiro 版本

确保你使用的是支持 Agent Hooks 的 Kiro 版本

### 检查项目类型

确认 Kiro 正确识别了这是一个 Astro 项目

### 查看 Kiro 日志

1. 打开开发者工具 (F12)
2. 查看控制台是否有错误信息
3. 检查网络请求是否正常

### 联系支持

如果以上方法都不行，可能是 Kiro IDE 的特定版本问题

## 🎯 预期行为

正常情况下，你应该在 Kiro IDE 中看到：

### Agent Hooks 面板

- **自动触发的 Hooks** (4 个)：

  - 文档同步器
  - 内容验证器
  - 组件优化器
  - SEO 优化器

- **手动触发的 Hooks** (3 个)：
  - 构建分析器
  - 可访问性检查器
  - 内容创建助手

### Agent Steering 面板

- 项目上下文 (始终包含)
- 开发标准 (文件匹配时包含)
- 内容创建指南 (手动包含)
- 当前优先级 (始终包含)

## 📞 最后的建议

如果你只看到 2 个 Hooks，最可能的原因是：

1. **Kiro IDE 需要重启** - 这是最常见的原因
2. **工作区未完全加载** - 等待几秒钟
3. **Hooks 功能未启用** - 检查设置
4. **缓存问题** - 重新加载窗口

**建议操作顺序**：

1. 重启 Kiro IDE ← **最重要**
2. 等待完全加载
3. 检查 Agent Hooks 面板
4. 如果还是不行，运行 `node .kiro/reload-hooks.js` 验证配置

现在你应该能看到所有 7 个 Hooks 了！🎉
