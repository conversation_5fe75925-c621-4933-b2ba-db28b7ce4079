---
/**
 * Enhanced Typography component for Pennfly Private Academy
 * Provides consistent text styling with semantic HTML and accessibility features
 */

export interface Props {
  as?:
    | 'h1'
    | 'h2'
    | 'h3'
    | 'h4'
    | 'h5'
    | 'h6'
    | 'p'
    | 'span'
    | 'div'
    | 'blockquote'
    | 'code'
    | 'pre';
  variant?:
    | 'display'
    | 'h1'
    | 'h2'
    | 'h3'
    | 'h4'
    | 'h5'
    | 'h6'
    | 'body'
    | 'lead'
    | 'small'
    | 'caption'
    | 'code'
    | 'quote';
  weight?: 'thin' | 'light' | 'normal' | 'medium' | 'semibold' | 'bold' | 'extrabold';
  align?: 'left' | 'center' | 'right' | 'justify';
  color?:
    | 'primary'
    | 'secondary'
    | 'tertiary'
    | 'inverse'
    | 'brand'
    | 'success'
    | 'warning'
    | 'error'
    | 'info';
  family?: 'sans' | 'serif' | 'mono' | 'math';
  truncate?: boolean;
  noWrap?: boolean;
  class?: string;
}

const {
  as = 'p',
  variant = 'body',
  weight = 'normal',
  align = 'left',
  color = 'primary',
  family = 'sans',
  truncate = false,
  noWrap = false,
  class: className = '',
  ...rest
} = Astro.props;

// Typography variant configurations
const variantClasses = {
  display: 'text-4xl md:text-5xl lg:text-6xl leading-tight tracking-tight',
  h1: 'text-3xl md:text-4xl leading-tight tracking-tight',
  h2: 'text-2xl md:text-3xl leading-tight tracking-tight',
  h3: 'text-xl md:text-2xl leading-snug tracking-tight',
  h4: 'text-lg md:text-xl leading-snug',
  h5: 'text-base md:text-lg leading-snug',
  h6: 'text-sm md:text-base leading-snug',
  body: 'text-base leading-relaxed',
  lead: 'text-lg md:text-xl leading-relaxed',
  small: 'text-sm leading-normal',
  caption: 'text-xs leading-normal',
  code: 'text-sm font-mono leading-normal',
  quote: 'text-lg md:text-xl leading-relaxed italic',
};

// Font weight configurations
const weightClasses = {
  thin: 'font-thin',
  light: 'font-light',
  normal: 'font-normal',
  medium: 'font-medium',
  semibold: 'font-semibold',
  bold: 'font-bold',
  extrabold: 'font-extrabold',
};

// Text alignment configurations
const alignClasses = {
  left: 'text-left',
  center: 'text-center',
  right: 'text-right',
  justify: 'text-justify',
};

// Color configurations using theme-aware classes
const colorClasses = {
  primary: 'text-theme-fg-primary',
  secondary: 'text-theme-fg-secondary',
  tertiary: 'text-theme-fg-tertiary',
  inverse: 'text-theme-fg-inverse',
  brand: 'text-theme-brand-primary',
  success: 'text-theme-semantic-success',
  warning: 'text-theme-semantic-warning',
  error: 'text-theme-semantic-error',
  info: 'text-theme-semantic-info',
};

// Font family configurations
const familyClasses = {
  sans: 'font-sans',
  serif: 'font-serif',
  mono: 'font-mono',
  math: 'font-math',
};

// Utility classes
const utilityClasses = [truncate && 'truncate', noWrap && 'whitespace-nowrap']
  .filter(Boolean)
  .join(' ');

// Default font weights for different variants
const defaultWeights = {
  display: 'extrabold',
  h1: 'bold',
  h2: 'bold',
  h3: 'semibold',
  h4: 'semibold',
  h5: 'medium',
  h6: 'medium',
  body: 'normal',
  lead: 'normal',
  small: 'normal',
  caption: 'normal',
  code: 'normal',
  quote: 'normal',
};

// Use default weight if not specified
const finalWeight =
  weight === 'normal' && defaultWeights[variant] ? defaultWeights[variant] : weight;

// Combine all classes
const combinedClasses = `
  ${variantClasses[variant]}
  ${weightClasses[finalWeight]}
  ${alignClasses[align]}
  ${colorClasses[color]}
  ${familyClasses[family]}
  ${utilityClasses}
  ${className}
`
  .trim()
  .replace(/\s+/g, ' ');

// Component mapping for semantic HTML
const Component = as;
---

<Component class={combinedClasses} {...rest}>
  <slot />
</Component>

<style>
  /* Enhanced readability for body text */
  .font-serif {
    font-feature-settings:
      'liga' 1,
      'kern' 1;
  }

  /* Math font optimization */
  .font-math {
    font-feature-settings: 'kern' 1;
    font-variant-numeric: normal;
  }

  /* Code font optimization */
  .font-mono {
    font-feature-settings:
      'liga' 0,
      'calt' 0;
    font-variant-ligatures: none;
  }

  /* Improved text rendering */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    text-rendering: optimizeLegibility;
    font-feature-settings:
      'liga' 1,
      'kern' 1;
  }

  p,
  span,
  div {
    text-rendering: optimizeSpeed;
  }

  /* Quote styling */
  blockquote {
    position: relative;
    padding-left: 1.5rem;
    border-left: 4px solid var(--color-brand-primary);
  }

  blockquote::before {
    content: '"';
    position: absolute;
    left: -0.25rem;
    top: -0.5rem;
    font-size: 3rem;
    color: var(--color-brand-primary);
    opacity: 0.3;
    font-family: serif;
  }

  /* Code styling */
  code {
    background-color: var(--color-background-tertiary);
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-size: 0.875em;
  }

  pre {
    background-color: var(--color-background-tertiary);
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    line-height: 1.5;
  }

  pre code {
    background: none;
    padding: 0;
    font-size: inherit;
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      font-weight: 900;
    }

    code,
    pre {
      border: 1px solid currentColor;
    }
  }

  /* Print styles */
  @media print {
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      page-break-after: avoid;
      page-break-inside: avoid;
    }

    p,
    div,
    span {
      orphans: 3;
      widows: 3;
    }

    blockquote {
      page-break-inside: avoid;
    }
  }

  /* Focus styles for interactive text elements */
  [tabindex]:focus-visible {
    outline: 2px solid var(--color-interactive-focus);
    outline-offset: 2px;
    border-radius: 0.25rem;
  }
</style>
