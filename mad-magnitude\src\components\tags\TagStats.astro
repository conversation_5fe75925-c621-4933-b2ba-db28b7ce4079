---
/**
 * 标签统计组件
 * 展示标签使用统计和分析数据
 */
import { globalTagManager } from '../../utils/tagManager';

export interface Props {
  showCategories?: boolean;
  showTrends?: boolean;
  compact?: boolean;
}

const { showCategories = true, showTrends = true, compact = false } = Astro.props;

// 获取标签统计数据
const tagStats = globalTagManager
  ? await globalTagManager.getTagStats()
  : {
      totalTags: 0,
      totalUniqueContent: 0,
      mostPopularTags: [],
      tagsByCategory: {},
      recentTags: [],
    };

// 计算分类统计
const categoryStats = Object.entries(tagStats.tagsByCategory)
  .map(([category, tags]) => ({
    name: category,
    count: tags.length,
    totalUsage: tags.reduce((sum, tag) => sum + tag.count, 0),
    topTag: tags[0]?.name || '',
    color: tags[0]?.color || '#64748b',
  }))
  .sort((a, b) => b.totalUsage - a.totalUsage);

// 获取分类显示名称
function getCategoryDisplayName(category: string): string {
  const names: Record<string, string> = {
    technology: '技术',
    economics: '经济',
    philosophy: '哲学',
    society: '社会',
    research: '研究',
    tools: '工具',
    general: '通用',
  };
  return names[category] || category;
}

// 获取分类图标
function getCategoryIcon(category: string): string {
  const icons: Record<string, string> = {
    technology: '🔬',
    economics: '💰',
    philosophy: '🤔',
    society: '🏛️',
    research: '📊',
    tools: '🛠️',
    general: '📝',
  };
  return icons[category] || '📝';
}
---

<div class={`tag-stats ${compact ? 'tag-stats--compact' : ''}`}>
  <!-- 总体统计 -->
  <div class="stats-overview">
    <div class="stat-card">
      <div class="stat-icon">🏷️</div>
      <div class="stat-content">
        <div class="stat-number">{tagStats.totalTags}</div>
        <div class="stat-label">总标签数</div>
      </div>
    </div>
    <div class="stat-card">
      <div class="stat-icon">📚</div>
      <div class="stat-content">
        <div class="stat-number">{tagStats.totalUniqueContent}</div>
        <div class="stat-label">标签使用次数</div>
      </div>
    </div>
    <div class="stat-card">
      <div class="stat-icon">⭐</div>
      <div class="stat-content">
        <div class="stat-number">{tagStats.mostPopularTags[0]?.count || 0}</div>
        <div class="stat-label">最热标签使用数</div>
      </div>
    </div>
  </div>

  {
    showCategories && (
      <div class="category-stats">
        <h3 class="section-title">
          <span class="title-icon">📊</span>
          标签分类统计
        </h3>
        <div class="category-grid">
          {categoryStats.map(category => (
            <div class="category-card" style={`--category-color: ${category.color}`}>
              <div class="category-header">
                <span class="category-icon">{getCategoryIcon(category.name)}</span>
                <span class="category-name">{getCategoryDisplayName(category.name)}</span>
              </div>
              <div class="category-metrics">
                <div class="metric">
                  <span class="metric-value">{category.count}</span>
                  <span class="metric-label">个标签</span>
                </div>
                <div class="metric">
                  <span class="metric-value">{category.totalUsage}</span>
                  <span class="metric-label">次使用</span>
                </div>
              </div>
              {category.topTag && (
                <div class="category-top-tag">
                  <span class="top-tag-label">热门:</span>
                  <span class="top-tag-name">{category.topTag}</span>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    )
  }

  {
    showTrends && tagStats.recentTags.length > 0 && (
      <div class="trend-stats">
        <h3 class="section-title">
          <span class="title-icon">📈</span>
          热门标签趋势
        </h3>
        <div class="trend-list">
          {tagStats.recentTags.slice(0, 8).map((tagName, index) => {
            const tagInfo = tagStats.mostPopularTags.find(t => t.name === tagName);
            return (
              tagInfo && (
                <div class="trend-item">
                  <div class="trend-rank">#{index + 1}</div>
                  <div class="trend-content">
                    <span class="trend-name">{tagName}</span>
                    <span class="trend-count">{tagInfo.count} 次</span>
                  </div>
                  <div class="trend-bar">
                    <div
                      class="trend-fill"
                      style={`width: ${(tagInfo.count / tagStats.mostPopularTags[0].count) * 100}%; background-color: ${tagInfo.color}`}
                    />
                  </div>
                </div>
              )
            );
          })}
        </div>
      </div>
    )
  }
</div>

<style>
  .tag-stats {
    background: #ffffff;
    border-radius: 0.75rem;
    border: 1px solid #e2e8f0;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .tag-stats--compact {
    padding: 1rem;
  }

  /* 总体统计 */
  .stats-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .stat-card {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
  }

  .stat-icon {
    font-size: 1.5rem;
    opacity: 0.8;
  }

  .stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    line-height: 1;
  }

  .stat-label {
    font-size: 0.875rem;
    color: #64748b;
    margin-top: 0.25rem;
  }

  /* 分类统计 */
  .section-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
  }

  .title-icon {
    font-size: 1.25rem;
  }

  .category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .category-card {
    padding: 1rem;
    background: rgba(var(--category-color-rgb, 100, 116, 139), 0.05);
    border: 1px solid rgba(var(--category-color-rgb, 100, 116, 139), 0.2);
    border-radius: 0.5rem;
    transition: all 0.2s ease;
  }

  .category-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .category-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
  }

  .category-icon {
    font-size: 1.25rem;
  }

  .category-name {
    font-weight: 600;
    color: #1e293b;
  }

  .category-metrics {
    display: flex;
    gap: 1rem;
    margin-bottom: 0.75rem;
  }

  .metric {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .metric-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--category-color, #64748b);
    line-height: 1;
  }

  .metric-label {
    font-size: 0.75rem;
    color: #64748b;
    margin-top: 0.25rem;
  }

  .category-top-tag {
    padding-top: 0.75rem;
    border-top: 1px solid rgba(var(--category-color-rgb, 100, 116, 139), 0.2);
    font-size: 0.875rem;
  }

  .top-tag-label {
    color: #64748b;
  }

  .top-tag-name {
    font-weight: 500;
    color: var(--category-color, #64748b);
    margin-left: 0.25rem;
  }

  /* 趋势统计 */
  .trend-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .trend-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: #f8fafc;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
  }

  .trend-rank {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    background: #3b82f6;
    color: white;
    border-radius: 50%;
    font-size: 0.875rem;
    font-weight: 600;
    flex-shrink: 0;
  }

  .trend-content {
    display: flex;
    flex-direction: column;
    min-width: 0;
    flex: 1;
  }

  .trend-name {
    font-weight: 500;
    color: #1e293b;
    line-height: 1.2;
  }

  .trend-count {
    font-size: 0.875rem;
    color: #64748b;
  }

  .trend-bar {
    width: 60px;
    height: 4px;
    background: #e2e8f0;
    border-radius: 2px;
    overflow: hidden;
    flex-shrink: 0;
  }

  .trend-fill {
    height: 100%;
    border-radius: 2px;
    transition: width 0.3s ease;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .tag-stats {
      padding: 1rem;
    }

    .stats-overview {
      grid-template-columns: 1fr;
      gap: 0.75rem;
      margin-bottom: 1.5rem;
    }

    .stat-card {
      padding: 0.75rem;
    }

    .category-grid {
      grid-template-columns: 1fr;
      gap: 0.75rem;
    }

    .trend-item {
      padding: 0.5rem;
    }

    .trend-bar {
      width: 40px;
    }
  }

  /* 紧凑模式 */
  .tag-stats--compact .stats-overview {
    margin-bottom: 1rem;
  }

  .tag-stats--compact .section-title {
    font-size: 1rem;
    margin-bottom: 0.75rem;
  }

  .tag-stats--compact .category-grid {
    margin-bottom: 1rem;
  }
</style>

<script>
  // 为分类卡片添加颜色 CSS 变量
  document.addEventListener('DOMContentLoaded', () => {
    const categoryCards = document.querySelectorAll('.category-card[style*="--category-color"]');
    categoryCards.forEach(card => {
      const htmlCard = card as HTMLElement;
      const colorValue = htmlCard.style.getPropertyValue('--category-color');
      if (colorValue) {
        // 将十六进制颜色转换为 RGB
        const hex = colorValue.replace('#', '');
        const r = parseInt(hex.substring(0, 2), 16);
        const g = parseInt(hex.substring(2, 4), 16);
        const b = parseInt(hex.substring(4, 6), 16);
        htmlCard.style.setProperty('--category-color-rgb', `${r}, ${g}, ${b}`);
      }
    });
  });
</script>
