---
/**
 * 内容列表组件
 * 显示内容列表，支持筛选和操作
 */
export interface Props {
  initialContent?: any[];
  showFilters?: boolean;
  showActions?: boolean;
  compact?: boolean;
}

const {
  initialContent = [],
  showFilters = true,
  showActions = true,
  compact = false,
} = Astro.props;

// 获取集合显示名称
function getCollectionDisplayName(collection: string): string {
  const names: Record<string, string> = {
    news: '动态资讯',
    logs: '研究日志',
    research: '研究报告',
    reflections: '反思记录',
    economics: '经济研究',
    philosophy: '哲学研究',
    internet: '互联网研究',
    ai: 'AI研究',
    future: '未来研究',
    products: '产品发布',
  };
  return names[collection] || collection;
}

// 获取集合图标
function getCollectionIcon(collection: string): string {
  const icons: Record<string, string> = {
    news: '📰',
    logs: '📔',
    research: '📊',
    reflections: '💭',
    economics: '💰',
    philosophy: '🤔',
    internet: '🌐',
    ai: '🤖',
    future: '🔮',
    products: '🛠️',
  };
  return icons[collection] || '📄';
}
---

<div class={`content-list ${compact ? 'content-list--compact' : ''}`}>
  {
    showFilters && (
      <div class="content-filters">
        <div class="filters-row">
          <div class="filter-group">
            <label for="collection-filter" class="filter-label">
              集合
            </label>
            <select id="collection-filter" class="filter-select">
              <option value="">所有集合</option>
              <option value="news">📰 动态资讯</option>
              <option value="logs">📔 研究日志</option>
              <option value="research">📊 研究报告</option>
              <option value="reflections">💭 反思记录</option>
              <option value="economics">💰 经济研究</option>
              <option value="philosophy">🤔 哲学研究</option>
              <option value="internet">🌐 互联网研究</option>
              <option value="ai">🤖 AI研究</option>
              <option value="future">🔮 未来研究</option>
              <option value="products">🛠️ 产品发布</option>
            </select>
          </div>

          <div class="filter-group">
            <label for="status-filter" class="filter-label">
              状态
            </label>
            <select id="status-filter" class="filter-select">
              <option value="">所有状态</option>
              <option value="published">已发布</option>
              <option value="draft">草稿</option>
              <option value="featured">特色内容</option>
            </select>
          </div>

          <div class="filter-group">
            <label for="author-filter" class="filter-label">
              作者
            </label>
            <select id="author-filter" class="filter-select">
              <option value="">所有作者</option>
              <option value="Pennfly">Pennfly</option>
            </select>
          </div>

          <div class="filter-group">
            <label for="search-input" class="filter-label">
              搜索
            </label>
            <input
              type="text"
              id="search-input"
              class="filter-input"
              placeholder="搜索标题、内容..."
            />
          </div>

          <div class="filter-actions">
            <button id="apply-filters" class="btn btn--primary">
              筛选
            </button>
            <button id="clear-filters" class="btn btn--secondary">
              清除
            </button>
          </div>
        </div>
      </div>
    )
  }

  <div class="content-table-container">
    <div class="table-header">
      <div class="table-actions">
        {
          showActions && (
            <button id="create-content" class="btn btn--success">
              <span class="btn-icon">➕</span>
              新建内容
            </button>
          )
        }
        <button id="refresh-list" class="btn btn--secondary">
          <span class="btn-icon">🔄</span>
          刷新
        </button>
      </div>
      <div class="table-info">
        <span id="content-count">加载中...</span>
      </div>
    </div>

    <div class="table-wrapper">
      <table class="content-table">
        <thead>
          <tr>
            <th class="col-title">标题</th>
            <th class="col-collection">集合</th>
            <th class="col-status">状态</th>
            <th class="col-author">作者</th>
            <th class="col-date">更新时间</th>
            {showActions && <th class="col-actions">操作</th>}
          </tr>
        </thead>
        <tbody id="content-table-body">
          <tr>
            <td colspan="6" class="loading-row">
              <div class="loading-spinner"></div>
              <span>加载内容中...</span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="table-pagination" id="pagination-container" style="display: none;">
      <div class="pagination-info">
        <span id="pagination-info-text"></span>
      </div>
      <div class="pagination-controls">
        <button id="prev-page" class="btn btn--secondary" disabled>上一页</button>
        <span id="page-numbers"></span>
        <button id="next-page" class="btn btn--secondary" disabled>下一页</button>
      </div>
    </div>
  </div>
</div>

<style>
  .content-list {
    background: white;
    border-radius: 0.75rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .content-list--compact {
    border-radius: 0.5rem;
    box-shadow: none;
  }

  /* 筛选器 */
  .content-filters {
    padding: 1.5rem;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
  }

  .filters-row {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: end;
  }

  .filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 150px;
  }

  .filter-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
  }

  .filter-select,
  .filter-input {
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    background: white;
    transition: border-color 0.2s ease;
  }

  .filter-select:focus,
  .filter-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .filter-actions {
    display: flex;
    gap: 0.5rem;
    margin-left: auto;
  }

  /* 按钮样式 */
  .btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
  }

  .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .btn--primary {
    background: #3b82f6;
    color: white;
  }

  .btn--primary:hover:not(:disabled) {
    background: #2563eb;
  }

  .btn--secondary {
    background: #f1f5f9;
    color: #64748b;
    border: 1px solid #e2e8f0;
  }

  .btn--secondary:hover:not(:disabled) {
    background: #e2e8f0;
    color: #475569;
  }

  .btn--success {
    background: #10b981;
    color: white;
  }

  .btn--success:hover:not(:disabled) {
    background: #059669;
  }

  .btn--danger {
    background: #ef4444;
    color: white;
  }

  .btn--danger:hover:not(:disabled) {
    background: #dc2626;
  }

  .btn-icon {
    font-size: 1rem;
  }

  /* 表格容器 */
  .content-table-container {
    padding: 1.5rem;
  }

  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }

  .table-actions {
    display: flex;
    gap: 0.5rem;
  }

  .table-info {
    font-size: 0.875rem;
    color: #64748b;
  }

  .table-wrapper {
    overflow-x: auto;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
  }

  .content-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
  }

  .content-table th {
    background: #f8fafc;
    padding: 0.75rem 1rem;
    text-align: left;
    font-weight: 600;
    color: #374151;
    border-bottom: 1px solid #e2e8f0;
  }

  .content-table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f1f5f9;
    vertical-align: middle;
  }

  .content-table tbody tr:hover {
    background: #f8fafc;
  }

  .col-title {
    min-width: 200px;
  }

  .col-collection {
    width: 120px;
  }

  .col-status {
    width: 100px;
  }

  .col-author {
    width: 100px;
  }

  .col-date {
    width: 140px;
  }

  .col-actions {
    width: 120px;
  }

  /* 状态标签 */
  .status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .status-badge--published {
    background: #dcfce7;
    color: #166534;
  }

  .status-badge--draft {
    background: #fef3c7;
    color: #92400e;
  }

  .status-badge--featured {
    background: #dbeafe;
    color: #1e40af;
  }

  /* 集合标签 */
  .collection-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    color: #64748b;
  }

  /* 加载状态 */
  .loading-row {
    text-align: center;
    padding: 2rem;
    color: #64748b;
  }

  .loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #e2e8f0;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 0.5rem;
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  /* 分页 */
  .table-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e2e8f0;
  }

  .pagination-info {
    font-size: 0.875rem;
    color: #64748b;
  }

  .pagination-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .page-number {
    padding: 0.25rem 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .page-number:hover {
    background: #f1f5f9;
  }

  .page-number.active {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
  }

  /* 响应式设计 */
  @media (max-width: 1024px) {
    .filters-row {
      flex-direction: column;
      align-items: stretch;
    }

    .filter-group {
      min-width: auto;
    }

    .filter-actions {
      margin-left: 0;
      justify-content: flex-end;
    }

    .table-header {
      flex-direction: column;
      gap: 1rem;
      align-items: stretch;
    }

    .table-actions {
      justify-content: center;
    }
  }

  @media (max-width: 768px) {
    .content-table-container {
      padding: 1rem;
    }

    .content-filters {
      padding: 1rem;
    }

    .table-pagination {
      flex-direction: column;
      gap: 1rem;
    }

    .pagination-controls {
      justify-content: center;
    }
  }
</style>

<script>
  class ContentListManager {
    private currentPage = 1;
    private currentFilters: Record<string, any> = {};
    private totalPages = 1;

    constructor() {
      this.bindEvents();
      this.loadContent();
    }

    private bindEvents() {
      // 筛选器事件
      const applyFiltersBtn = document.getElementById('apply-filters');
      const clearFiltersBtn = document.getElementById('clear-filters');
      const refreshBtn = document.getElementById('refresh-list');
      const createBtn = document.getElementById('create-content');

      applyFiltersBtn?.addEventListener('click', () => this.applyFilters());
      clearFiltersBtn?.addEventListener('click', () => this.clearFilters());
      refreshBtn?.addEventListener('click', () => this.loadContent());
      createBtn?.addEventListener('click', () => this.createContent());

      // 搜索输入框回车事件
      const searchInput = document.getElementById('search-input') as HTMLInputElement;
      searchInput?.addEventListener('keypress', e => {
        if (e.key === 'Enter') {
          this.applyFilters();
        }
      });

      // 分页事件
      const prevBtn = document.getElementById('prev-page');
      const nextBtn = document.getElementById('next-page');

      prevBtn?.addEventListener('click', () => this.goToPage(this.currentPage - 1));
      nextBtn?.addEventListener('click', () => this.goToPage(this.currentPage + 1));
    }

    private async loadContent(page = 1) {
      try {
        const params = new URLSearchParams({
          page: page.toString(),
          limit: '20',
          ...this.currentFilters,
        });

        const response = await fetch(`/api/content/list?${params}`);
        const data = await response.json();

        if (response.ok) {
          this.renderContent(data.content);
          this.renderPagination(data.pagination);
          this.updateContentCount(data.pagination.total);
        } else {
          this.showError('加载内容失败: ' + data.error);
        }
      } catch (error) {
        this.showError('网络错误: ' + error);
      }
    }

    private renderContent(content: any[]) {
      const tbody = document.getElementById('content-table-body');
      if (!tbody) return;

      if (content.length === 0) {
        tbody.innerHTML = `
          <tr>
            <td colspan="6" class="loading-row">
              <span>没有找到内容</span>
            </td>
          </tr>
        `;
        return;
      }

      tbody.innerHTML = content
        .map(
          item => `
        <tr data-id="${item.id}">
          <td class="col-title">
            <div>
              <div class="font-medium text-gray-900">${item.title}</div>
              ${item.description ? `<div class="text-sm text-gray-500 mt-1">${item.description}</div>` : ''}
            </div>
          </td>
          <td class="col-collection">
            <span class="collection-badge">
              ${this.getCollectionIcon(item.collection)}
              ${this.getCollectionDisplayName(item.collection)}
            </span>
          </td>
          <td class="col-status">
            ${this.renderStatusBadge(item)}
          </td>
          <td class="col-author">${item.author}</td>
          <td class="col-date">
            <div class="text-sm">
              ${this.formatDate(item.updateDate || item.publishDate)}
            </div>
          </td>
          <td class="col-actions">
            <div class="flex gap-1">
              <button onclick="contentList.editContent('${item.id}')" class="btn btn--secondary btn--sm" title="编辑">
                ✏️
              </button>
              <button onclick="contentList.deleteContent('${item.id}')" class="btn btn--danger btn--sm" title="删除">
                🗑️
              </button>
            </div>
          </td>
        </tr>
      `
        )
        .join('');
    }

    private renderStatusBadge(item: any): string {
      if (item.featured) {
        return '<span class="status-badge status-badge--featured">⭐ 特色</span>';
      } else if (item.draft) {
        return '<span class="status-badge status-badge--draft">📝 草稿</span>';
      } else {
        return '<span class="status-badge status-badge--published">✅ 已发布</span>';
      }
    }

    private renderPagination(pagination: any) {
      const container = document.getElementById('pagination-container');
      const infoText = document.getElementById('pagination-info-text');
      const pageNumbers = document.getElementById('page-numbers');
      const prevBtn = document.getElementById('prev-page') as HTMLButtonElement;
      const nextBtn = document.getElementById('next-page') as HTMLButtonElement;

      if (!container || !infoText || !pageNumbers || !prevBtn || !nextBtn) return;

      this.currentPage = pagination.page;
      this.totalPages = pagination.totalPages;

      // 显示分页信息
      const start = (pagination.page - 1) * pagination.limit + 1;
      const end = Math.min(pagination.page * pagination.limit, pagination.total);
      infoText.textContent = `显示 ${start}-${end} 条，共 ${pagination.total} 条`;

      // 更新按钮状态
      prevBtn.disabled = !pagination.hasPrev;
      nextBtn.disabled = !pagination.hasNext;

      // 生成页码
      const pages = this.generatePageNumbers(pagination.page, pagination.totalPages);
      pageNumbers.innerHTML = pages
        .map(page => {
          if (page === '...') {
            return '<span class="px-2">...</span>';
          }
          return `
          <button 
            class="page-number ${page === pagination.page ? 'active' : ''}"
            onclick="contentList.goToPage(${page})"
          >
            ${page}
          </button>
        `;
        })
        .join('');

      container.style.display = pagination.totalPages > 1 ? 'flex' : 'none';
    }

    private generatePageNumbers(current: number, total: number): (number | string)[] {
      const pages: (number | string)[] = [];

      if (total <= 7) {
        for (let i = 1; i <= total; i++) {
          pages.push(i);
        }
      } else {
        pages.push(1);

        if (current > 4) {
          pages.push('...');
        }

        const start = Math.max(2, current - 1);
        const end = Math.min(total - 1, current + 1);

        for (let i = start; i <= end; i++) {
          pages.push(i);
        }

        if (current < total - 3) {
          pages.push('...');
        }

        pages.push(total);
      }

      return pages;
    }

    private updateContentCount(total: number) {
      const countElement = document.getElementById('content-count');
      if (countElement) {
        countElement.textContent = `共 ${total} 条内容`;
      }
    }

    private applyFilters() {
      const collectionFilter = document.getElementById('collection-filter') as HTMLSelectElement;
      const statusFilter = document.getElementById('status-filter') as HTMLSelectElement;
      const authorFilter = document.getElementById('author-filter') as HTMLSelectElement;
      const searchInput = document.getElementById('search-input') as HTMLInputElement;

      this.currentFilters = {};

      if (collectionFilter?.value) {
        this.currentFilters.collection = collectionFilter.value;
      }

      if (statusFilter?.value) {
        if (statusFilter.value === 'published') {
          this.currentFilters.draft = 'false';
        } else if (statusFilter.value === 'draft') {
          this.currentFilters.draft = 'true';
        } else if (statusFilter.value === 'featured') {
          this.currentFilters.featured = 'true';
        }
      }

      if (authorFilter?.value) {
        this.currentFilters.author = authorFilter.value;
      }

      if (searchInput?.value.trim()) {
        this.currentFilters.search = searchInput.value.trim();
      }

      this.currentPage = 1;
      this.loadContent(1);
    }

    private clearFilters() {
      const collectionFilter = document.getElementById('collection-filter') as HTMLSelectElement;
      const statusFilter = document.getElementById('status-filter') as HTMLSelectElement;
      const authorFilter = document.getElementById('author-filter') as HTMLSelectElement;
      const searchInput = document.getElementById('search-input') as HTMLInputElement;

      if (collectionFilter) collectionFilter.value = '';
      if (statusFilter) statusFilter.value = '';
      if (authorFilter) authorFilter.value = '';
      if (searchInput) searchInput.value = '';

      this.currentFilters = {};
      this.currentPage = 1;
      this.loadContent(1);
    }

    public goToPage(page: number) {
      if (page < 1 || page > this.totalPages) return;
      this.currentPage = page;
      this.loadContent(page);
    }

    public editContent(id: string) {
      window.location.href = `/admin/content/edit/${encodeURIComponent(id)}`;
    }

    public async deleteContent(id: string) {
      if (!confirm('确定要删除这个内容吗？此操作不可恢复。')) {
        return;
      }

      try {
        const response = await fetch(`/api/content/${encodeURIComponent(id)}`, {
          method: 'DELETE',
        });

        if (response.ok) {
          this.loadContent(this.currentPage);
        } else {
          const data = await response.json();
          this.showError('删除失败: ' + data.error);
        }
      } catch (error) {
        this.showError('网络错误: ' + error);
      }
    }

    public createContent() {
      window.location.href = '/admin/content/create';
    }

    private showError(message: string) {
      alert(message); // 简单的错误显示，生产环境可以使用更好的通知组件
    }

    private formatDate(dateString: string): string {
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
      });
    }

    private getCollectionDisplayName(collection: string): string {
      const names: Record<string, string> = {
        news: '动态资讯',
        logs: '研究日志',
        research: '研究报告',
        reflections: '反思记录',
        economics: '经济研究',
        philosophy: '哲学研究',
        internet: '互联网研究',
        ai: 'AI研究',
        future: '未来研究',
        products: '产品发布',
      };
      return names[collection] || collection;
    }

    private getCollectionIcon(collection: string): string {
      const icons: Record<string, string> = {
        news: '📰',
        logs: '📔',
        research: '📊',
        reflections: '💭',
        economics: '💰',
        philosophy: '🤔',
        internet: '🌐',
        ai: '🤖',
        future: '🔮',
        products: '🛠️',
      };
      return icons[collection] || '📄';
    }
  }

  // 全局实例
  let contentList: ContentListManager;

  document.addEventListener('DOMContentLoaded', () => {
    contentList = new ContentListManager();
  });
</script>
