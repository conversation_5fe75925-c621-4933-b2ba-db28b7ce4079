---
/**
 * Enhanced Flex layout component for Pennfly Private Academy
 * Provides flexible box layout with comprehensive responsive options
 */

export interface Props {
  /** Flex direction */
  direction?: 'row' | 'row-reverse' | 'col' | 'col-reverse';
  /** Responsive flex direction for small screens (sm: 640px+) */
  smDirection?: 'row' | 'row-reverse' | 'col' | 'col-reverse';
  /** Responsive flex direction for medium screens (md: 768px+) */
  mdDirection?: 'row' | 'row-reverse' | 'col' | 'col-reverse';
  /** Responsive flex direction for large screens (lg: 1024px+) */
  lgDirection?: 'row' | 'row-reverse' | 'col' | 'col-reverse';
  /** Responsive flex direction for extra large screens (xl: 1280px+) */
  xlDirection?: 'row' | 'row-reverse' | 'col' | 'col-reverse';
  /** Responsive flex direction for 2xl screens (2xl: 1536px+) */
  '2xlDirection'?: 'row' | 'row-reverse' | 'col' | 'col-reverse';

  /** Flex wrap */
  wrap?: 'nowrap' | 'wrap' | 'wrap-reverse';
  /** Responsive flex wrap */
  smWrap?: 'nowrap' | 'wrap' | 'wrap-reverse';
  mdWrap?: 'nowrap' | 'wrap' | 'wrap-reverse';
  lgWrap?: 'nowrap' | 'wrap' | 'wrap-reverse';
  xlWrap?: 'nowrap' | 'wrap' | 'wrap-reverse';
  '2xlWrap'?: 'nowrap' | 'wrap' | 'wrap-reverse';

  /** Justify content (main axis alignment) */
  justify?: 'start' | 'end' | 'center' | 'between' | 'around' | 'evenly';
  /** Responsive justify content */
  smJustify?: 'start' | 'end' | 'center' | 'between' | 'around' | 'evenly';
  mdJustify?: 'start' | 'end' | 'center' | 'between' | 'around' | 'evenly';
  lgJustify?: 'start' | 'end' | 'center' | 'between' | 'around' | 'evenly';
  xlJustify?: 'start' | 'end' | 'center' | 'between' | 'around' | 'evenly';
  '2xlJustify'?: 'start' | 'end' | 'center' | 'between' | 'around' | 'evenly';

  /** Align items (cross axis alignment) */
  align?: 'start' | 'end' | 'center' | 'baseline' | 'stretch';
  /** Responsive align items */
  smAlign?: 'start' | 'end' | 'center' | 'baseline' | 'stretch';
  mdAlign?: 'start' | 'end' | 'center' | 'baseline' | 'stretch';
  lgAlign?: 'start' | 'end' | 'center' | 'baseline' | 'stretch';
  xlAlign?: 'start' | 'end' | 'center' | 'baseline' | 'stretch';
  '2xlAlign'?: 'start' | 'end' | 'center' | 'baseline' | 'stretch';

  /** Gap between items */
  gap?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  /** Horizontal gap */
  gapX?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  /** Vertical gap */
  gapY?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';

  /** Responsive gap */
  smGap?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  mdGap?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  lgGap?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  xlGap?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  '2xlGap'?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';

  /** Full width */
  fullWidth?: boolean;
  /** Full height */
  fullHeight?: boolean;

  /** Custom CSS class */
  class?: string;
  /** HTML tag type */
  as?: 'div' | 'section' | 'article' | 'main' | 'aside' | 'header' | 'footer' | 'nav' | 'ul' | 'ol';
}

const {
  direction = 'row',
  smDirection,
  mdDirection,
  lgDirection,
  xlDirection,
  '2xlDirection': xxlDirection,

  wrap = 'nowrap',
  smWrap,
  mdWrap,
  lgWrap,
  xlWrap,
  '2xlWrap': xxlWrap,

  justify = 'start',
  smJustify,
  mdJustify,
  lgJustify,
  xlJustify,
  '2xlJustify': xxlJustify,

  align = 'stretch',
  smAlign,
  mdAlign,
  lgAlign,
  xlAlign,
  '2xlAlign': xxlAlign,

  gap = 'none',
  gapX,
  gapY,
  smGap,
  mdGap,
  lgGap,
  xlGap,
  '2xlGap': xxlGap,

  fullWidth = false,
  fullHeight = false,

  class: className = '',
  as: Tag = 'div',
} = Astro.props;

// Direction class mappings
const directionClasses = {
  row: 'flex-row',
  'row-reverse': 'flex-row-reverse',
  col: 'flex-col',
  'col-reverse': 'flex-col-reverse',
};

// Wrap class mappings
const wrapClasses = {
  nowrap: 'flex-nowrap',
  wrap: 'flex-wrap',
  'wrap-reverse': 'flex-wrap-reverse',
};

// Justify content class mappings
const justifyClasses = {
  start: 'justify-start',
  end: 'justify-end',
  center: 'justify-center',
  between: 'justify-between',
  around: 'justify-around',
  evenly: 'justify-evenly',
};

// Align items class mappings
const alignClasses = {
  start: 'items-start',
  end: 'items-end',
  center: 'items-center',
  baseline: 'items-baseline',
  stretch: 'items-stretch',
};

// Gap class mappings
const gapClasses = {
  none: 'gap-0',
  xs: 'gap-1',
  sm: 'gap-2',
  md: 'gap-4',
  lg: 'gap-6',
  xl: 'gap-8',
  '2xl': 'gap-12',
};

const gapXClasses = {
  none: 'gap-x-0',
  xs: 'gap-x-1',
  sm: 'gap-x-2',
  md: 'gap-x-4',
  lg: 'gap-x-6',
  xl: 'gap-x-8',
  '2xl': 'gap-x-12',
};

const gapYClasses = {
  none: 'gap-y-0',
  xs: 'gap-y-1',
  sm: 'gap-y-2',
  md: 'gap-y-4',
  lg: 'gap-y-6',
  xl: 'gap-y-8',
  '2xl': 'gap-y-12',
};

// Build responsive direction classes
const responsiveDirectionClasses = [
  directionClasses[direction],
  smDirection ? `sm:${directionClasses[smDirection]}` : '',
  mdDirection ? `md:${directionClasses[mdDirection]}` : '',
  lgDirection ? `lg:${directionClasses[lgDirection]}` : '',
  xlDirection ? `xl:${directionClasses[xlDirection]}` : '',
  xxlDirection ? `2xl:${directionClasses[xxlDirection]}` : '',
].filter(Boolean);

// Build responsive wrap classes
const responsiveWrapClasses = [
  wrapClasses[wrap],
  smWrap ? `sm:${wrapClasses[smWrap]}` : '',
  mdWrap ? `md:${wrapClasses[mdWrap]}` : '',
  lgWrap ? `lg:${wrapClasses[lgWrap]}` : '',
  xlWrap ? `xl:${wrapClasses[xlWrap]}` : '',
  xxlWrap ? `2xl:${wrapClasses[xxlWrap]}` : '',
].filter(Boolean);

// Build responsive justify classes
const responsiveJustifyClasses = [
  justifyClasses[justify],
  smJustify ? `sm:${justifyClasses[smJustify]}` : '',
  mdJustify ? `md:${justifyClasses[mdJustify]}` : '',
  lgJustify ? `lg:${justifyClasses[lgJustify]}` : '',
  xlJustify ? `xl:${justifyClasses[xlJustify]}` : '',
  xxlJustify ? `2xl:${justifyClasses[xxlJustify]}` : '',
].filter(Boolean);

// Build responsive align classes
const responsiveAlignClasses = [
  alignClasses[align],
  smAlign ? `sm:${alignClasses[smAlign]}` : '',
  mdAlign ? `md:${alignClasses[mdAlign]}` : '',
  lgAlign ? `lg:${alignClasses[lgAlign]}` : '',
  xlAlign ? `xl:${alignClasses[xlAlign]}` : '',
  xxlAlign ? `2xl:${alignClasses[xxlAlign]}` : '',
].filter(Boolean);

// Build gap classes
const gapClassNames = [];
if (gapX || gapY) {
  // If gapX or gapY is specified, use them
  if (gapX) gapClassNames.push(gapXClasses[gapX]);
  if (gapY) gapClassNames.push(gapYClasses[gapY]);
} else {
  // Otherwise use unified gap
  gapClassNames.push(gapClasses[gap]);

  // Add responsive gaps
  if (smGap) gapClassNames.push(`sm:${gapClasses[smGap]}`);
  if (mdGap) gapClassNames.push(`md:${gapClasses[mdGap]}`);
  if (lgGap) gapClassNames.push(`lg:${gapClasses[lgGap]}`);
  if (xlGap) gapClassNames.push(`xl:${gapClasses[xlGap]}`);
  if (xxlGap) gapClassNames.push(`2xl:${gapClasses[xxlGap]}`);
}

// Combine all classes
const flexClasses = [
  'flex',
  ...responsiveDirectionClasses,
  ...responsiveWrapClasses,
  ...responsiveJustifyClasses,
  ...responsiveAlignClasses,
  ...gapClassNames,
  fullWidth ? 'w-full' : '',
  fullHeight ? 'h-full' : '',
  className,
]
  .filter(Boolean)
  .join(' ');
---

<Tag class={flexClasses}>
  <slot />
</Tag>

<style>
  /* Ensure flex container handles overflow properly */
  .flex {
    min-width: 0;
    min-height: 0;
  }

  /* Print styles */
  @media print {
    .flex {
      display: block !important;
    }

    .flex > * {
      margin-bottom: 0.5rem;
    }

    .flex > *:last-child {
      margin-bottom: 0;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .flex {
      outline: 1px solid transparent;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .flex {
      transition: none !important;
    }
  }
</style>
