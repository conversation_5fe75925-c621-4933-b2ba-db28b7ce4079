# 设计文档 - 多语言国际化系统

## 概述

本文档描述了 Pennfly Private Academy 多语言国际化系统的技术设计。系统基于 Astro 5.x 的 i18n 功能，实现中英文双语支持，包括路由本地化、内容翻译、界面本地化和 SEO 优化。

## 架构

### 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    用户界面层                                │
├─────────────────────────────────────────────────────────────┤
│  语言切换组件  │  本地化路由  │  翻译内容显示  │  SEO 标签    │
├─────────────────────────────────────────────────────────────┤
│                    业务逻辑层                                │
├─────────────────────────────────────────────────────────────┤
│  语言检测     │  路由处理    │  翻译加载     │  回退处理     │
├─────────────────────────────────────────────────────────────┤
│                    数据访问层                                │
├─────────────────────────────────────────────────────────────┤
│  翻译文件     │  内容文件    │  配置文件     │  缓存存储     │
└─────────────────────────────────────────────────────────────┘
```

### 技术栈

- **前端框架**: Astro 5.12.9
- **国际化**: Astro i18n
- **样式**: Tailwind CSS 4.1.11
- **类型检查**: TypeScript
- **翻译格式**: JSON
- **内容格式**: Markdown + Frontmatter

## 组件和接口

### 1. 语言切换组件 (LanguageSwitch.astro)

```typescript
interface LanguageSwitchProps {
  currentLang: 'zh' | 'en';
  currentPath: string;
  className?: string;
}
```

**功能**:
- 显示当前语言状态
- 提供语言切换选项
- 保持当前页面路径
- 支持键盘导航

### 2. 翻译工具函数

```typescript
interface TranslationKey {
  [key: string]: string | TranslationKey;
}

interface TranslationFunction {
  (key: string, params?: Record<string, string>): string;
}

// 翻译函数
function t(key: string, lang: 'zh' | 'en', params?: Record<string, string>): string;

// 获取本地化路径
function getLocalizedPath(path: string, lang: 'zh' | 'en'): string;

// 语言检测
function detectLanguage(request: Request): 'zh' | 'en';
```

### 3. 路由配置接口

```typescript
interface I18nConfig {
  defaultLocale: 'zh' | 'en';
  locales: ['zh', 'en'];
  routing: {
    prefixDefaultLocale: boolean;
    redirectToDefaultLocale: boolean;
  };
  fallback: {
    en: 'zh';
  };
}
```

### 4. 内容接口

```typescript
interface LocalizedContent {
  zh: {
    title: string;
    description: string;
    content: string;
    slug: string;
  };
  en: {
    title: string;
    description: string;
    content: string;
    slug: string;
  };
}

interface PageMeta {
  title: Record<'zh' | 'en', string>;
  description: Record<'zh' | 'en', string>;
  keywords: Record<'zh' | 'en', string[]>;
}
```

## 数据模型

### 1. 翻译文件结构

```json
// src/i18n/zh.json
{
  "nav": {
    "home": "首页",
    "research": "研究成果",
    "reflections": "个人思考",
    "collections": "数字资源",
    "about": "关于我"
  },
  "common": {
    "readMore": "阅读更多",
    "backToTop": "返回顶部",
    "loading": "加载中...",
    "error": "出错了"
  },
  "meta": {
    "siteTitle": "Pennfly Private Academy",
    "siteDescription": "专业的私人学院教育平台，分享研究成果、个人思考与数字资源"
  }
}
```

```json
// src/i18n/en.json
{
  "nav": {
    "home": "Home",
    "research": "Research",
    "reflections": "Reflections",
    "collections": "Collections",
    "about": "About"
  },
  "common": {
    "readMore": "Read More",
    "backToTop": "Back to Top",
    "loading": "Loading...",
    "error": "Something went wrong"
  },
  "meta": {
    "siteTitle": "Pennfly Private Academy",
    "siteDescription": "A professional private academy platform for sharing research, reflections, and digital collections"
  }
}
```

### 2. 内容文件结构

```markdown
<!-- src/content/research/ai-ethics.md -->
---
title:
  zh: "人工智能伦理研究"
  en: "AI Ethics Research"
description:
  zh: "探索人工智能技术发展中的伦理边界"
  en: "Exploring ethical boundaries in AI technology development"
publishDate: 2024-12-01
tags: ["AI", "Ethics", "Technology"]
lang: "zh"
translatedFrom: null
---

# 人工智能伦理研究

内容...
```

### 3. 页面路由结构

```
src/pages/
├── zh/
│   ├── index.astro              # 中文首页
│   ├── research/
│   │   ├── index.astro          # 研究列表页
│   │   └── [slug].astro         # 研究详情页
│   ├── reflections/
│   │   ├── index.astro          # 思考列表页
│   │   └── [slug].astro         # 思考详情页
│   ├── collections/
│   │   └── index.astro          # 资源页面
│   └── about.astro              # 关于页面
├── en/
│   ├── index.astro              # 英文首页
│   ├── research/
│   │   ├── index.astro          # Research list
│   │   └── [slug].astro         # Research detail
│   ├── reflections/
│   │   ├── index.astro          # Reflections list
│   │   └── [slug].astro         # Reflection detail
│   ├── collections/
│   │   └── index.astro          # Collections page
│   └── about.astro              # About page
└── index.astro                  # 根路径重定向
```

## 错误处理

### 1. 翻译缺失处理

```typescript
function getTranslation(key: string, lang: 'zh' | 'en'): string {
  const translation = translations[lang]?.[key];
  
  if (!translation) {
    // 回退到默认语言
    const fallback = translations['zh']?.[key];
    if (fallback) {
      console.warn(`Translation missing for key "${key}" in language "${lang}"`);
      return fallback;
    }
    
    // 返回 key 作为最后回退
    console.error(`Translation missing for key "${key}" in all languages`);
    return key;
  }
  
  return translation;
}
```

### 2. 路由错误处理

```typescript
// 404 页面本地化
export async function getStaticPaths() {
  return [
    { params: { lang: 'zh' } },
    { params: { lang: 'en' } }
  ];
}
```

### 3. 内容加载错误处理

```typescript
async function getLocalizedContent(slug: string, lang: 'zh' | 'en') {
  try {
    const content = await getEntry('research', `${slug}-${lang}`);
    return content;
  } catch (error) {
    // 尝试加载默认语言版本
    try {
      const fallbackContent = await getEntry('research', `${slug}-zh`);
      return { ...fallbackContent, isTranslated: false };
    } catch (fallbackError) {
      throw new Error(`Content not found: ${slug}`);
    }
  }
}
```

## 测试策略

### 1. 单元测试

- 翻译函数测试
- 语言检测函数测试
- 路径本地化函数测试
- 回退机制测试

### 2. 集成测试

- 语言切换流程测试
- 路由跳转测试
- 内容加载测试
- SEO 标签生成测试

### 3. 端到端测试

- 用户语言切换体验测试
- 多语言页面导航测试
- 搜索引擎爬取测试
- 性能基准测试

### 4. 可访问性测试

- 键盘导航测试
- 屏幕阅读器兼容性测试
- 语言标记正确性测试

## 性能优化

### 1. 翻译文件优化

- 按页面拆分翻译文件
- 实现翻译文件的懒加载
- 使用 Tree Shaking 移除未使用的翻译

### 2. 路由优化

- 预生成所有语言版本的静态页面
- 实现智能的语言重定向
- 优化语言检测逻辑

### 3. 缓存策略

- 浏览器端翻译缓存
- CDN 层面的语言版本缓存
- 构建时翻译预处理

### 4. 代码分割

```typescript
// 动态导入翻译文件
const loadTranslations = async (lang: 'zh' | 'en') => {
  const translations = await import(`../i18n/${lang}.json`);
  return translations.default;
};
```

## SEO 优化

### 1. Hreflang 标签

```html
<link rel="alternate" hreflang="zh" href="https://pennfly.com/zh/research" />
<link rel="alternate" hreflang="en" href="https://pennfly.com/en/research" />
<link rel="alternate" hreflang="x-default" href="https://pennfly.com/zh/research" />
```

### 2. 本地化 Meta 标签

```html
<html lang="zh">
<head>
  <title>研究成果 - Pennfly Private Academy</title>
  <meta name="description" content="探索深度学术思考与创新研究成果" />
  <meta property="og:locale" content="zh_CN" />
  <meta property="og:locale:alternate" content="en_US" />
</head>
```

### 3. 结构化数据

```json
{
  "@context": "https://schema.org",
  "@type": "WebSite",
  "name": "Pennfly Private Academy",
  "alternateName": "PPA",
  "url": "https://pennfly.com",
  "potentialAction": {
    "@type": "SearchAction",
    "target": "https://pennfly.com/search?q={search_term_string}",
    "query-input": "required name=search_term_string"
  },
  "inLanguage": ["zh", "en"]
}
```

## 部署配置

### 1. Astro 配置

```javascript
// astro.config.mjs
export default defineConfig({
  site: 'https://pennfly.com',
  i18n: {
    defaultLocale: 'zh',
    locales: ['zh', 'en'],
    routing: {
      prefixDefaultLocale: true,
      redirectToDefaultLocale: false
    },
    fallback: {
      en: 'zh'
    }
  },
  vite: {
    plugins: [tailwindcss()],
  },
});
```

### 2. 构建优化

```json
{
  "scripts": {
    "build": "astro build",
    "build:analyze": "astro build --analyze",
    "preview": "astro preview"
  }
}
```

### 3. 服务器配置

```nginx
# Nginx 配置示例
location / {
    # 语言检测和重定向
    if ($http_accept_language ~* "^en") {
        return 302 /en$request_uri;
    }
    return 302 /zh$request_uri;
}
```