---
import type { InstituteConfig } from '../../utils/instituteConfig';
import type { InstituteArticle } from '../../types/astro';

export interface Props {
  institute: InstituteConfig;
  articles: InstituteArticle[];
  showFilters?: boolean;
}

const { 
  institute, 
  articles, 
  showFilters = true
} = Astro.props;

// 获取所有标签用于筛选
const allTags = [...new Set(articles.flatMap(article => article.data.tags))];
---

<div class="article-list">
  <!-- 筛选和排序控件 -->
  {showFilters && (
    <div class="mb-8 rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
      <div class="mb-4 flex flex-wrap items-center justify-between gap-4">
        <h3 class="text-lg font-semibold text-gray-800">筛选和排序</h3>
        <div class="flex items-center gap-4">
          <!-- 排序选择 -->
          <div class="flex items-center gap-2">
            <label for="sort-select" class="text-sm text-gray-600">排序:</label>
            <select 
              id="sort-select" 
              class="rounded border border-gray-300 px-3 py-1 text-sm focus:border-blue-500 focus:outline-none"
            >
              <option value="date-desc">最新发布</option>
              <option value="date-asc">最早发布</option>
              <option value="title-asc">标题 A-Z</option>
              <option value="title-desc">标题 Z-A</option>
              <option value="featured-desc">精选优先</option>
              <option value="readingTime-asc">阅读时间短</option>
              <option value="readingTime-desc">阅读时间长</option>
            </select>
          </div>
          
          <!-- 视图切换 -->
          <div class="flex items-center gap-2">
            <button 
              id="list-view" 
              class="rounded px-3 py-1 text-sm transition-colors hover:bg-gray-100"
              data-view="list"
            >
              📋 列表
            </button>
            <button 
              id="grid-view" 
              class="rounded px-3 py-1 text-sm transition-colors hover:bg-gray-100"
              data-view="grid"
            >
              🔲 网格
            </button>
          </div>
        </div>
      </div>

      <!-- 标签筛选 -->
      {allTags.length > 0 && (
        <div class="mb-4">
          <label class="mb-2 block text-sm text-gray-600">标签筛选:</label>
          <div class="flex flex-wrap gap-2">
            <button 
              class="tag-filter active rounded bg-gray-200 px-3 py-1 text-sm transition-colors hover:bg-gray-300"
              data-tag=""
            >
              全部
            </button>
            {allTags.map(tag => (
              <button 
                class="tag-filter rounded bg-gray-100 px-3 py-1 text-sm transition-colors hover:bg-gray-200"
                data-tag={tag}
              >
                {tag}
              </button>
            ))}
          </div>
        </div>
      )}

      <!-- 其他筛选选项 -->
      <div class="flex flex-wrap gap-4">
        <label class="flex items-center gap-2">
          <input type="checkbox" id="featured-only" class="rounded">
          <span class="text-sm text-gray-600">仅显示精选</span>
        </label>
        
        <div class="flex items-center gap-2">
          <label for="date-from" class="text-sm text-gray-600">发布时间:</label>
          <input 
            type="date" 
            id="date-from" 
            class="rounded border border-gray-300 px-2 py-1 text-sm focus:border-blue-500 focus:outline-none"
          >
          <span class="text-sm text-gray-500">至</span>
          <input 
            type="date" 
            id="date-to" 
            class="rounded border border-gray-300 px-2 py-1 text-sm focus:border-blue-500 focus:outline-none"
          >
        </div>
      </div>
    </div>
  )}

  <!-- 文章列表 -->
  <div id="articles-container" class="space-y-6" data-view="list">
    {articles.map((article) => (
      <article 
        class="article-item rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition-shadow hover:shadow-md"
        data-tags={JSON.stringify(article.data.tags)}
        data-featured={article.data.featured}
        data-date={article.data.publishDate.toISOString()}
        data-reading-time={article.data.readingTime || 0}
        data-title={article.data.title.zh}
      >
        <div class="mb-4 flex items-start justify-between">
          <div class="flex-1">
            <h3 class="mb-2 text-xl font-semibold text-gray-800">
              <a
                href={`/${institute.id}/${article.slug}`}
                class={`transition-colors hover:${institute.color}`}
              >
                {article.data.title.zh}
              </a>
            </h3>
            <p class="mb-3 text-gray-600">{article.data.description.zh}</p>

            <div class="flex items-center space-x-4 text-sm text-gray-500">
              <span>{article.data.publishDate.toLocaleDateString('zh-CN')}</span>
              {article.data.readingTime && (
                <span>阅读时间 {article.data.readingTime} 分钟</span>
              )}
            </div>
          </div>

          {article.data.featured && (
            <div class="ml-4">
              <span class="rounded bg-yellow-100 px-2 py-1 text-xs font-medium text-yellow-800">
                精选
              </span>
            </div>
          )}
        </div>

        {article.data.tags.length > 0 && (
          <div class="flex flex-wrap gap-2">
            {article.data.tags.map((tag: string) => (
              <span class="rounded bg-gray-100 px-2 py-1 text-xs text-gray-700">
                {tag}
              </span>
            ))}
          </div>
        )}
      </article>
    ))}
  </div>

  <!-- 无结果提示 -->
  <div id="no-results" class="hidden py-12 text-center">
    <div class="mb-4 text-6xl">🔍</div>
    <h3 class="mb-2 text-xl font-semibold text-gray-800">没有找到匹配的文章</h3>
    <p class="text-gray-600">请尝试调整筛选条件</p>
  </div>
</div>

<script>
  // 文章筛选和排序功能
  class ArticleFilter {
    private articles: Element[];
    private container: HTMLElement | null;
    private noResults: HTMLElement | null;

    constructor() {
      this.articles = Array.from(document.querySelectorAll('.article-item'));
      this.container = document.getElementById('articles-container');
      this.noResults = document.getElementById('no-results');
      this.init();
    }

    init(): void {
      // 排序功能
      const sortSelect = document.getElementById('sort-select') as HTMLSelectElement;
      if (sortSelect) {
        sortSelect.addEventListener('change', (e) => {
          const target = e.target as HTMLSelectElement;
          if (target && target.value) {
            const parts = target.value.split('-');
            const field = parts[0];
            const order = parts[1];
            if (field && order) {
              this.sortArticles(field, order);
            }
          }
        });
      }

      // 标签筛选
      const tagFilters = document.querySelectorAll('.tag-filter');
      tagFilters.forEach(button => {
        button.addEventListener('click', (e) => {
          const target = e.target as HTMLElement;
          if (target) {
            // 移除其他按钮的active状态
            tagFilters.forEach(btn => {
              btn.classList.remove('active', 'bg-gray-200');
              btn.classList.add('bg-gray-100');
            });
            
            // 激活当前按钮
            target.classList.add('active', 'bg-gray-200');
            target.classList.remove('bg-gray-100');
            
            this.filterArticles();
          }
        });
      });

      // 精选筛选
      const featuredOnly = document.getElementById('featured-only') as HTMLInputElement;
      if (featuredOnly) {
        featuredOnly.addEventListener('change', () => this.filterArticles());
      }

      // 日期筛选
      const dateFrom = document.getElementById('date-from') as HTMLInputElement;
      const dateTo = document.getElementById('date-to') as HTMLInputElement;
      if (dateFrom) dateFrom.addEventListener('change', () => this.filterArticles());
      if (dateTo) dateTo.addEventListener('change', () => this.filterArticles());

      // 视图切换
      const listView = document.getElementById('list-view');
      const gridView = document.getElementById('grid-view');
      if (listView) {
        listView.addEventListener('click', () => this.switchView('list'));
      }
      if (gridView) {
        gridView.addEventListener('click', () => this.switchView('grid'));
      }
    }

    sortArticles(field: string, order: string): void {
      const sortedArticles = [...this.articles].sort((a, b) => {
        let aValue: any, bValue: any;
        const aElement = a as HTMLElement;
        const bElement = b as HTMLElement;
        
        switch (field) {
          case 'date':
            aValue = new Date(aElement.dataset.date || '');
            bValue = new Date(bElement.dataset.date || '');
            break;
          case 'title':
            aValue = aElement.dataset.title || '';
            bValue = bElement.dataset.title || '';
            break;
          case 'readingTime':
            aValue = parseInt(aElement.dataset.readingTime || '0', 10);
            bValue = parseInt(bElement.dataset.readingTime || '0', 10);
            break;
          case 'featured':
            aValue = aElement.dataset.featured === 'true' ? 1 : 0;
            bValue = bElement.dataset.featured === 'true' ? 1 : 0;
            break;
          default:
            return 0;
        }
        
        if (aValue < bValue) return order === 'asc' ? -1 : 1;
        if (aValue > bValue) return order === 'asc' ? 1 : -1;
        return 0;
      });

      // 重新排列DOM元素
      if (this.container) {
        sortedArticles.forEach(article => {
          this.container!.appendChild(article);
        });
      }
    }

    filterArticles(): void {
      const activeTagElement = document.querySelector('.tag-filter.active') as HTMLElement;
      const activeTag = activeTagElement?.dataset.tag || '';
      const featuredOnlyElement = document.getElementById('featured-only') as HTMLInputElement;
      const featuredOnly = featuredOnlyElement?.checked || false;
      const dateFromElement = document.getElementById('date-from') as HTMLInputElement;
      const dateToElement = document.getElementById('date-to') as HTMLInputElement;
      const dateFrom = dateFromElement?.value;
      const dateTo = dateToElement?.value;

      let visibleCount = 0;

      this.articles.forEach((article) => {
        const articleElement = article as HTMLElement;
        let visible = true;

        // 标签筛选
        if (activeTag) {
          const articleTags = JSON.parse(articleElement.dataset.tags || '[]');
          if (!articleTags.includes(activeTag)) {
            visible = false;
          }
        }

        // 精选筛选
        if (featuredOnly && articleElement.dataset.featured !== 'true') {
          visible = false;
        }

        // 日期筛选
        if (dateFrom || dateTo) {
          const articleDate = new Date(articleElement.dataset.date || '');
          if (dateFrom && articleDate < new Date(dateFrom)) {
            visible = false;
          }
          if (dateTo && articleDate > new Date(dateTo)) {
            visible = false;
          }
        }

        articleElement.style.display = visible ? 'block' : 'none';
        if (visible) visibleCount++;
      });

      // 显示/隐藏无结果提示
      if (this.noResults) {
        if (visibleCount === 0) {
          this.noResults.classList.remove('hidden');
        } else {
          this.noResults.classList.add('hidden');
        }
      }
    }

    switchView(view: string): void {
      const listBtn = document.getElementById('list-view');
      const gridBtn = document.getElementById('grid-view');
      
      if (this.container) {
        if (view === 'list') {
          this.container.className = 'space-y-6';
          this.container.dataset.view = 'list';
          listBtn?.classList.add('bg-gray-200');
          gridBtn?.classList.remove('bg-gray-200');
        } else {
          this.container.className = 'grid gap-6 md:grid-cols-2 lg:grid-cols-3';
          this.container.dataset.view = 'grid';
          gridBtn?.classList.add('bg-gray-200');
          listBtn?.classList.remove('bg-gray-200');
        }
      }
    }
  }

  // 初始化筛选器
  document.addEventListener('DOMContentLoaded', () => {
    new ArticleFilter();
  });
</script>

<style>
  .tag-filter.active {
    @apply bg-gray-200 font-medium;
  }
  
  [data-view="grid"] .article-item {
    @apply h-full;
  }
  
  [data-view="grid"] .article-item h3 {
    @apply text-lg;
  }
  
  [data-view="grid"] .article-item p {
    @apply text-sm;
  }
</style>