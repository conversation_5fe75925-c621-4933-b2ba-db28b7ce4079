export interface InstituteArticle {
  slug: string;
  data: any;
  render: () => Promise<any>;
}

export interface SortOptions {
  by: 'date' | 'title' | 'readingTime' | 'featured';
  order: 'asc' | 'desc';
}

export interface FilterOptions {
  featured?: boolean;
  tags?: string[];
  dateRange?: {
    start?: Date;
    end?: Date;
  };
}

export class InstituteManager {
  /**
   * 排序文章
   */
  static sortArticles(articles: InstituteArticle[], options: SortOptions): InstituteArticle[] {
    return [...articles].sort((a, b) => {
      let comparison = 0;

      switch (options.by) {
        case 'date':
          comparison = a.data.publishDate.getTime() - b.data.publishDate.getTime();
          break;
        case 'title':
          comparison = a.data.title.zh.localeCompare(b.data.title.zh, 'zh-CN');
          break;
        case 'readingTime':
          comparison = (a.data.readingTime || 0) - (b.data.readingTime || 0);
          break;
        case 'featured':
          comparison = (b.data.featured ? 1 : 0) - (a.data.featured ? 1 : 0);
          break;
      }

      return options.order === 'desc' ? -comparison : comparison;
    });
  }

  /**
   * 筛选文章
   */
  static filterArticles(articles: InstituteArticle[], options: FilterOptions): InstituteArticle[] {
    return articles.filter(article => {
      // 筛选精选文章
      if (options.featured !== undefined && article.data.featured !== options.featured) {
        return false;
      }

      // 筛选标签
      if (options.tags && options.tags.length > 0) {
        const hasMatchingTag = options.tags.some(tag => article.data.tags.includes(tag));
        if (!hasMatchingTag) {
          return false;
        }
      }

      // 筛选日期范围
      if (options.dateRange) {
        const articleDate = article.data.publishDate;
        if (options.dateRange.start && articleDate < options.dateRange.start) {
          return false;
        }
        if (options.dateRange.end && articleDate > options.dateRange.end) {
          return false;
        }
      }

      return true;
    });
  }

  /**
   * 获取相关文章
   */
  static getRelatedArticles(
    currentArticle: InstituteArticle,
    allArticles: InstituteArticle[],
    limit: number = 3
  ): InstituteArticle[] {
    const currentTags = currentArticle.data.tags || [];

    // 计算文章相关性得分
    const articlesWithScore = allArticles
      .filter(article => article.slug !== currentArticle.slug && !article.data.draft)
      .map(article => {
        let score = 0;
        const articleTags = article.data.tags || [];

        // 标签匹配得分
        const commonTags = currentTags.filter(tag => articleTags.includes(tag));
        score += commonTags.length * 2;

        // 精选文章加分
        if (article.data.featured) {
          score += 1;
        }

        // 时间相近性得分（越近得分越高）
        const timeDiff = Math.abs(
          currentArticle.data.publishDate.getTime() - article.data.publishDate.getTime()
        );
        const daysDiff = timeDiff / (1000 * 60 * 60 * 24);
        if (daysDiff < 30) score += 1;
        if (daysDiff < 7) score += 1;

        return { article, score };
      })
      .filter(item => item.score > 0)
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);

    return articlesWithScore.map(item => item.article);
  }

  /**
   * 获取热门标签
   */
  static getPopularTags(
    articles: InstituteArticle[],
    limit: number = 10
  ): Array<{ tag: string; count: number }> {
    const tagCounts = new Map<string, number>();

    articles.forEach(article => {
      if (!article.data.draft) {
        article.data.tags.forEach((tag: string) => {
          tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);
        });
      }
    });

    return Array.from(tagCounts.entries())
      .map(([tag, count]) => ({ tag, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);
  }

  /**
   * 获取文章统计信息
   */
  static getArticleStats(articles: InstituteArticle[]) {
    const publishedArticles = articles.filter(article => !article.data.draft);

    return {
      total: publishedArticles.length,
      featured: publishedArticles.filter(article => article.data.featured).length,
      totalTags: new Set(publishedArticles.flatMap(article => article.data.tags)).size,
      averageReadingTime:
        publishedArticles.reduce((sum, article) => sum + (article.data.readingTime || 0), 0) /
          publishedArticles.length || 0,
      latestDate:
        publishedArticles.length > 0
          ? new Date(
              Math.max(...publishedArticles.map(article => article.data.publishDate.getTime()))
            )
          : null,
      oldestDate:
        publishedArticles.length > 0
          ? new Date(
              Math.min(...publishedArticles.map(article => article.data.publishDate.getTime()))
            )
          : null,
    };
  }

  /**
   * 分页处理
   */
  static paginateArticles(articles: InstituteArticle[], page: number = 1, pageSize: number = 10) {
    const totalPages = Math.ceil(articles.length / pageSize);
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;

    return {
      articles: articles.slice(startIndex, endIndex),
      pagination: {
        currentPage: page,
        totalPages,
        totalItems: articles.length,
        hasNext: page < totalPages,
        hasPrev: page > 1,
        pageSize,
      },
    };
  }

  /**
   * 搜索文章
   */
  static searchArticles(articles: InstituteArticle[], query: string): InstituteArticle[] {
    if (!query.trim()) return articles;

    const searchTerm = query.toLowerCase().trim();

    return articles.filter(article => {
      // 搜索标题
      if (article.data.title.zh.toLowerCase().includes(searchTerm)) {
        return true;
      }

      // 搜索描述
      if (article.data.description.zh.toLowerCase().includes(searchTerm)) {
        return true;
      }

      // 搜索标签
      if (article.data.tags.some((tag: string) => tag.toLowerCase().includes(searchTerm))) {
        return true;
      }

      return false;
    });
  }
}
