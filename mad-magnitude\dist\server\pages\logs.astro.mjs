import { a as createComponent, h as renderComponent, r as renderScript, d as renderTemplate, m as maybeRenderHead, b as addAttribute } from "../assets/vendor-astro.kctgsZae.js";
import { i } from "../assets/vendor-astro.kctgsZae.js";
import "kleur/colors";
import { e as getCollection, i as groupByMonth, j as formatDate } from "../assets/utils.CcA_tyNa.js";
import { $ as $$Layout } from "../assets/Layout.BKd1ZXhO.js";
/* empty css                                */
const $$Index = createComponent(async ($$result, $$props, $$slots) => {
  const researchLogs = await getCollection("logs");
  const sortedLogs = researchLogs.filter((log) => !log.data.draft).sort((a, b) => b.data.date.getTime() - a.data.date.getTime());
  const logsByMood = {
    thoughtful: sortedLogs.filter((log) => log.data.mood === "thoughtful"),
    critical: sortedLogs.filter((log) => log.data.mood === "critical"),
    optimistic: sortedLogs.filter((log) => log.data.mood === "optimistic"),
    analytical: sortedLogs.filter((log) => log.data.mood === "analytical")
  };
  const logsByInstitute = {
    economics: sortedLogs.filter((log) => log.data.relatedInstitute?.includes("economics")),
    philosophy: sortedLogs.filter((log) => log.data.relatedInstitute?.includes("philosophy")),
    internet: sortedLogs.filter((log) => log.data.relatedInstitute?.includes("internet")),
    ai: sortedLogs.filter((log) => log.data.relatedInstitute?.includes("ai")),
    future: sortedLogs.filter((log) => log.data.relatedInstitute?.includes("future"))
  };
  const logsByMonth = groupByMonth(sortedLogs);
  const stats = {
    total: sortedLogs.length,
    thisMonth: sortedLogs.filter((log) => {
      const now = /* @__PURE__ */ new Date();
      const logDate = new Date(log.data.date);
      return logDate.getMonth() === now.getMonth() && logDate.getFullYear() === now.getFullYear();
    }).length,
    byMood: {
      thoughtful: logsByMood.thoughtful.length,
      critical: logsByMood.critical.length,
      optimistic: logsByMood.optimistic.length,
      analytical: logsByMood.analytical.length
    },
    byInstitute: {
      economics: logsByInstitute.economics.length,
      philosophy: logsByInstitute.philosophy.length,
      internet: logsByInstitute.internet.length,
      ai: logsByInstitute.ai.length,
      future: logsByInstitute.future.length
    }
  };
  const allTags = /* @__PURE__ */ new Set();
  sortedLogs.forEach((log) => {
    log.data.tags?.forEach((tag) => allTags.add(tag));
  });
  const popularTags = Array.from(allTags).slice(0, 10);
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "研究日志 - Pennfly Private Academy", "description": "记录日常的研究思考、学习心得和灵感闪现，展示知识创造的真实过程", "data-astro-cid-kykr7tuj": true }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<main class="min-h-screen bg-gray-50" data-astro-cid-kykr7tuj> <!-- 页面头部 --> <div class="bg-gradient-to-r from-amber-500 to-orange-600 py-16 text-white" data-astro-cid-kykr7tuj> <div class="container mx-auto px-6" data-astro-cid-kykr7tuj> <div class="mb-6 flex items-center" data-astro-cid-kykr7tuj> <span class="mr-4 text-5xl" data-astro-cid-kykr7tuj>📔</span> <div data-astro-cid-kykr7tuj> <h1 class="mb-2 text-4xl font-bold" data-astro-cid-kykr7tuj>研究日志</h1> <p class="text-xl opacity-90" data-astro-cid-kykr7tuj>Research Journal</p> </div> </div> <p class="max-w-3xl text-lg opacity-90" data-astro-cid-kykr7tuj>
记录日常的研究思考、学习心得和灵感闪现。
          这里是思想的实验室，记录着知识创造和思考演进的真实轨迹。
</p> </div> </div> <!-- 日志列表 --> <div class="container mx-auto px-6 py-12" data-astro-cid-kykr7tuj> <!-- 统计和筛选 --> <div class="mb-8" data-astro-cid-kykr7tuj> <div class="mb-6 flex flex-wrap items-center justify-between gap-4" data-astro-cid-kykr7tuj> <h2 class="text-2xl font-bold text-gray-800" data-astro-cid-kykr7tuj>研究记录</h2> <div class="flex items-center space-x-4" data-astro-cid-kykr7tuj> <!-- 视图切换 --> <div class="flex rounded-lg border border-gray-300 bg-white" data-astro-cid-kykr7tuj> <button class="view-toggle active rounded-l-lg px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50" data-view="list" data-astro-cid-kykr7tuj>
📋 列表视图
</button> <button class="view-toggle rounded-r-lg border-l border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50" data-view="timeline" data-astro-cid-kykr7tuj>
📅 时间线
</button> </div> </div> </div> <!-- 筛选器 --> <div class="mb-6 space-y-4" data-astro-cid-kykr7tuj> <!-- 心情筛选 --> <div data-astro-cid-kykr7tuj> <h3 class="mb-2 text-sm font-medium text-gray-700" data-astro-cid-kykr7tuj>按心情筛选</h3> <div class="flex flex-wrap gap-2" data-astro-cid-kykr7tuj> <button class="mood-filter active" data-mood="all" data-astro-cid-kykr7tuj>
全部 (${stats.total})
</button> <button class="mood-filter" data-mood="thoughtful" data-astro-cid-kykr7tuj>
🤔 深思 (${stats.byMood.thoughtful})
</button> <button class="mood-filter" data-mood="optimistic" data-astro-cid-kykr7tuj>
😊 乐观 (${stats.byMood.optimistic})
</button> <button class="mood-filter" data-mood="analytical" data-astro-cid-kykr7tuj>
🔍 分析 (${stats.byMood.analytical})
</button> <button class="mood-filter" data-mood="critical" data-astro-cid-kykr7tuj>
🧐 批判 (${stats.byMood.critical})
</button> </div> </div> <!-- 热门标签 --> ${popularTags.length > 0 && renderTemplate`<div data-astro-cid-kykr7tuj> <h3 class="mb-2 text-sm font-medium text-gray-700" data-astro-cid-kykr7tuj>热门标签</h3> <div class="flex flex-wrap gap-2" data-astro-cid-kykr7tuj> ${popularTags.map((tag) => renderTemplate`<button class="tag-filter rounded-full bg-gray-100 px-3 py-1 text-xs text-gray-700 transition-colors hover:bg-gray-200" data-astro-cid-kykr7tuj>
#${tag} </button>`)} </div> </div>`} </div> <!-- 统计信息 --> <div class="mb-8 grid grid-cols-2 gap-4 md:grid-cols-4" data-astro-cid-kykr7tuj> <div class="rounded-lg bg-amber-50 p-4 text-center" data-astro-cid-kykr7tuj> <div class="text-2xl font-bold text-amber-600" data-astro-cid-kykr7tuj>${stats.total}</div> <div class="text-sm text-amber-800" data-astro-cid-kykr7tuj>总日志数</div> </div> <div class="rounded-lg bg-green-50 p-4 text-center" data-astro-cid-kykr7tuj> <div class="text-2xl font-bold text-green-600" data-astro-cid-kykr7tuj>${stats.thisMonth}</div> <div class="text-sm text-green-800" data-astro-cid-kykr7tuj>本月记录</div> </div> <div class="rounded-lg bg-blue-50 p-4 text-center" data-astro-cid-kykr7tuj> <div class="text-2xl font-bold text-blue-600" data-astro-cid-kykr7tuj> ${stats.byInstitute.ai + stats.byInstitute.economics} </div> <div class="text-sm text-blue-800" data-astro-cid-kykr7tuj>热门研究所</div> </div> <div class="rounded-lg bg-purple-50 p-4 text-center" data-astro-cid-kykr7tuj> <div class="text-2xl font-bold text-purple-600" data-astro-cid-kykr7tuj>${popularTags.length}</div> <div class="text-sm text-purple-800" data-astro-cid-kykr7tuj>标签数量</div> </div> </div> </div> <!-- 列表视图 --> <div id="list-view" class="view-content mb-12" data-astro-cid-kykr7tuj> ${sortedLogs.length > 0 ? renderTemplate`<div class="space-y-6" data-astro-cid-kykr7tuj> ${sortedLogs.map((log) => renderTemplate`<article class="log-item rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition-all hover:-translate-y-1 hover:shadow-md"${addAttribute(log.data.mood || "none", "data-mood")} data-astro-cid-kykr7tuj> <div class="mb-4 flex items-start justify-between" data-astro-cid-kykr7tuj> <div class="flex-1" data-astro-cid-kykr7tuj> <div class="mb-3 flex items-center space-x-3" data-astro-cid-kykr7tuj> <span class="font-mono text-lg text-gray-500" data-astro-cid-kykr7tuj>
📅 ${formatDate(log.data.date)} </span> ${log.data.mood && renderTemplate`<span${addAttribute(`rounded px-2 py-1 text-xs font-medium ${log.data.mood === "thoughtful" ? "bg-blue-100 text-blue-800" : log.data.mood === "critical" ? "bg-red-100 text-red-800" : log.data.mood === "optimistic" ? "bg-green-100 text-green-800" : "bg-purple-100 text-purple-800"}`, "class")} data-astro-cid-kykr7tuj> ${log.data.mood === "thoughtful" ? "🤔 深思" : log.data.mood === "critical" ? "🧐 批判" : log.data.mood === "optimistic" ? "😊 乐观" : "🔍 分析"} </span>`} </div> <h3 class="mb-3 text-xl font-semibold text-gray-800" data-astro-cid-kykr7tuj> <a${addAttribute(`/logs/${log.slug}`, "href")} class="transition-colors hover:text-amber-600" data-astro-cid-kykr7tuj> ${log.data.title} </a> </h3>  ${log.data.relatedInstitute && log.data.relatedInstitute.length > 0 && renderTemplate`<div class="mb-3" data-astro-cid-kykr7tuj> <div class="mb-2 text-xs text-gray-500" data-astro-cid-kykr7tuj>相关研究所:</div> <div class="flex flex-wrap gap-2" data-astro-cid-kykr7tuj> ${log.data.relatedInstitute.map((institute) => renderTemplate`<a${addAttribute(`/${institute}`, "href")} class="inline-flex items-center space-x-1 rounded bg-green-100 px-2 py-1 text-xs text-green-700 transition-colors hover:bg-green-200" data-astro-cid-kykr7tuj> <span data-astro-cid-kykr7tuj> ${institute === "economics" ? "💰" : institute === "philosophy" ? "🤔" : institute === "internet" ? "🌐" : institute === "ai" ? "🤖" : "🔮"} </span> <span data-astro-cid-kykr7tuj> ${institute === "economics" ? "经济研究所" : institute === "philosophy" ? "哲学研究所" : institute === "internet" ? "互联网研究所" : institute === "ai" ? "AI研究所" : "未来研究所"} </span> </a>`)} </div> </div>`} </div> </div> ${log.data.tags && log.data.tags.length > 0 && renderTemplate`<div class="flex flex-wrap gap-2" data-astro-cid-kykr7tuj> ${log.data.tags.map((tag) => renderTemplate`<span class="cursor-pointer rounded-full bg-gray-100 px-3 py-1 text-xs text-gray-700 transition-colors hover:bg-gray-200" data-astro-cid-kykr7tuj>
#${tag} </span>`)} </div>`} </article>`)} </div>` : renderTemplate`<div class="py-12 text-center" data-astro-cid-kykr7tuj> <div class="mb-4 text-6xl" data-astro-cid-kykr7tuj>📔</div> <h3 class="mb-2 text-xl font-semibold text-gray-800" data-astro-cid-kykr7tuj>暂无研究日志</h3> <p class="mb-6 text-gray-600" data-astro-cid-kykr7tuj>开始记录您的研究思考和学习心得吧</p> <a href="/admin" class="inline-block rounded-lg bg-amber-600 px-6 py-2 text-white transition-colors hover:bg-amber-700" data-astro-cid-kykr7tuj>
写日志
</a> </div>`} </div> <!-- 时间线视图 --> <div id="timeline-view" class="view-content mb-12 hidden" data-astro-cid-kykr7tuj> ${Object.entries(logsByMonth).map(([month, logs]) => renderTemplate`<div class="mb-8" data-astro-cid-kykr7tuj> <div class="mb-4 flex items-center" data-astro-cid-kykr7tuj> <div class="mr-4 rounded-lg bg-amber-100 px-3 py-1 text-sm font-medium text-amber-800" data-astro-cid-kykr7tuj> ${month} </div> <div class="h-px flex-1 bg-gray-200" data-astro-cid-kykr7tuj></div> </div> <div class="space-y-4" data-astro-cid-kykr7tuj> ${logs.map((log) => renderTemplate`<div class="log-item flex items-start space-x-4"${addAttribute(log.data.mood || "none", "data-mood")} data-astro-cid-kykr7tuj> <div class="flex-shrink-0" data-astro-cid-kykr7tuj> <div class="flex h-10 w-10 items-center justify-center rounded-full border-2 border-amber-200 bg-white" data-astro-cid-kykr7tuj> <span class="text-sm" data-astro-cid-kykr7tuj>📔</span> </div> </div> <div class="flex-1 rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-shadow hover:shadow-md" data-astro-cid-kykr7tuj> <div class="mb-2 flex items-center justify-between" data-astro-cid-kykr7tuj> <h4 class="font-semibold text-gray-800" data-astro-cid-kykr7tuj> <a${addAttribute(`/logs/${log.slug}`, "href")} class="transition-colors hover:text-amber-600" data-astro-cid-kykr7tuj> ${log.data.title} </a> </h4> <span class="text-xs text-gray-500" data-astro-cid-kykr7tuj>${formatDate(log.data.date)}</span> </div> ${log.data.mood && renderTemplate`<div class="mb-2" data-astro-cid-kykr7tuj> <span${addAttribute(`inline-block rounded px-2 py-1 text-xs font-medium ${log.data.mood === "thoughtful" ? "bg-blue-100 text-blue-800" : log.data.mood === "critical" ? "bg-red-100 text-red-800" : log.data.mood === "optimistic" ? "bg-green-100 text-green-800" : "bg-purple-100 text-purple-800"}`, "class")} data-astro-cid-kykr7tuj> ${log.data.mood === "thoughtful" ? "🤔 深思" : log.data.mood === "critical" ? "🧐 批判" : log.data.mood === "optimistic" ? "😊 乐观" : "🔍 分析"} </span> </div>`} ${log.data.tags && log.data.tags.length > 0 && renderTemplate`<div class="flex flex-wrap gap-1" data-astro-cid-kykr7tuj> ${log.data.tags.slice(0, 3).map((tag) => renderTemplate`<span class="rounded bg-gray-100 px-2 py-0.5 text-xs text-gray-600" data-astro-cid-kykr7tuj>
#${tag} </span>`)} </div>`} </div> </div>`)} </div> </div>`)} </div> <!-- 返回首页 --> <div class="text-center" data-astro-cid-kykr7tuj> <a href="/" class="inline-block rounded-lg bg-gray-600 px-6 py-2 text-white transition-colors hover:bg-gray-700" data-astro-cid-kykr7tuj>
← 返回研究院首页
</a> </div> </div> </main> ` })}  ${renderScript($$result, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/logs/index.astro?astro&type=script&index=0&lang.ts")}`;
}, "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/logs/index.astro", void 0);
const $$file = "D:/Projects/PennflyPrivateAcademy/mad-magnitude/src/pages/logs/index.astro";
const $$url = "/logs";
const _page = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({ __proto__: null, default: $$Index, file: $$file, url: $$url }, Symbol.toStringTag, { value: "Module" }));
const page = () => _page;
export {
  page,
  i as renderers
};
