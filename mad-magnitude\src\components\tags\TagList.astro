---
/**
 * 标签列表组件
 * 用于展示内容的标签，支持多种显示模式
 */
export interface Props {
  tags: string[];
  variant?: 'default' | 'compact' | 'badge' | 'minimal';
  showCount?: boolean;
  maxTags?: number;
  linkable?: boolean;
  size?: 'small' | 'medium' | 'large';
}

const {
  tags = [],
  variant = 'default',
  showCount = false,
  maxTags,
  linkable = true,
  size = 'medium',
} = Astro.props;

// 限制显示的标签数量
const displayTags = maxTags ? tags.slice(0, maxTags) : tags;
const hasMoreTags = maxTags && tags.length > maxTags;
const remainingCount = hasMoreTags ? tags.length - maxTags : 0;

// 获取标签颜色（简化版本）
function getTagColor(tag: string): string {
  const colors = [
    '#3b82f6',
    '#10b981',
    '#8b5cf6',
    '#f59e0b',
    '#ef4444',
    '#6b7280',
    '#64748b',
    '#06b6d4',
  ];
  // 基于标签名称生成一致的颜色
  let hash = 0;
  for (let i = 0; i < tag.length; i++) {
    hash = tag.charCodeAt(i) + ((hash << 5) - hash);
  }
  return colors[Math.abs(hash) % colors.length];
}
---

<div class={`tag-list tag-list--${variant} tag-list--${size}`}>
  {
    displayTags.map(tag =>
      linkable ? (
        <a
          href={`/tags/${encodeURIComponent(tag)}`}
          class="tag-item tag-link"
          style={`--tag-color: ${getTagColor(tag)}`}
          title={`查看标签"${tag}"的所有内容`}
        >
          <span class="tag-text">{tag}</span>
          {showCount && <span class="tag-count">(0)</span>}
        </a>
      ) : (
        <span class="tag-item tag-static" style={`--tag-color: ${getTagColor(tag)}`}>
          <span class="tag-text">{tag}</span>
          {showCount && <span class="tag-count">(0)</span>}
        </span>
      )
    )
  }
  {
    hasMoreTags && (
      <span class="tag-item tag-more" title={`还有 ${remainingCount} 个标签`}>
        +{remainingCount}
      </span>
    )
  }
</div>

<style>
  .tag-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    align-items: center;
  }

  .tag-item {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
    white-space: nowrap;
  }

  .tag-text {
    line-height: 1;
  }

  .tag-count {
    font-size: 0.8em;
    opacity: 0.7;
    font-weight: 400;
  }

  /* 默认样式 */
  .tag-list--default .tag-item {
    padding: 0.375rem 0.75rem;
    background: rgba(var(--tag-color-rgb, 59, 130, 246), 0.1);
    color: var(--tag-color, #3b82f6);
    border: 1px solid rgba(var(--tag-color-rgb, 59, 130, 246), 0.2);
  }

  .tag-list--default .tag-link:hover {
    background: rgba(var(--tag-color-rgb, 59, 130, 246), 0.15);
    border-color: var(--tag-color, #3b82f6);
    transform: translateY(-1px);
  }

  /* 紧凑样式 */
  .tag-list--compact .tag-item {
    padding: 0.25rem 0.5rem;
    background: #f1f5f9;
    color: #475569;
    border: 1px solid #e2e8f0;
    font-size: 0.875rem;
  }

  .tag-list--compact .tag-link:hover {
    background: #e2e8f0;
    color: #334155;
  }

  /* 徽章样式 */
  .tag-list--badge .tag-item {
    padding: 0.25rem 0.625rem;
    background: var(--tag-color, #3b82f6);
    color: white;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 600;
  }

  .tag-list--badge .tag-link:hover {
    opacity: 0.9;
    transform: scale(1.05);
  }

  /* 最小样式 */
  .tag-list--minimal .tag-item {
    padding: 0.125rem 0.375rem;
    color: var(--tag-color, #3b82f6);
    font-size: 0.75rem;
    text-decoration: underline;
    text-decoration-color: transparent;
    background: none;
    border: none;
  }

  .tag-list--minimal .tag-link:hover {
    text-decoration-color: currentColor;
  }

  /* 尺寸变体 */
  .tag-list--small .tag-item {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }

  .tag-list--large .tag-item {
    font-size: 1rem;
    padding: 0.5rem 1rem;
  }

  /* 更多标签指示器 */
  .tag-more {
    background: #f8fafc !important;
    color: #64748b !important;
    border: 1px dashed #cbd5e1 !important;
    cursor: help;
  }

  /* 链接样式 */
  .tag-link {
    text-decoration: none;
    cursor: pointer;
  }

  .tag-static {
    cursor: default;
  }

  /* 响应式设计 */
  @media (max-width: 640px) {
    .tag-list {
      gap: 0.375rem;
    }
    .tag-item {
      font-size: 0.875rem;
    }
    .tag-list--large .tag-item {
      font-size: 0.875rem;
      padding: 0.375rem 0.75rem;
    }
  }

  /* 无障碍支持 */
  .tag-link:focus {
    outline: 2px solid var(--tag-color, #3b82f6);
    outline-offset: 2px;
  }

  /* 打印样式 */
  @media print {
    .tag-list {
      gap: 0.25rem;
    }
    .tag-item {
      background: none !important;
      color: #000 !important;
      border: 1px solid #000 !important;
      font-size: 0.75rem !important;
      padding: 0.125rem 0.25rem !important;
    }
  }
</style>

<script>
  // 为标签添加颜色 CSS 变量
  document.addEventListener('DOMContentLoaded', () => {
    const tagItems = document.querySelectorAll('.tag-item[style*="--tag-color"]');
    tagItems.forEach(item => {
      const htmlItem = item as HTMLElement;
      const colorValue = htmlItem.style.getPropertyValue('--tag-color');
      if (colorValue) {
        // 将十六进制颜色转换为 RGB
        const hex = colorValue.replace('#', '');
        const r = parseInt(hex.substr(0, 2), 16);
        const g = parseInt(hex.substr(2, 2), 16);
        const b = parseInt(hex.substr(4, 2), 16);
        htmlItem.style.setProperty('--tag-color-rgb', `${r}, ${g}, ${b}`);
      }
    });
  });
</script>
