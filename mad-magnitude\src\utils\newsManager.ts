/**
 * 动态资讯管理工具
 */

import { getCollection } from 'astro:content';

// 动态类型配置
export const NEWS_TYPE_CONFIG = {
  research: {
    label: '研究动态',
    icon: '🔬',
    color: 'blue',
    description: '最新的研究进展和发现',
  },
  announcement: {
    label: '重要公告',
    icon: '📢',
    color: 'purple',
    description: '重要通知和公告信息',
  },
  reflection: {
    label: '个人思考',
    icon: '💭',
    color: 'orange',
    description: '个人的思考和感悟',
  },
  milestone: {
    label: '里程碑',
    icon: '🎯',
    color: 'green',
    description: '重要的里程碑事件',
  },
} as const;

// 研究所配置
export const INSTITUTE_CONFIG = {
  economics: {
    label: '经济研究所',
    icon: '💰',
    color: 'yellow',
    path: '/economics',
  },
  philosophy: {
    label: '哲学研究所',
    icon: '🤔',
    color: 'indigo',
    path: '/philosophy',
  },
  internet: {
    label: '互联网研究所',
    icon: '🌐',
    color: 'cyan',
    path: '/internet',
  },
  ai: {
    label: 'AI研究所',
    icon: '🤖',
    color: 'purple',
    path: '/ai',
  },
  future: {
    label: '未来研究所',
    icon: '🔮',
    color: 'pink',
    path: '/future',
  },
} as const;

/**
 * 获取所有动态资讯
 */
export async function getAllNews() {
  const newsCollection = await getCollection('news');
  return newsCollection
    .filter(news => !news.data.draft)
    .sort(
      (a, b) => new Date(b.data.publishDate).getTime() - new Date(a.data.publishDate).getTime()
    );
}

/**
 * 按类型获取动态资讯
 */
export async function getNewsByType(type: keyof typeof NEWS_TYPE_CONFIG) {
  const allNews = await getAllNews();
  return allNews.filter(news => news.data.type === type);
}

/**
 * 按研究所获取动态资讯
 */
export async function getNewsByInstitute(institute: keyof typeof INSTITUTE_CONFIG) {
  const allNews = await getAllNews();
  return allNews.filter(
    news => news.data.relatedInstitute && news.data.relatedInstitute.includes(institute)
  );
}

/**
 * 获取置顶动态
 */
export async function getFeaturedNews() {
  const allNews = await getAllNews();
  return allNews.filter(news => news.data.featured);
}

/**
 * 获取最新动态
 */
export async function getRecentNews(limit: number = 5) {
  const allNews = await getAllNews();
  return allNews.slice(0, limit);
}

/**
 * 按标签获取动态资讯
 */
export async function getNewsByTag(tag: string) {
  const allNews = await getAllNews();
  return allNews.filter(news => news.data.tags && news.data.tags.includes(tag));
}

/**
 * 搜索动态资讯
 */
export async function searchNews(query: string) {
  const allNews = await getAllNews();
  const searchTerm = query.toLowerCase();

  return allNews.filter(news => {
    const title = news.data.title.zh.toLowerCase();
    const description = news.data.description.zh.toLowerCase();
    const summary = news.data.summary?.toLowerCase() || '';
    const tags = news.data.tags?.join(' ').toLowerCase() || '';

    return (
      title.includes(searchTerm) ||
      description.includes(searchTerm) ||
      summary.includes(searchTerm) ||
      tags.includes(searchTerm)
    );
  });
}

/**
 * 获取相关动态
 */
export async function getRelatedNews(currentSlug: string, limit: number = 3) {
  const allNews = await getAllNews();
  const currentNews = allNews.find(news => news.slug === currentSlug);

  if (!currentNews) {
    return [];
  }

  // 计算相关性得分
  const relatedNews = allNews
    .filter(news => news.slug !== currentSlug)
    .map(news => {
      let score = 0;

      // 同类型加分
      if (news.data.type === currentNews.data.type) {
        score += 3;
      }

      // 相同研究所加分
      if (currentNews.data.relatedInstitute && news.data.relatedInstitute) {
        const commonInstitutes = currentNews.data.relatedInstitute.filter(inst =>
          news.data.relatedInstitute?.includes(inst)
        );
        score += commonInstitutes.length * 2;
      }

      // 相同标签加分
      if (currentNews.data.tags && news.data.tags) {
        const commonTags = currentNews.data.tags.filter(tag => news.data.tags?.includes(tag));
        score += commonTags.length;
      }

      // 时间接近加分
      const timeDiff = Math.abs(
        new Date(news.data.publishDate).getTime() - new Date(currentNews.data.publishDate).getTime()
      );
      const daysDiff = timeDiff / (1000 * 60 * 60 * 24);
      if (daysDiff < 30) {
        score += 1;
      }

      return { news, score };
    })
    .filter(item => item.score > 0)
    .sort((a, b) => b.score - a.score)
    .slice(0, limit)
    .map(item => item.news);

  return relatedNews;
}

/**
 * 获取动态统计信息
 */
export async function getNewsStats() {
  const allNews = await getAllNews();

  const stats = {
    total: allNews.length,
    byType: {} as Record<string, number>,
    byInstitute: {} as Record<string, number>,
    featured: 0,
    thisMonth: 0,
    thisYear: 0,
  };

  const now = new Date();
  const thisMonth = now.getMonth();
  const thisYear = now.getFullYear();

  allNews.forEach(news => {
    // 按类型统计
    stats.byType[news.data.type] = (stats.byType[news.data.type] || 0) + 1;

    // 按研究所统计
    if (news.data.relatedInstitute) {
      news.data.relatedInstitute.forEach(institute => {
        stats.byInstitute[institute] = (stats.byInstitute[institute] || 0) + 1;
      });
    }

    // 置顶统计
    if (news.data.featured) {
      stats.featured++;
    }

    // 时间统计
    const publishDate = new Date(news.data.publishDate);
    if (publishDate.getFullYear() === thisYear) {
      stats.thisYear++;
      if (publishDate.getMonth() === thisMonth) {
        stats.thisMonth++;
      }
    }
  });

  return stats;
}

/**
 * 获取热门标签
 */
export async function getPopularTags(limit: number = 10) {
  const allNews = await getAllNews();
  const tagCounts: Record<string, number> = {};

  allNews.forEach(news => {
    if (news.data.tags) {
      news.data.tags.forEach(tag => {
        tagCounts[tag] = (tagCounts[tag] || 0) + 1;
      });
    }
  });

  return Object.entries(tagCounts)
    .sort(([, a], [, b]) => b - a)
    .slice(0, limit)
    .map(([tag, count]) => ({ tag, count }));
}

/**
 * 按月份分组动态
 */
export async function getNewsByMonth() {
  const allNews = await getAllNews();
  const groupedNews: Record<string, typeof allNews> = {};

  allNews.forEach(news => {
    const date = new Date(news.data.publishDate);
    const monthKey = `${date.getFullYear()}年${date.getMonth() + 1}月`;

    if (!groupedNews[monthKey]) {
      groupedNews[monthKey] = [];
    }
    groupedNews[monthKey].push(news);
  });

  return groupedNews;
}

/**
 * 验证动态数据
 */
export function validateNewsData(data: any) {
  const errors: string[] = [];

  if (!data.title?.zh) {
    errors.push('缺少中文标题');
  }

  if (!data.description?.zh) {
    errors.push('缺少中文描述');
  }

  if (!data.publishDate) {
    errors.push('缺少发布日期');
  }

  if (!data.type || !NEWS_TYPE_CONFIG[data.type as keyof typeof NEWS_TYPE_CONFIG]) {
    errors.push('无效的动态类型');
  }

  if (data.relatedInstitute) {
    const invalidInstitutes = data.relatedInstitute.filter(
      inst => !INSTITUTE_CONFIG[inst as keyof typeof INSTITUTE_CONFIG]
    );
    if (invalidInstitutes.length > 0) {
      errors.push(`无效的研究所: ${invalidInstitutes.join(', ')}`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}
