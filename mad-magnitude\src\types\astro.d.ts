/**
 * Astro 相关类型声明
 */

declare module 'astro:content' {
  export function getCollection(collection: string, filter?: any): Promise<any[]>;
  export function defineCollection(config: any): any;
  export const z: any;
}

// 扩展HTMLElement以支持dataset
declare global {
  interface Element {
    dataset: DOMStringMap;
  }

  interface HTMLElement {
    dataset: DOMStringMap;
  }
}

// Astro组件类型
declare module '*.astro' {
  const Component: any;
  export default Component;
}

// 内容集合类型
export interface ContentEntry {
  slug: string;
  data: {
    [key: string]: any;
  };
  render(): Promise<{ Content: any }>;
}

// 常用的内容数据类型
export interface BaseContentData {
  title?: string | { zh: string; en?: string };
  description?: string | { zh: string; en?: string };
  publishDate?: Date;
  date?: Date;
  draft?: boolean;
  featured?: boolean;
  tags?: string[];
  author?: string;
  summary?: string;
  type?: string;
  mood?: string;
  relatedInstitute?: string[];
  relatedContent?: string[];
}
