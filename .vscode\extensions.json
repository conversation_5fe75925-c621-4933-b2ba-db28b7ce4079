{"recommendations": ["astro-build.astro-vscode", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "dbaeumer.vscode-eslint", "ms-vscode.vscode-typescript-next", "streetsidesoftware.code-spell-checker", "ms-vscode.vscode-json", "redhat.vscode-yaml", "vitest.explorer", "ms-vscode.vscode-markdown", "yzhang.markdown-all-in-one", "christian-kohler.path-intellisense", "formulahendry.auto-rename-tag", "ms-vscode.vscode-css-peek"], "unwantedRecommendations": ["ms-vscode.vscode-typescript", "hookyqr.beautify", "ms-vscode.vscode-json-languageservice"]}