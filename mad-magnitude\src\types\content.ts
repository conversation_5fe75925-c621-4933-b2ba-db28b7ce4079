/**
 * 内容类型定义
 */

// 基础内容接口
export interface BaseContent {
  id: string;
  title: {
    zh: string;
    en?: string;
  };
  description: {
    zh: string;
    en?: string;
  };
  publishDate: Date;
  updateDate?: Date;
  draft: boolean;
  featured: boolean;
  tags: string[];
  author: string;
  readingTime?: number;
  relatedContent?: string[];
  summary?: string;
}

// 研究所类型
export type InstituteType = 'economics' | 'philosophy' | 'internet' | 'ai' | 'future';

// 动态资讯类型
export type NewsType = 'research' | 'announcement' | 'reflection' | 'milestone';

// 心情类型
export type MoodType = 'thoughtful' | 'critical' | 'optimistic' | 'analytical';

// 研究所内容接口
export interface InstituteContent extends BaseContent {
  institute: InstituteType;
}

// 经济研究所内容
export interface EconomicsContent extends InstituteContent {
  institute: 'economics';
  analysisType?: 'market' | 'policy' | 'theory' | 'data';
  dataSource?: string;
}

// 哲学研究所内容
export interface PhilosophyContent extends InstituteContent {
  institute: 'philosophy';
  philosophyBranch?: 'ethics' | 'metaphysics' | 'epistemology' | 'logic' | 'aesthetics';
  thinkers?: string[];
}

// 互联网研究所内容
export interface InternetContent extends InstituteContent {
  institute: 'internet';
  industry?: 'social' | 'ecommerce' | 'fintech' | 'education' | 'entertainment';
  companies?: string[];
}

// AI研究所内容
export interface AIContent extends InstituteContent {
  institute: 'ai';
  aiField?: 'ml' | 'nlp' | 'cv' | 'robotics' | 'ethics' | 'agi';
  techStack?: string[];
  models?: string[];
}

// 未来研究所内容
export interface FutureContent extends InstituteContent {
  institute: 'future';
  timeHorizon?: 'short' | 'medium' | 'long';
  domains?: string[];
  confidence?: 'low' | 'medium' | 'high';
}

// 动态资讯接口
export interface NewsContent extends BaseContent {
  type: NewsType;
  relatedInstitute?: InstituteType[];
  category?: 'ai' | 'education' | 'philosophy' | 'technology';
  mood?: MoodType;
  seo?: {
    keywords?: string[];
    canonical?: string;
  };
}

// 研究日志接口
export interface LogContent {
  id: string;
  date: Date;
  title: string;
  content: string;
  tags: string[];
  mood?: MoodType;
  relatedInstitute?: InstituteType[];
  draft: boolean;
  weather?: string;
  location?: string;
}

// 产品内容接口
export interface ProductContent extends BaseContent {
  demo?: string;
}

// 资源内容接口
export interface ResourceContent {
  id: string;
  title: {
    zh: string;
    en?: string;
  };
  description: {
    zh: string;
    en?: string;
  };
  category: 'books' | 'videos' | 'software' | 'datasets' | 'papers';
  items: ResourceItem[];
  updateDate: Date;
}

export interface ResourceItem {
  name: string;
  url?: string;
  description: string;
  tags: string[];
  rating?: number;
  notes?: string;
}

// 内容统计接口
export interface ContentStats {
  totalArticles: number;
  totalLogs: number;
  totalProducts: number;
  recentUpdates: number;
  popularTags: Array<{ tag: string; count: number }>;
  instituteStats: Record<InstituteType, number>;
}

// 研究所信息接口
export interface InstituteInfo {
  id: InstituteType;
  name: {
    zh: string;
    en: string;
  };
  description: {
    zh: string;
    en: string;
  };
  icon: string;
  color: string;
  established: Date;
  focus: string[];
  stats: {
    articles: number;
    lastUpdate: Date;
  };
}

// 搜索结果接口
export interface SearchResult {
  id: string;
  type: 'news' | 'log' | 'institute' | 'project';
  title: string;
  summary: string;
  url: string;
  publishDate: Date;
  tags: string[];
  relevance: number;
}

// 内容关联接口
export interface ContentRelation {
  sourceId: string;
  targetId: string;
  relationType: 'reference' | 'continuation' | 'related' | 'inspiration';
  strength: number; // 关联强度 0-1
}

// 知识图谱节点接口
export interface KnowledgeNode {
  id: string;
  title: string;
  type: 'concept' | 'content' | 'institute' | 'tag';
  size: number; // 节点大小，基于重要性
  connections: string[]; // 连接的其他节点ID
}

// 页面元数据接口
export interface PageMeta {
  title: string;
  description: string;
  keywords?: string[];
  canonical?: string;
  ogImage?: string;
  publishDate?: Date;
  updateDate?: Date;
}
