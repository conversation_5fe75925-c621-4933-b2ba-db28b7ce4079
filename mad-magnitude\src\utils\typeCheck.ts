/**
 * 类型检查工具函数
 * 用于确保组件中的类型安全
 */

// 检查是否为有效日期
export function isValidDate(date: any): date is Date {
  return date instanceof Date && !isNaN(date.getTime());
}

// 安全获取标题
export function getTitle(title: any): string {
  if (typeof title === 'string') {
    return title;
  }
  if (title && typeof title === 'object' && title.zh) {
    return title.zh;
  }
  return '无标题';
}

// 安全获取描述
export function getDescription(description: any, summary?: any): string {
  if (typeof description === 'string') {
    return description;
  }
  if (description && typeof description === 'object' && description.zh) {
    return description.zh;
  }
  if (typeof summary === 'string') {
    return summary;
  }
  return '';
}

// 安全获取标签数组
export function getTags(tags: any): string[] {
  if (Array.isArray(tags)) {
    return tags.filter(tag => typeof tag === 'string');
  }
  return [];
}

// 安全的日期比较
export function compareDates(a: any, b: any): number {
  const dateA = a.data?.publishDate || a.data?.date;
  const dateB = b.data?.publishDate || b.data?.date;

  if (!dateA || !dateB) return 0;

  const timeA = isValidDate(dateA) ? dateA.getTime() : new Date(dateA).getTime();
  const timeB = isValidDate(dateB) ? dateB.getTime() : new Date(dateB).getTime();

  return timeB - timeA;
}
