---
/**
 * 标签云组件
 * 展示热门标签的可视化云图
 */
import { globalTagManager } from '../../utils/tagManager';

export interface Props {
  maxTags?: number;
  showCount?: boolean;
  size?: 'small' | 'medium' | 'large';
  interactive?: boolean;
}

const { maxTags = 30, showCount = true, size = 'medium', interactive = true } = Astro.props;

// 获取标签统计
const tagStats = globalTagManager
  ? await globalTagManager.getTagStats()
  : {
      totalTags: 0,
      totalUniqueContent: 0,
      mostPopularTags: [],
      tagsByCategory: {},
      recentTags: [],
    };
const popularTags = tagStats.mostPopularTags.slice(0, maxTags);

// 计算标签大小权重
const maxCount = Math.max(...popularTags.map(tag => tag.count));
const minCount = Math.min(...popularTags.map(tag => tag.count));

function getTagSize(count: number): string {
  const ratio = (count - minCount) / (maxCount - minCount);
  switch (size) {
    case 'small':
      return `${0.8 + ratio * 0.6}rem`; // 0.8rem - 1.4rem
    case 'large':
      return `${1.2 + ratio * 1.2}rem`; // 1.2rem - 2.4rem
    default: // medium
      return `${1 + ratio * 0.8}rem`; // 1rem - 1.8rem
  }
}

function getTagOpacity(count: number): number {
  const ratio = (count - minCount) / (maxCount - minCount);
  return 0.6 + ratio * 0.4; // 0.6 - 1.0
}
---

<div class="tag-cloud" data-interactive={interactive}>
  <div class="tag-cloud-container">
    {
      popularTags.map(tag => (
        <a
          href={`/tags/${encodeURIComponent(tag.name)}`}
          class="tag-item"
          style={`
          font-size: ${getTagSize(tag.count)};
          opacity: ${getTagOpacity(tag.count)};
          color: ${tag.color};
        `}
          data-count={tag.count}
          data-category={tag.category}
          title={`${tag.name} (${tag.count} 篇内容)`}
        >
          {tag.name}
          {showCount && <span class="tag-count">({tag.count})</span>}
        </a>
      ))
    }
  </div>
</div>

<style>
  .tag-cloud {
    /* 标签云容器样式 */
  }

  /* 响应式调整 */
  @media (max-width: 640px) {
    .tag-cloud .flex {
      gap: 0.375rem; /* 移动端减小间距 */
    }
  }
</style>

<style>
  .tag-cloud {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 0.75rem;
    border: 1px solid #e2e8f0;
  }

  .tag-cloud-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    justify-content: center;
    align-items: center;
    line-height: 1.4;
  }

  .tag-item {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.75rem;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 2rem;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    backdrop-filter: blur(4px);
    white-space: nowrap;
  }

  .tag-item:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    background: rgba(255, 255, 255, 0.95);
    border-color: currentColor;
  }

  .tag-count {
    font-size: 0.8em;
    opacity: 0.7;
    font-weight: 400;
  }

  /* 交互式效果 */
  .tag-cloud[data-interactive='true'] .tag-item {
    cursor: pointer;
  }

  .tag-cloud[data-interactive='true'] .tag-item:hover {
    animation: pulse 0.6s ease-in-out;
  }

  @keyframes pulse {
    0%,
    100% {
      transform: translateY(-2px) scale(1.05);
    }
    50% {
      transform: translateY(-3px) scale(1.08);
    }
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .tag-cloud {
      padding: 0.75rem;
    }

    .tag-cloud-container {
      gap: 0.5rem;
    }

    .tag-item {
      padding: 0.2rem 0.6rem;
      font-size: 0.9em !important;
    }
  }

  /* 分类颜色主题 */
  .tag-item[data-category='technology'] {
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
  }

  .tag-item[data-category='economics'] {
    background: linear-gradient(135deg, #d1fae5, #a7f3d0);
  }

  .tag-item[data-category='philosophy'] {
    background: linear-gradient(135deg, #e9d5ff, #ddd6fe);
  }

  .tag-item[data-category='society'] {
    background: linear-gradient(135deg, #fed7aa, #fdba74);
  }

  .tag-item[data-category='research'] {
    background: linear-gradient(135deg, #fecaca, #fca5a5);
  }

  .tag-item[data-category='tools'] {
    background: linear-gradient(135deg, #e5e7eb, #d1d5db);
  }
</style>
