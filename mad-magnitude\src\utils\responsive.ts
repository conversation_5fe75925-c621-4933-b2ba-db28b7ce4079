/**
 * Responsive utility functions for Pennfly Private Academy
 * Provides helper functions for responsive design and breakpoint management
 */

// Breakpoint definitions matching Tailwind config
export const breakpoints = {
  xs: 475,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
  '3xl': 1920,
} as const;

export type Breakpoint = keyof typeof breakpoints;

/**
 * Get the current breakpoint based on window width
 */
export function getCurrentBreakpoint(): Breakpoint {
  if (typeof window === 'undefined') return 'lg'; // Default for SSR

  const width = window.innerWidth;

  if (width >= breakpoints['3xl']) return '3xl';
  if (width >= breakpoints['2xl']) return '2xl';
  if (width >= breakpoints.xl) return 'xl';
  if (width >= breakpoints.lg) return 'lg';
  if (width >= breakpoints.md) return 'md';
  if (width >= breakpoints.sm) return 'sm';
  return 'xs';
}

/**
 * Check if current viewport matches a breakpoint
 */
export function matchesBreakpoint(breakpoint: Breakpoint): boolean {
  if (typeof window === 'undefined') return false;
  return window.innerWidth >= breakpoints[breakpoint];
}

/**
 * Check if current viewport is mobile (below md breakpoint)
 */
export function isMobile(): boolean {
  return !matchesBreakpoint('md');
}

/**
 * Check if current viewport is tablet (md to lg)
 */
export function isTablet(): boolean {
  return matchesBreakpoint('md') && !matchesBreakpoint('lg');
}

/**
 * Check if current viewport is desktop (lg and above)
 */
export function isDesktop(): boolean {
  return matchesBreakpoint('lg');
}

/**
 * Listen for breakpoint changes
 */
export function onBreakpointChange(callback: (breakpoint: Breakpoint) => void): () => void {
  if (typeof window === 'undefined') return () => {};

  let currentBreakpoint = getCurrentBreakpoint();

  const handleResize = () => {
    const newBreakpoint = getCurrentBreakpoint();
    if (newBreakpoint !== currentBreakpoint) {
      currentBreakpoint = newBreakpoint;
      callback(newBreakpoint);
    }
  };

  // Use ResizeObserver if available, otherwise fall back to resize event
  if (window.ResizeObserver) {
    const resizeObserver = new ResizeObserver(handleResize);
    resizeObserver.observe(document.documentElement);

    return () => resizeObserver.disconnect();
  } else {
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }
}

/**
 * Create a media query for a breakpoint
 */
export function createMediaQuery(breakpoint: Breakpoint, type: 'min' | 'max' = 'min'): string {
  const width = breakpoints[breakpoint];
  return `(${type}-width: ${width}px)`;
}

/**
 * Check if a media query matches
 */
export function matchesMediaQuery(query: string): boolean {
  if (typeof window === 'undefined') return false;
  return window.matchMedia(query).matches;
}

/**
 * Get responsive value based on current breakpoint
 */
export function getResponsiveValue<T>(values: Partial<Record<Breakpoint, T>>, fallback: T): T {
  const currentBp = getCurrentBreakpoint();
  const orderedBreakpoints: Breakpoint[] = ['xs', 'sm', 'md', 'lg', 'xl', '2xl', '3xl'];

  // Find the largest breakpoint that has a value and is <= current breakpoint
  const currentIndex = orderedBreakpoints.indexOf(currentBp);

  for (let i = currentIndex; i >= 0; i--) {
    const bp = orderedBreakpoints[i];
    if (values[bp] !== undefined) {
      return values[bp]!;
    }
  }

  return fallback;
}

/**
 * Generate responsive class names
 */
export function generateResponsiveClasses(
  baseClass: string,
  values: Partial<Record<Breakpoint, string>>
): string {
  const classes = [baseClass];

  Object.entries(values).forEach(([breakpoint, value]) => {
    if (value && breakpoint !== 'xs') {
      classes.push(`${breakpoint}:${value}`);
    } else if (value && breakpoint === 'xs') {
      // For xs, we don't need a prefix
      classes[0] = value;
    }
  });

  return classes.filter(Boolean).join(' ');
}

/**
 * Responsive container sizes
 */
export const containerSizes = {
  xs: '100%',
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
  '3xl': '1920px',
} as const;

/**
 * Get container size for current breakpoint
 */
export function getContainerSize(): string {
  const breakpoint = getCurrentBreakpoint();
  return containerSizes[breakpoint];
}

/**
 * Responsive grid columns
 */
export const gridColumns = {
  xs: 1,
  sm: 2,
  md: 3,
  lg: 4,
  xl: 5,
  '2xl': 6,
  '3xl': 8,
} as const;

/**
 * Get optimal grid columns for current breakpoint
 */
export function getGridColumns(maxColumns: number = 12): number {
  const breakpoint = getCurrentBreakpoint();
  const defaultColumns = gridColumns[breakpoint];
  return Math.min(defaultColumns, maxColumns);
}

/**
 * Device detection utilities
 */
export const deviceUtils = {
  /**
   * Check if device supports touch
   */
  isTouchDevice(): boolean {
    if (typeof window === 'undefined') return false;
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  },

  /**
   * Check if device is in portrait orientation
   */
  isPortrait(): boolean {
    if (typeof window === 'undefined') return true;
    return window.innerHeight > window.innerWidth;
  },

  /**
   * Check if device is in landscape orientation
   */
  isLandscape(): boolean {
    return !this.isPortrait();
  },

  /**
   * Get device pixel ratio
   */
  getPixelRatio(): number {
    if (typeof window === 'undefined') return 1;
    return window.devicePixelRatio || 1;
  },

  /**
   * Check if device has high DPI display
   */
  isHighDPI(): boolean {
    return this.getPixelRatio() > 1;
  },
};

/**
 * Responsive image utilities
 */
export const imageUtils = {
  /**
   * Generate responsive image sizes attribute
   */
  generateSizes(sizes: Partial<Record<Breakpoint, string>>): string {
    const sizeEntries = Object.entries(sizes)
      .map(([bp, size]) => {
        if (bp === 'xs') return size;
        return `(min-width: ${breakpoints[bp as Breakpoint]}px) ${size}`;
      })
      .reverse(); // Largest first for media queries

    return sizeEntries.join(', ');
  },

  /**
   * Get optimal image width for current breakpoint
   */
  getOptimalWidth(widths: Partial<Record<Breakpoint, number>>): number {
    return getResponsiveValue(widths, 800);
  },
};

/**
 * Responsive typography utilities
 */
export const typographyUtils = {
  /**
   * Get responsive font size
   */
  getFontSize(sizes: Partial<Record<Breakpoint, string>>): string {
    return getResponsiveValue(sizes, '1rem');
  },

  /**
   * Generate responsive typography classes
   */
  generateTypographyClasses(
    baseFontSize: string,
    responsiveSizes: Partial<Record<Breakpoint, string>>
  ): string {
    return generateResponsiveClasses(
      `text-${baseFontSize}`,
      Object.fromEntries(Object.entries(responsiveSizes).map(([bp, size]) => [bp, `text-${size}`]))
    );
  },
};

/**
 * Export all utilities as a single object
 */
export const responsive = {
  breakpoints,
  getCurrentBreakpoint,
  matchesBreakpoint,
  isMobile,
  isTablet,
  isDesktop,
  onBreakpointChange,
  createMediaQuery,
  matchesMediaQuery,
  getResponsiveValue,
  generateResponsiveClasses,
  containerSizes,
  getContainerSize,
  gridColumns,
  getGridColumns,
  device: deviceUtils,
  image: imageUtils,
  typography: typographyUtils,
};

export default responsive;
