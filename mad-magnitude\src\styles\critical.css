/**
 * 关键CSS - 首屏渲染必需的样式
 * 内联到HTML中以提升首屏性能
 */

/* 基础重置 */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', system-ui, sans-serif;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  line-height: 1.6;
  background-color: rgb(var(--color-background-primary));
  color: rgb(var(--color-foreground-primary));
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* 关键布局样式 */
.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* 导航栏关键样式 */
.navbar {
  position: sticky;
  top: 0;
  z-index: 50;
  background-color: rgb(var(--color-background-elevated));
  border-bottom: 1px solid rgb(var(--color-border-primary));
}

/* 主要内容区域 */
.main-content {
  min-height: calc(100vh - 4rem);
  padding: 2rem 0;
}

/* 加载状态 */
.loading {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid rgb(var(--color-border-primary));
  border-radius: 50%;
  border-top-color: rgb(var(--color-brand-primary));
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 响应式网格 */
.grid {
  display: grid;
  gap: 1.5rem;
}

.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }

@media (max-width: 767px) {
  .grid-cols-2,
  .grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .grid-cols-3 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

/* 基础按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  font-weight: 500;
  text-decoration: none;
  transition: all var(--transition-fast);
  cursor: pointer;
  border: none;
}

.btn-primary {
  background-color: rgb(var(--color-brand-primary));
  color: rgb(var(--color-foreground-inverse));
}

.btn-primary:hover {
  background-color: rgb(var(--color-primary-700));
}

/* 基础卡片样式 */
.card {
  background-color: rgb(var(--color-background-elevated));
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
  transition: box-shadow var(--transition-fast);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

/* 文本样式 */
.text-primary { color: rgb(var(--color-foreground-primary)); }
.text-secondary { color: rgb(var(--color-foreground-secondary)); }
.text-tertiary { color: rgb(var(--color-foreground-tertiary)); }

/* 标题样式 */
.heading-1 { font-size: var(--font-size-4xl); font-weight: 700; line-height: 1.2; }
.heading-2 { font-size: var(--font-size-3xl); font-weight: 600; line-height: 1.3; }
.heading-3 { font-size: var(--font-size-2xl); font-weight: 600; line-height: 1.4; }

/* 间距工具类 */
.mt-4 { margin-top: var(--spacing-lg); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.p-4 { padding: var(--spacing-lg); }
.px-4 { padding-left: var(--spacing-lg); padding-right: var(--spacing-lg); }
.py-4 { padding-top: var(--spacing-lg); padding-bottom: var(--spacing-lg); }

/* 可访问性 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 焦点样式 */
.focus-visible:focus-visible {
  outline: 2px solid rgb(var(--color-border-focus));
  outline-offset: 2px;
}

/* 跳过链接 */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: rgb(var(--color-background-primary));
  color: rgb(var(--color-foreground-primary));
  padding: 8px;
  text-decoration: none;
  border-radius: var(--radius-md);
  z-index: 100;
}

.skip-link:focus {
  top: 6px;
}
